/**
 * LOTUS GLASS - Shopping Cart Prototype
 * File: shopping-cart-prototype.js
 * 
 * Đây là prototype code cho shopping cart functionality
 * Sẽ được integrate vào blogthemen.xml hiện tại
 */

// ============================================================================
// SHOPPING CART CORE CLASS
// ============================================================================

class LotusCart {
  constructor() {
    this.items = this.loadFromStorage() || [];
    this.init();
  }

  init() {
    this.createCartUI();
    this.attachEventListeners();
    this.updateCartDisplay();
  }

  // ----------------------------------------------------------------------------
  // CART DATA MANAGEMENT
  // ----------------------------------------------------------------------------

  add(sku, productName, variant, price, thumbnail, quantity = 1) {
    const existingItem = this.items.find(item => item.sku === sku);
    
    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      this.items.push({
        sku,
        productName,
        variant,
        price: parseFloat(price),
        thumbnail,
        quantity,
        addedAt: new Date().toISOString()
      });
    }

    this.saveToStorage();
    this.updateCartDisplay();
    this.showCartNotification(`Đã thêm "${productName}" vào giỏ hàng`);
  }

  remove(sku) {
    this.items = this.items.filter(item => item.sku !== sku);
    this.saveToStorage();
    this.updateCartDisplay();
  }

  updateQuantity(sku, newQuantity) {
    const item = this.items.find(item => item.sku === sku);
    if (item) {
      if (newQuantity <= 0) {
        this.remove(sku);
      } else {
        item.quantity = newQuantity;
        this.saveToStorage();
        this.updateCartDisplay();
      }
    }
  }

  clear() {
    this.items = [];
    this.saveToStorage();
    this.updateCartDisplay();
  }

  getItemCount() {
    return this.items.reduce((total, item) => total + item.quantity, 0);
  }

  getSubtotal() {
    return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  // ----------------------------------------------------------------------------
  // LOCAL STORAGE
  // ----------------------------------------------------------------------------

  saveToStorage() {
    localStorage.setItem('lotus-cart', JSON.stringify(this.items));
  }

  loadFromStorage() {
    try {
      const saved = localStorage.getItem('lotus-cart');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Cart load error:', error);
      return [];
    }
  }

  // ----------------------------------------------------------------------------
  // UI CREATION & MANAGEMENT
  // ----------------------------------------------------------------------------

  createCartUI() {
    // Create cart sidebar
    const cartHTML = `
      <div id="cart-sidebar" class="cart-sidebar">
        <div class="cart-header">
          <h3>Giỏ hàng của bạn</h3>
          <button id="cart-close" class="cart-close">&times;</button>
        </div>
        
        <div id="cart-items" class="cart-items">
          <!-- Cart items will be rendered here -->
        </div>
        
        <div class="cart-footer">
          <div class="cart-totals">
            <div class="subtotal">
              Tạm tính: <span id="cart-subtotal">0₫</span>
            </div>
            <div class="shipping-note">
              * Phí vận chuyển sẽ được tính khi thanh toán
            </div>
          </div>
          
          <div class="cart-actions">
            <button id="continue-shopping" class="btn-secondary">
              Tiếp tục mua sắm
            </button>
            <button id="proceed-checkout" class="btn-primary">
              Thanh toán
            </button>
          </div>
        </div>
      </div>
      
      <div id="cart-overlay" class="cart-overlay"></div>
    `;

    // Add to page
    document.body.insertAdjacentHTML('beforeend', cartHTML);

    // Create cart badge
    const cartBadgeHTML = `
      <div id="cart-badge" class="cart-badge">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
        </svg>
        <span id="cart-count" class="cart-count">0</span>
      </div>
    `;

    // Add cart badge to header
    const header = document.querySelector('.custom-header .custom-nav');
    if (header) {
      header.insertAdjacentHTML('afterend', cartBadgeHTML);
    }
  }

  updateCartDisplay() {
    this.updateCartBadge();
    this.updateCartItems();
    this.updateCartTotals();
  }

  updateCartBadge() {
    const badge = document.getElementById('cart-count');
    const count = this.getItemCount();
    
    if (badge) {
      badge.textContent = count;
      badge.classList.toggle('has-items', count > 0);
    }
  }

  updateCartItems() {
    const container = document.getElementById('cart-items');
    if (!container) return;

    if (this.items.length === 0) {
      container.innerHTML = `
        <div class="cart-empty">
          <p>Giỏ hàng trống</p>
          <p>Hãy thêm sản phẩm vào giỏ hàng để tiếp tục</p>
        </div>
      `;
      return;
    }

    container.innerHTML = this.items.map(item => `
      <div class="cart-item" data-sku="${item.sku}">
        <div class="item-image">
          <img src="${item.thumbnail}" alt="${item.productName}" loading="lazy">
        </div>
        
        <div class="item-details">
          <h4 class="item-name">${item.productName}</h4>
          <p class="item-variant">${item.variant}</p>
          <p class="item-price">${this.formatPrice(item.price)}</p>
        </div>
        
        <div class="item-controls">
          <div class="quantity-controls">
            <button class="qty-btn minus" data-sku="${item.sku}">-</button>
            <input type="number" class="qty-input" value="${item.quantity}" 
                   min="1" max="999" data-sku="${item.sku}">
            <button class="qty-btn plus" data-sku="${item.sku}">+</button>
          </div>
          
          <div class="item-total">
            ${this.formatPrice(item.price * item.quantity)}
          </div>
          
          <button class="remove-item" data-sku="${item.sku}" title="Xóa khỏi giỏ hàng">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/>
            </svg>
          </button>
        </div>
      </div>
    `).join('');
  }

  updateCartTotals() {
    const subtotalElement = document.getElementById('cart-subtotal');
    if (subtotalElement) {
      subtotalElement.textContent = this.formatPrice(this.getSubtotal());
    }

    // Enable/disable checkout button
    const checkoutBtn = document.getElementById('proceed-checkout');
    if (checkoutBtn) {
      checkoutBtn.disabled = this.items.length === 0;
    }
  }

  // ----------------------------------------------------------------------------
  // EVENT LISTENERS
  // ----------------------------------------------------------------------------

  attachEventListeners() {
    // Cart badge click - open cart
    document.addEventListener('click', (e) => {
      if (e.target.closest('#cart-badge')) {
        this.openCart();
      }
    });

    // Cart close
    document.addEventListener('click', (e) => {
      if (e.target.id === 'cart-close' || e.target.id === 'cart-overlay') {
        this.closeCart();
      }
    });

    // Add to cart buttons
    document.addEventListener('click', (e) => {
      if (e.target.closest('.add-to-cart-btn')) {
        e.preventDefault();
        this.handleAddToCart(e.target.closest('.add-to-cart-btn'));
      }
    });

    // Quantity controls
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('qty-btn')) {
        this.handleQuantityChange(e.target);
      }
    });

    document.addEventListener('input', (e) => {
      if (e.target.classList.contains('qty-input')) {
        this.handleQuantityInput(e.target);
      }
    });

    // Remove item
    document.addEventListener('click', (e) => {
      if (e.target.closest('.remove-item')) {
        const sku = e.target.closest('.remove-item').dataset.sku;
        this.remove(sku);
      }
    });

    // Continue shopping
    document.addEventListener('click', (e) => {
      if (e.target.id === 'continue-shopping') {
        this.closeCart();
      }
    });

    // Proceed to checkout
    document.addEventListener('click', (e) => {
      if (e.target.id === 'proceed-checkout') {
        this.proceedToCheckout();
      }
    });
  }

  handleAddToCart(button) {
    const productCard = button.closest('.product-card');
    if (!productCard) return;

    // Extract product data from the product card
    const productName = productCard.querySelector('.product-name')?.textContent?.trim();
    const priceText = productCard.querySelector('.product-price')?.textContent?.trim();
    const thumbnail = productCard.querySelector('img')?.src;

    // Parse price (remove currency and formatting)
    const price = this.parsePrice(priceText);
    
    // For now, use product name as SKU (in real implementation, this would come from data attributes)
    const sku = productName?.replace(/\s+/g, '-').toLowerCase();
    
    if (productName && price && sku) {
      this.add(sku, productName, 'Mặc định', price, thumbnail, 1);
    }
  }

  handleQuantityChange(button) {
    const sku = button.dataset.sku;
    const input = button.parentElement.querySelector('.qty-input');
    const currentQty = parseInt(input.value) || 1;

    if (button.classList.contains('minus') && currentQty > 1) {
      this.updateQuantity(sku, currentQty - 1);
    } else if (button.classList.contains('plus')) {
      this.updateQuantity(sku, currentQty + 1);
    }
  }

  handleQuantityInput(input) {
    const sku = input.dataset.sku;
    const newQty = parseInt(input.value) || 1;
    
    if (newQty >= 1) {
      this.updateQuantity(sku, newQty);
    }
  }

  // ----------------------------------------------------------------------------
  // CART UI ACTIONS
  // ----------------------------------------------------------------------------

  openCart() {
    const sidebar = document.getElementById('cart-sidebar');
    const overlay = document.getElementById('cart-overlay');
    
    if (sidebar && overlay) {
      sidebar.classList.add('active');
      overlay.classList.add('active');
      document.body.classList.add('cart-open');
    }
  }

  closeCart() {
    const sidebar = document.getElementById('cart-sidebar');
    const overlay = document.getElementById('cart-overlay');
    
    if (sidebar && overlay) {
      sidebar.classList.remove('active');
      overlay.classList.remove('active');
      document.body.classList.remove('cart-open');
    }
  }

  showCartNotification(message) {
    // Create notification
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"/>
        </svg>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto remove
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // ----------------------------------------------------------------------------
  // CHECKOUT INTEGRATION
  // ----------------------------------------------------------------------------

  async proceedToCheckout() {
    if (this.items.length === 0) {
      alert('Giỏ hàng trống');
      return;
    }

    try {
      // Validate cart items with server
      const validationResult = await this.validateCartWithServer();
      
      if (!validationResult.success) {
        alert('Có lỗi với một số sản phẩm trong giỏ hàng: ' + validationResult.message);
        return;
      }

      // Open checkout modal
      this.openCheckoutModal();
      
    } catch (error) {
      console.error('Checkout error:', error);
      alert('Có lỗi xảy ra khi kiểm tra giỏ hàng. Vui lòng thử lại.');
    }
  }

  async validateCartWithServer() {
    try {
      const response = await fetch(window.THEME_CONFIG.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'validateCart',
          cartData: JSON.stringify(this.items)
        })
      });

      const result = await response.json();
      return result;
      
    } catch (error) {
      console.error('Cart validation error:', error);
      return { success: false, message: 'Không thể kết nối server' };
    }
  }

  openCheckoutModal() {
    // This will be implemented in the checkout module
    // For now, just show an alert
    alert('Tính năng thanh toán sẽ được triển khai trong Phase 1. Vui lòng liên hệ 0981 500 400 để đặt hàng.');
  }

  // ----------------------------------------------------------------------------
  // UTILITY METHODS
  // ----------------------------------------------------------------------------

  formatPrice(price) {
    if (typeof price !== 'number' || isNaN(price)) return 'Liên hệ';
    return price.toLocaleString('vi-VN') + '₫';
  }

  parsePrice(priceText) {
    if (!priceText) return 0;
    
    // Remove currency symbols and whitespace, keep only numbers
    const cleaned = priceText.replace(/[^\d]/g, '');
    return parseInt(cleaned) || 0;
  }
}

// ============================================================================
// CART STYLES (CSS)
// ============================================================================

const cartStyles = `
/* Cart Badge */
.cart-badge {
  position: relative;
  cursor: pointer;
  padding: 8px;
  color: var(--brand-color);
  transition: color 0.2s ease;
}

.cart-badge:hover {
  color: var(--brand-color-dark);
}

.cart-count {
  position: absolute;
  top: 0;
  right: 0;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transform: scale(0);
  transition: transform 0.2s ease;
}

.cart-count.has-items {
  transform: scale(1);
}

/* Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.active {
  right: 0;
}

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

body.cart-open {
  overflow: hidden;
}

/* Cart Header */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.cart-header h3 {
  margin: 0;
  color: var(--brand-color);
}

.cart-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-close:hover {
  color: #333;
}

/* Cart Items */
.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.cart-empty {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.cart-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
}

.item-variant {
  margin: 0 0 5px 0;
  font-size: 12px;
  color: #666;
}

.item-price {
  margin: 0;
  font-size: 14px;
  color: var(--brand-color);
  font-weight: 500;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qty-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 14px;
}

.qty-btn:hover {
  background: #f5f5f5;
}

.qty-input {
  width: 40px;
  height: 24px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.item-total {
  font-size: 14px;
  font-weight: 600;
  color: var(--brand-color);
}

.remove-item {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
}

.remove-item:hover {
  color: #dc3545;
}

/* Cart Footer */
.cart-footer {
  border-top: 1px solid #eee;
  padding: 20px;
}

.cart-totals {
  margin-bottom: 20px;
}

.subtotal {
  font-size: 18px;
  font-weight: 600;
  color: var(--brand-color);
  margin-bottom: 8px;
}

.shipping-note {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-primary, .btn-secondary {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--brand-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--brand-color-dark);
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: white;
  color: var(--brand-color);
  border: 1px solid var(--brand-color);
}

.btn-secondary:hover {
  background: var(--brand-color);
  color: white;
}

/* Cart Notification */
.cart-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--brand-color);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  max-width: 300px;
}

.cart-notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-content svg {
  flex-shrink: 0;
}

/* Add to Cart Buttons */
.add-to-cart-btn {
  background: var(--brand-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-top: 10px;
}

.add-to-cart-btn:hover {
  background: var(--brand-color-dark);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
    right: -100vw;
  }
  
  .cart-sidebar.active {
    right: 0;
  }
}
`;

// ============================================================================
// INITIALIZATION
// ============================================================================

// Add styles to page
function addCartStyles() {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = cartStyles;
  document.head.appendChild(styleSheet);
}

// Add "Add to Cart" buttons to existing product cards
function addCartButtons() {
  const productCards = document.querySelectorAll('.product-card');
  
  productCards.forEach(card => {
    // Check if button already exists
    if (card.querySelector('.add-to-cart-btn')) return;
    
    const infoSection = card.querySelector('.info');
    if (infoSection) {
      const addButton = document.createElement('button');
      addButton.className = 'add-to-cart-btn';
      addButton.textContent = 'Thêm vào giỏ';
      addButton.type = 'button';
      
      infoSection.appendChild(addButton);
    }
  });
}

// Initialize cart when DOM is ready
function initLotusCart() {
  if (typeof window.lotusCart !== 'undefined') return; // Already initialized
  
  addCartStyles();
  window.lotusCart = new LotusCart();
  
  // Add cart buttons to existing products
  addCartButtons();
  
  // Re-add buttons when new products are loaded (for infinite scroll)
  const observer = new MutationObserver(() => {
    addCartButtons();
  });
  
  const productsContainer = document.getElementById('productsList');
  if (productsContainer) {
    observer.observe(productsContainer, { childList: true });
  }
}

// Auto-initialize if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initLotusCart);
} else {
  initLotusCart();
}

// ============================================================================
// EXPORT FOR TESTING/DEVELOPMENT
// ============================================================================

// Make cart available globally for testing
window.LotusCart = LotusCart;

// Export methods for external use
window.cartAPI = {
  add: (sku, name, variant, price, thumbnail, qty) => {
    if (window.lotusCart) {
      window.lotusCart.add(sku, name, variant, price, thumbnail, qty);
    }
  },
  
  remove: (sku) => {
    if (window.lotusCart) {
      window.lotusCart.remove(sku);
    }
  },
  
  clear: () => {
    if (window.lotusCart) {
      window.lotusCart.clear();
    }
  },
  
  getItems: () => {
    return window.lotusCart ? window.lotusCart.items : [];
  },
  
  getCount: () => {
    return window.lotusCart ? window.lotusCart.getItemCount() : 0;
  },
  
  getTotal: () => {
    return window.lotusCart ? window.lotusCart.getSubtotal() : 0;
  }
};

console.log('✅ Lotus Glass Shopping Cart loaded successfully!');
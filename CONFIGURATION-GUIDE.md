# 🔧 LOTUS GLASS - CONFIGURATION GUIDE

## 📋 OVERVIEW
Hướng dẫn cấu hình hoàn chỉnh cho Lotus Glass e-commerce system sau khi triển khai Phase 1 và Phase 2.

---

## 🚀 STEP 1: GOOGLE APPS SCRIPT SETUP

### 1.1 Deploy Apps Script
1. **Mở Google Apps Script**: https://script.google.com
2. **Tạo project mới** hoặc mở project hiện có
3. **Copy toàn bộ nội dung** từ `code.gs` vào Apps Script editor
4. **Save project** (Ctrl+S)
5. **Deploy as web app**:
   - Click **Deploy** → **New deployment**
   - Type: **Web app**
   - Execute as: **Me**
   - Who has access: **Anyone**
   - Click **Deploy**
6. **Copy Web App URL** (dạng: `https://script.google.com/macros/s/SCRIPT_ID/exec`)

### 1.2 Setup Google Sheets Database
1. **Tạo Google Sheets mới** hoặc sử dụng existing
2. **Copy Spreadsheet ID** từ URL (phần giữa `/d/` và `/edit`)
3. **Update SPREADSHEET_ID** trong code.gs:
```javascript
const SPREADSHEET_ID = 'YOUR_ACTUAL_SPREADSHEET_ID';
```
4. **Tạo các sheets** (sẽ tự động tạo khi có order đầu tiên):
   - `Orders` - Thông tin đơn hàng
   - `OrderDetails` - Chi tiết sản phẩm trong đơn hàng  
   - `Customers` - Thông tin khách hàng

---

## 🌐 STEP 2: FRONTEND CONFIGURATION

### 2.1 Update API URL
Trong file `blogthemen.xml`, tìm và cập nhật:

```javascript
const LOTUS_CONFIG = {
  // Google Apps Script API URL
  API_BASE_URL: 'https://script.google.com/macros/s/YOUR_ACTUAL_SCRIPT_ID/exec',
  // ... other config
};
```

**Thay `YOUR_ACTUAL_SCRIPT_ID`** bằng Script ID thực tế từ Step 1.1

### 2.2 Email Configuration
Trong `code.gs`, cập nhật email settings:

```javascript
EMAIL: {
  FROM_NAME: 'Lotus Glass Vietnam',
  FROM_EMAIL: '<EMAIL>',
  REPLY_TO: '<EMAIL>',  // ← Cập nhật email thực
  BCC: '<EMAIL>'              // ← Email nhận copy đơn hàng
}
```

---

## 💳 STEP 3: PAYMENT GATEWAY SETUP

### 3.1 VNPay Configuration (Production)
1. **Đăng ký VNPay merchant account**: https://vnpay.vn
2. **Lấy thông tin từ VNPay dashboard**:
   - TMN_CODE (Mã website)
   - HASH_SECRET (Chuỗi bí mật)
3. **Cập nhật trong code.gs**:

```javascript
VNPAY: {
  TMN_CODE: 'YOUR_ACTUAL_TMN_CODE',           // ← Từ VNPay dashboard
  HASH_SECRET: 'YOUR_ACTUAL_HASH_SECRET',     // ← Từ VNPay dashboard
  URL: 'https://pay.vnpay.vn/vpcpay.html',    // Production URL
  RETURN_URL: 'https://lotusglassvietnam.blogspot.com/payment-return'
}
```

### 3.2 Bank Transfer Information
Cập nhật thông tin chuyển khoản trong checkout HTML:

```html
<p><strong>Ngân hàng:</strong> Vietcombank</p>
<p><strong>Số tài khoản:</strong> YOUR_ACTUAL_ACCOUNT_NUMBER</p>
<p><strong>Chủ tài khoản:</strong> YOUR_ACTUAL_ACCOUNT_NAME</p>
```

---

## 🧪 STEP 4: TESTING

### 4.1 Backend Testing
1. **Mở Apps Script editor**
2. **Run function `testOrderCreation()`**
3. **Check execution log** - không có errors
4. **Kiểm tra Google Sheets** - test data xuất hiện

### 4.2 Frontend Testing
1. **Mở website Lotus Glass**
2. **Thêm sản phẩm vào cart**
3. **Click "Thanh Toán"** → Checkout modal hiện
4. **Fill form với thông tin test**
5. **Submit order** → Kiểm tra response

### 4.3 Email Testing
```javascript
// Run trong Apps Script để test email:
function testEmail() {
  GmailApp.sendEmail('<EMAIL>', 'Test Email', 'Test message');
}
```

### 4.4 Integration Testing
1. **Complete checkout flow** từ đầu đến cuối
2. **Kiểm tra order trong Google Sheets**
3. **Verify email delivery**
4. **Test trên mobile device**

---

## 🔒 STEP 5: SECURITY & PERMISSIONS

### 5.1 Apps Script Permissions
- **Gmail API**: Để gửi email confirmation
- **Sheets API**: Để lưu order data
- **External requests**: Để nhận webhook từ VNPay

### 5.2 Spreadsheet Permissions
- **Apps Script có quyền edit** spreadsheet
- **Backup spreadsheet** thường xuyên
- **Restrict access** chỉ cho authorized users

---

## 📊 STEP 6: MONITORING & MAINTENANCE

### 6.1 Daily Monitoring
- [ ] Check order creation rates
- [ ] Monitor email delivery
- [ ] Verify payment processing
- [ ] Review error logs

### 6.2 Weekly Review
- [ ] Analyze conversion metrics
- [ ] Check system performance
- [ ] Update inventory if needed
- [ ] Review customer feedback

### 6.3 Monthly Optimization
- [ ] Performance optimization
- [ ] A/B test checkout flow
- [ ] Update email templates
- [ ] Plan new features

---

## 🚨 TROUBLESHOOTING

### Common Issues & Solutions

#### 1. Checkout Modal Won't Open
```javascript
// Debug in browser console:
console.log('LotusCheckout available:', window.LotusCheckout);
console.log('Cart items:', window.LotusCart.items);
```

#### 2. Order Creation Fails
- Check Apps Script execution logs
- Verify API URL is correct
- Test doPost function manually
- Check spreadsheet permissions

#### 3. Email Not Sending
- Verify Gmail API permissions
- Check email quota limits
- Test with simple email first

#### 4. VNPay Integration Issues
- Verify TMN_CODE and HASH_SECRET
- Check return URL configuration
- Test with small amounts first

---

## 📞 SUPPORT

### If You Need Help
1. **Check browser console** for JavaScript errors
2. **Review Apps Script logs** for backend errors
3. **Test individual components** separately
4. **Contact technical support** with specific error details

### Success Metrics
- **Order Creation Success**: >95%
- **Email Delivery Rate**: >98%
- **Payment Success Rate**: >85%
- **Page Load Speed**: <3 seconds

---

**🎯 Ready to Go Live!** 

Sau khi hoàn thành tất cả steps trên, Lotus Glass sẽ có:
- ✅ **Complete e-commerce functionality**
- ✅ **Professional checkout experience**
- ✅ **Automated order processing**
- ✅ **Multiple payment methods**
- ✅ **Email automation system**

**Expected Results**: 5-12% conversion rate và automated operations!

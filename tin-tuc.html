<!-- Page: <PERSON> tức -->
<!--
  This news page hides the product-related UI and fetches the latest blog posts via the blog's JSON feed.
  It displays a list of recent posts with titles, publication dates and excerpts.  
-->
<style>
  /* Hide the template's hero, search and product listing */
  .hero-section,
  .search-bar,
  .filters-toolbar,
  #productsContainer {
    display: none !important;
  }
  .news-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }
  .news-section h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
    text-align: center;
  }
  .news-item {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
  }
  .news-item h2 {
    font-size: 1.4rem;
    margin: 0 0 .5rem 0;
    color: #212529;
  }
  .news-item small {
    display: block;
    color: #6c757d;
    margin-bottom: 0.5rem;
  }
  .news-item p {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }
  .news-item a {
    color: var(--brand-color);
    text-decoration: none;
  }
  .news-item a:hover {
    text-decoration: underline;
    color: var(--brand-color-dark);
  }
</style>

<section class="news-section">
  <h1>Tin&nbsp;tức</h1>
  <p><PERSON><PERSON> đ<PERSON>c những tin tức và bài viết mới nhất về sản phẩm, xu hướng và hoạt động của Lotus&nbsp;Glass.</p>
  <div id="blogPostList"></div>
</section>

<script>
async function fetchBlogPosts() {
  try {
    // Fetch the JSON feed of recent posts from the current blog.  
    const feedUrl = '/feeds/posts/default?alt=json&max-results=10';
    const response = await fetch(feedUrl);
    const data = await response.json();
    const posts = data.feed.entry || [];
    const container = document.getElementById('blogPostList');
    container.innerHTML = '';
    posts.forEach(entry => {
      const title = entry.title.$t;
      const link = entry.link.find(l => l.rel === 'alternate').href;
      const datePublished = entry.published.$t.substring(0, 10);
      const summary = entry.summary ? entry.summary.$t.replace(/<[^>]*>/g, '') : '';
      const excerpt = summary.length > 200 ? summary.substring(0, 200) + '…' : summary;
      const article = document.createElement('article');
      article.className = 'news-item';
      article.innerHTML = `
        <h2><a href="${link}">${title}</a></h2>
        <small>${datePublished}</small>
        <p>${excerpt}</p>
        <a href="${link}">Đọc tiếp</a>
      `;
      container.appendChild(article);
    });
  } catch (error) {
    document.getElementById('blogPostList').innerHTML = '<p>Không thể tải tin tức. Vui lòng thử lại sau.</p>';
  }
}
document.addEventListener('DOMContentLoaded', fetchBlogPosts);
</script>
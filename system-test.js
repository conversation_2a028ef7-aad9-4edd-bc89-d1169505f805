/**
 * ============================================================================
 * LOTUS GLASS - SYSTEM TEST SCRIPT
 * ============================================================================
 * 
 * Chạy script này trong browser console để test toàn bộ hệ thống
 */

// ============================================================================
// TEST CONFIGURATION
// ============================================================================

const TEST_CONFIG = {
  // Test data
  testProduct: {
    sku: 'TEST001',
    name: 'Bình Thủy Tinh Test',
    variant: '1L',
    price: 250000,
    image: 'https://via.placeholder.com/200x200'
  },
  
  testCustomer: {
    name: 'Nguyễn Văn Test',
    phone: '0123456789',
    email: '<EMAIL>',
    address: '123 Đường Test',
    ward: 'Phường Test',
    district: 'Quận Test',
    city: 'hanoi'
  },
  
  // Test timeouts
  timeouts: {
    cartOperation: 2000,
    checkoutStep: 3000,
    orderSubmission: 10000
  }
};

// ============================================================================
// TEST UTILITIES
// ============================================================================

class LotusSystemTest {
  constructor() {
    this.results = [];
    this.currentTest = null;
  }
  
  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Lotus Glass System Tests...\n');
    
    try {
      await this.testCartSystem();
      await this.testCheckoutSystem();
      await this.testIntegration();
      
      this.showResults();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.addResult('SYSTEM', 'Test Suite', false, error.message);
    }
  }
  
  /**
   * Test cart system
   */
  async testCartSystem() {
    console.log('📦 Testing Cart System...');
    
    // Test 1: Cart initialization
    this.addResult('CART', 'Initialization', 
      window.LotusCart && typeof window.LotusCart.init === 'function',
      'LotusCart object available'
    );
    
    // Test 2: Add to cart
    const addResult = window.LotusCart.addToCart(
      TEST_CONFIG.testProduct.sku,
      TEST_CONFIG.testProduct.name,
      TEST_CONFIG.testProduct.variant,
      TEST_CONFIG.testProduct.price,
      TEST_CONFIG.testProduct.image,
      2
    );
    
    this.addResult('CART', 'Add to Cart', addResult, 'Product added successfully');
    
    // Test 3: Cart count update
    await this.wait(1000);
    const cartCount = document.getElementById('cart-count')?.textContent;
    this.addResult('CART', 'Count Update', cartCount === '2', `Cart count: ${cartCount}`);
    
    // Test 4: Open cart
    window.LotusCart.openCart();
    await this.wait(500);
    const cartOpen = document.getElementById('cart-sidebar')?.classList.contains('active');
    this.addResult('CART', 'Open Cart', cartOpen, 'Cart sidebar opened');
    
    // Test 5: Cart items display
    const cartItems = document.querySelectorAll('.cart-item').length;
    this.addResult('CART', 'Items Display', cartItems > 0, `${cartItems} items displayed`);
    
    console.log('✅ Cart system tests completed\n');
  }
  
  /**
   * Test checkout system
   */
  async testCheckoutSystem() {
    console.log('💳 Testing Checkout System...');
    
    // Test 1: Checkout initialization
    this.addResult('CHECKOUT', 'Initialization',
      window.LotusCheckout && typeof window.LotusCheckout.open === 'function',
      'LotusCheckout object available'
    );
    
    // Test 2: Open checkout
    window.LotusCheckout.open();
    await this.wait(500);
    const checkoutOpen = document.getElementById('checkout-modal')?.classList.contains('active');
    this.addResult('CHECKOUT', 'Open Modal', checkoutOpen, 'Checkout modal opened');
    
    // Test 3: Form fields
    const requiredFields = [
      'customer-name', 'customer-phone', 'shipping-address',
      'shipping-ward', 'shipping-district', 'shipping-city'
    ];
    
    const fieldsExist = requiredFields.every(id => document.getElementById(id));
    this.addResult('CHECKOUT', 'Form Fields', fieldsExist, 'All required fields present');
    
    // Test 4: Fill form
    this.fillTestForm();
    await this.wait(1000);
    
    const formFilled = document.getElementById('customer-name').value === TEST_CONFIG.testCustomer.name;
    this.addResult('CHECKOUT', 'Form Fill', formFilled, 'Form filled with test data');
    
    // Test 5: Payment methods
    const paymentMethods = document.querySelectorAll('.payment-method').length;
    this.addResult('CHECKOUT', 'Payment Methods', paymentMethods >= 3, `${paymentMethods} payment methods available`);
    
    console.log('✅ Checkout system tests completed\n');
  }
  
  /**
   * Test integration
   */
  async testIntegration() {
    console.log('🔗 Testing Integration...');
    
    // Test 1: API URL configuration
    const apiUrl = window.LOTUS_CONFIG?.API_BASE_URL;
    const apiConfigured = apiUrl && !apiUrl.includes('YOUR_SCRIPT_ID');
    this.addResult('INTEGRATION', 'API Configuration', apiConfigured, 
      apiConfigured ? 'API URL configured' : 'API URL needs configuration');
    
    // Test 2: Order summary
    const orderSummary = document.getElementById('checkout-items')?.innerHTML;
    const summaryPopulated = orderSummary && orderSummary.includes(TEST_CONFIG.testProduct.name);
    this.addResult('INTEGRATION', 'Order Summary', summaryPopulated, 'Order summary populated');
    
    // Test 3: Total calculation
    const totalElement = document.getElementById('checkout-total');
    const totalCalculated = totalElement && totalElement.textContent.includes('₫');
    this.addResult('INTEGRATION', 'Total Calculation', totalCalculated, 'Total calculated correctly');
    
    // Test 4: Local storage
    const cartData = localStorage.getItem('lotus_cart_items');
    const storageWorking = cartData && JSON.parse(cartData).items?.length > 0;
    this.addResult('INTEGRATION', 'Local Storage', storageWorking, 'Cart data persisted');
    
    console.log('✅ Integration tests completed\n');
  }
  
  /**
   * Fill form with test data
   */
  fillTestForm() {
    const fields = {
      'customer-name': TEST_CONFIG.testCustomer.name,
      'customer-phone': TEST_CONFIG.testCustomer.phone,
      'customer-email': TEST_CONFIG.testCustomer.email,
      'shipping-address': TEST_CONFIG.testCustomer.address,
      'shipping-ward': TEST_CONFIG.testCustomer.ward,
      'shipping-district': TEST_CONFIG.testCustomer.district,
      'shipping-city': TEST_CONFIG.testCustomer.city
    };
    
    Object.keys(fields).forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.value = fields[id];
        element.dispatchEvent(new Event('input', { bubbles: true }));
      }
    });
  }
  
  /**
   * Add test result
   */
  addResult(category, test, passed, message) {
    this.results.push({
      category,
      test,
      passed,
      message,
      timestamp: new Date()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${category}: ${test} - ${message}`);
  }
  
  /**
   * Show test results summary
   */
  showResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    
    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      const passed = categoryResults.filter(r => r.passed).length;
      const total = categoryResults.length;
      const percentage = Math.round((passed / total) * 100);
      
      console.log(`\n${category}: ${passed}/${total} (${percentage}%)`);
      
      categoryResults.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`  ${status} ${result.test}: ${result.message}`);
      });
    });
    
    const totalPassed = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    const overallPercentage = Math.round((totalPassed / totalTests) * 100);
    
    console.log(`\n🎯 OVERALL: ${totalPassed}/${totalTests} (${overallPercentage}%)`);
    
    if (overallPercentage >= 80) {
      console.log('🎉 System is ready for production!');
    } else if (overallPercentage >= 60) {
      console.log('⚠️ System needs some fixes before production');
    } else {
      console.log('🚨 System has critical issues that need to be resolved');
    }
  }
  
  /**
   * Wait utility
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Clean up after tests
   */
  cleanup() {
    // Close modals
    if (window.LotusCheckout) {
      window.LotusCheckout.close();
    }
    
    if (window.LotusCart) {
      window.LotusCart.closeCart();
      // Optionally clear test cart
      // window.LotusCart.clearCart();
    }
  }
}

// ============================================================================
// QUICK TEST FUNCTIONS
// ============================================================================

/**
 * Quick cart test
 */
function quickCartTest() {
  console.log('🚀 Quick Cart Test');
  
  if (!window.LotusCart) {
    console.error('❌ LotusCart not available');
    return;
  }
  
  // Add test product
  const result = window.LotusCart.addToCart('QUICK001', 'Quick Test Product', '', 100000, '', 1);
  console.log(result ? '✅ Cart working' : '❌ Cart failed');
  
  // Open cart
  window.LotusCart.openCart();
  console.log('✅ Cart opened');
}

/**
 * Quick checkout test
 */
function quickCheckoutTest() {
  console.log('🚀 Quick Checkout Test');
  
  if (!window.LotusCheckout) {
    console.error('❌ LotusCheckout not available');
    return;
  }
  
  // Ensure cart has items
  if (!window.LotusCart || window.LotusCart.items.length === 0) {
    quickCartTest();
  }
  
  // Open checkout
  window.LotusCheckout.open();
  console.log('✅ Checkout opened');
}

// ============================================================================
// AUTO-RUN INSTRUCTIONS
// ============================================================================

console.log(`
🧪 LOTUS GLASS SYSTEM TEST SCRIPT LOADED

Available commands:
- new LotusSystemTest().runAllTests()  // Run complete test suite
- quickCartTest()                      // Quick cart functionality test  
- quickCheckoutTest()                  // Quick checkout functionality test

Example usage:
const tester = new LotusSystemTest();
tester.runAllTests();
`);

/**
 * ============================================================================
 * LOTUS GLASS - COMPLETE SHOPPING CART JAVASCRIPT
 * File: lotus-cart-complete.js
 * 
 * Thêm vào cuối blogthemen.xml, trước </body>
 * ============================================================================
 */

// ============================================================================
// GLOBAL CONFIGURATION
// ============================================================================

const LOTUS_CONFIG = {
  // Google Apps Script API URL
  API_BASE_URL: 'https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec',
  
  // Currency settings
  CURRENCY: 'VND',
  CURRENCY_SYMBOL: '₫',
  
  // Cart settings
  MAX_QUANTITY: 999,
  MIN_QUANTITY: 1,
  
  // Local storage keys
  STORAGE_KEYS: {
    CART: 'lotus_cart_items',
    CUSTOMER: 'lotus_customer_info',
    LAST_VISIT: 'lotus_last_visit'
  },
  
  // Animation durations
  ANIMATION: {
    NOTIFICATION: 3000,
    CART_SLIDE: 400,
    BUTTON_FEEDBACK: 200
  }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const LotusUtils = {
  /**
   * Format price to Vietnamese currency
   */
  formatPrice(amount) {
    if (!amount || isNaN(amount)) return '0₫';
    return new Intl.NumberFormat('vi-VN').format(amount) + '₫';
  },
  
  /**
   * Debounce function calls
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
  
  /**
   * Generate unique ID
   */
  generateId() {
    return 'lotus_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  },
  
  /**
   * Sanitize HTML to prevent XSS
   */
  sanitizeHtml(str) {
    const temp = document.createElement('div');
    temp.textContent = str;
    return temp.innerHTML;
  },
  
  /**
   * Check if element is in viewport
   */
  isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }
};

// ============================================================================
// LOTUS CART MAIN CLASS
// ============================================================================

class LotusCart {
  constructor() {
    this.items = [];
    this.isOpen = false;
    this.isInitialized = false;
    
    // Bind methods to preserve context
    this.addToCart = this.addToCart.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.updateQuantity = this.updateQuantity.bind(this);
    this.setQuantity = this.setQuantity.bind(this);
    this.openCart = this.openCart.bind(this);
    this.closeCart = this.closeCart.bind(this);
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init());
    } else {
      this.init();
    }
  }
  
  // --------------------------------------------------------------------------
  // INITIALIZATION
  // --------------------------------------------------------------------------
  
  init() {
    try {
      this.loadFromStorage();
      this.setupEventListeners();
      this.updateCartDisplay();
      this.setupAddToCartButtons();
      this.isInitialized = true;
      
      console.log('🛒 Lotus Cart initialized successfully');
    } catch (error) {
      console.error('❌ Cart initialization failed:', error);
    }
  }
  
  setupEventListeners() {
    // ESC key to close cart
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeCart();
      }
    });
    
    // Handle page unload
    window.addEventListener('beforeunload', () => {
      this.saveToStorage();
    });
    
    // Auto-save every 30 seconds
    setInterval(() => {
      if (this.items.length > 0) {
        this.saveToStorage();
      }
    }, 30000);
  }
  
  setupAddToCartButtons() {
    // Setup existing add to cart buttons
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
      if (!button.hasAttribute('data-lotus-setup')) {
        this.setupAddToCartButton(button);
        button.setAttribute('data-lotus-setup', 'true');
      }
    });
    
    // Setup observer for dynamically added buttons
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const buttons = node.querySelectorAll ? 
              node.querySelectorAll('.add-to-cart-btn') : 
              [];
            buttons.forEach(button => {
              if (!button.hasAttribute('data-lotus-setup')) {
                this.setupAddToCartButton(button);
                button.setAttribute('data-lotus-setup', 'true');
              }
            });
          }
        });
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  setupAddToCartButton(button) {
    const productCard = button.closest('.product-card, .post, .item');
    if (!productCard) return;
    
    // Extract product data from card
    const sku = button.dataset.sku || productCard.dataset.sku || this.extractSKU(productCard);
    const name = this.extractProductName(productCard);
    const variant = this.extractVariant(productCard);
    const price = this.extractPrice(productCard);
    const image = this.extractImage(productCard);
    
    // Set up click handler
    button.addEventListener('click', (e) => {
      e.preventDefault();
      
      if (sku && name && price) {
        this.addToCart(sku, name, variant, price, image);
      } else {
        console.warn('Missing product data for add to cart:', { sku, name, variant, price });
        this.showNotification('Không thể thêm sản phẩm vào giỏ hàng', 'error');
      }
    });
  }
  
  // --------------------------------------------------------------------------
  // DATA EXTRACTION FROM PRODUCT CARDS
  // --------------------------------------------------------------------------
  
  extractSKU(productCard) {
    // Try multiple methods to extract SKU
    const link = productCard.querySelector('a[href]');
    if (link) {
      const urlMatch = link.href.match(/\/([A-Z0-9]+)\.html/);
      if (urlMatch) return urlMatch[1];
    }
    
    const codeElement = productCard.querySelector('.product-code, .item-code, .sku');
    if (codeElement) return codeElement.textContent.trim();
    
    return 'LOTUS_' + Date.now();
  }
  
  extractProductName(productCard) {
    const selectors = [
      '.product-name',
      '.item-title', 
      '.post-title a',
      'h3 a',
      'h2 a',
      '.title'
    ];
    
    for (const selector of selectors) {
      const element = productCard.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }
    
    return 'Sản phẩm Lotus Glass';
  }
  
  extractVariant(productCard) {
    const selectors = [
      '.product-variant',
      '.item-variant',
      '.size',
      '.specification'
    ];
    
    for (const selector of selectors) {
      const element = productCard.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }
    
    return '';
  }
  
  extractPrice(productCard) {
    const selectors = [
      '.product-price',
      '.price',
      '.cost',
      '.amount'
    ];
    
    for (const selector of selectors) {
      const element = productCard.querySelector(selector);
      if (element) {
        const priceText = element.textContent.trim();
        const priceMatch = priceText.match(/[\d,]+/);
        if (priceMatch) {
          return parseFloat(priceMatch[0].replace(/,/g, ''));
        }
      }
    }
    
    return 0;
  }
  
  extractImage(productCard) {
    const img = productCard.querySelector('img');
    if (img) {
      return img.src || img.dataset.src || '';
    }
    return '';
  }
  
  // --------------------------------------------------------------------------
  // CART OPERATIONS
  // --------------------------------------------------------------------------
  
  addToCart(sku, productName, variant = '', price = 0, thumbnail = '', quantity = 1) {
    try {
      if (!sku || !productName) {
        throw new Error('Missing required product information');
      }
      
      price = parseFloat(price) || 0;
      quantity = parseInt(quantity) || 1;
      
      // Validate quantity
      if (quantity < LOTUS_CONFIG.MIN_QUANTITY || quantity > LOTUS_CONFIG.MAX_QUANTITY) {
        throw new Error(`Số lượng phải từ ${LOTUS_CONFIG.MIN_QUANTITY} đến ${LOTUS_CONFIG.MAX_QUANTITY}`);
      }
      
      const existingItem = this.items.find(item => item.sku === sku);
      
      if (existingItem) {
        // Update existing item
        const newQuantity = existingItem.quantity + quantity;
        if (newQuantity > LOTUS_CONFIG.MAX_QUANTITY) {
          this.showNotification(`Số lượng tối đa cho sản phẩm này là ${LOTUS_CONFIG.MAX_QUANTITY}`, 'warning');
          return;
        }
        existingItem.quantity = newQuantity;
        existingItem.totalPrice = existingItem.price * existingItem.quantity;
      } else {
        // Add new item
        this.items.push({
          sku,
          productName: LotusUtils.sanitizeHtml(productName),
          variant: LotusUtils.sanitizeHtml(variant),
          price,
          thumbnail,
          quantity,
          totalPrice: price * quantity,
          addedAt: new Date().toISOString()
        });
      }
      
      this.saveToStorage();
      this.updateCartDisplay();
      this.animateAddToCart(sku);
      this.showNotification(`Đã thêm "${productName}" vào giỏ hàng`);
      
      // Analytics
      this.trackEvent('add_to_cart', { sku, productName, price, quantity });
      
    } catch (error) {
      console.error('Add to cart error:', error);
      this.showNotification(error.message || 'Có lỗi xảy ra khi thêm sản phẩm', 'error');
    }
  }
  
  removeItem(sku) {
    try {
      const itemIndex = this.items.findIndex(item => item.sku === sku);
      if (itemIndex === -1) return;
      
      const item = this.items[itemIndex];
      
      // Animate removal
      const cartItem = document.querySelector(`[data-sku="${sku}"]`);
      if (cartItem) {
        cartItem.classList.add('cart-item-exit');
        setTimeout(() => {
          this.items.splice(itemIndex, 1);
          this.saveToStorage();
          this.updateCartDisplay();
          this.showNotification(`Đã xóa "${item.productName}" khỏi giỏ hàng`);
        }, 300);
      } else {
        this.items.splice(itemIndex, 1);
        this.saveToStorage();
        this.updateCartDisplay();
        this.showNotification(`Đã xóa "${item.productName}" khỏi giỏ hàng`);
      }
      
      // Analytics
      this.trackEvent('remove_from_cart', { sku, productName: item.productName });
      
    } catch (error) {
      console.error('Remove item error:', error);
      this.showNotification('Có lỗi xảy ra khi xóa sản phẩm', 'error');
    }
  }
  
  updateQuantity(sku, change) {
    try {
      const item = this.items.find(item => item.sku === sku);
      if (!item) return;
      
      const newQuantity = Math.max(
        LOTUS_CONFIG.MIN_QUANTITY, 
        Math.min(LOTUS_CONFIG.MAX_QUANTITY, item.quantity + change)
      );
      
      if (newQuantity === item.quantity) {
        if (newQuantity === LOTUS_CONFIG.MIN_QUANTITY && change < 0) {
          // Remove item if trying to go below minimum
          this.removeItem(sku);
          return;
        }
        return;
      }
      
      item.quantity = newQuantity;
      item.totalPrice = item.price * item.quantity;
      
      this.saveToStorage();
      this.updateCartDisplay();
      
      // Update quantity input
      const quantityInput = document.querySelector(`[data-sku="${sku}"] .qty-input`);
      if (quantityInput) {
        quantityInput.value = newQuantity;
      }
      
      // Analytics
      this.trackEvent('update_quantity', { sku, newQuantity, change });
      
    } catch (error) {
      console.error('Update quantity error:', error);
    }
  }
  
  setQuantity(sku, quantity) {
    try {
      quantity = parseInt(quantity);
      if (isNaN(quantity) || quantity < LOTUS_CONFIG.MIN_QUANTITY) {
        if (quantity < LOTUS_CONFIG.MIN_QUANTITY) {
          this.removeItem(sku);
          return;
        }
        return;
      }
      
      if (quantity > LOTUS_CONFIG.MAX_QUANTITY) {
        quantity = LOTUS_CONFIG.MAX_QUANTITY;
        this.showNotification(`Số lượng tối đa là ${LOTUS_CONFIG.MAX_QUANTITY}`, 'warning');
      }
      
      const item = this.items.find(item => item.sku === sku);
      if (!item) return;
      
      item.quantity = quantity;
      item.totalPrice = item.price * item.quantity;
      
      this.saveToStorage();
      this.updateCartDisplay();
      
      // Update quantity input
      const quantityInput = document.querySelector(`[data-sku="${sku}"] .qty-input`);
      if (quantityInput) {
        quantityInput.value = quantity;
      }
      
    } catch (error) {
      console.error('Set quantity error:', error);
    }
  }
  
  clearCart() {
    try {
      this.items = [];
      this.saveToStorage();
      this.updateCartDisplay();
      this.showNotification('Đã xóa tất cả sản phẩm khỏi giỏ hàng');
      
      // Analytics
      this.trackEvent('clear_cart');
      
    } catch (error) {
      console.error('Clear cart error:', error);
    }
  }
  
  // --------------------------------------------------------------------------
  // UI MANAGEMENT
  // --------------------------------------------------------------------------
  
  openCart() {
    try {
      this.isOpen = true;
      document.body.classList.add('cart-open');
      
      const sidebar = document.getElementById('cart-sidebar');
      const overlay = document.getElementById('cart-overlay');
      
      if (sidebar) {
        sidebar.classList.add('active');
        sidebar.setAttribute('aria-hidden', 'false');
      }
      
      if (overlay) {
        overlay.classList.add('active');
      }
      
      // Focus management for accessibility
      setTimeout(() => {
        const closeButton = document.getElementById('cart-close');
        if (closeButton) closeButton.focus();
      }, 100);
      
      // Analytics
      this.trackEvent('open_cart');
      
    } catch (error) {
      console.error('Open cart error:', error);
    }
  }
  
  closeCart() {
    try {
      this.isOpen = false;
      document.body.classList.remove('cart-open');
      
      const sidebar = document.getElementById('cart-sidebar');
      const overlay = document.getElementById('cart-overlay');
      
      if (sidebar) {
        sidebar.classList.remove('active');
        sidebar.setAttribute('aria-hidden', 'true');
      }
      
      if (overlay) {
        overlay.classList.remove('active');
      }
      
      // Analytics
      this.trackEvent('close_cart');
      
    } catch (error) {
      console.error('Close cart error:', error);
    }
  }
  
  updateCartDisplay() {
    this.updateCartBadge();
    this.updateCartItems();
    this.updateCartTotals();
    this.updateCheckoutButton();
  }
  
  updateCartBadge() {
    const badge = document.getElementById('cart-count');
    if (!badge) return;
    
    const totalItems = this.items.reduce((sum, item) => sum + item.quantity, 0);
    
    badge.textContent = totalItems;
    
    if (totalItems > 0) {
      badge.classList.add('has-items');
    } else {
      badge.classList.remove('has-items');
    }
  }
  
  updateCartItems() {
    const container = document.getElementById('cart-items');
    const emptyState = document.getElementById('cart-empty');
    
    if (!container) return;
    
    if (this.items.length === 0) {
      // Show empty state
      if (emptyState) {
        emptyState.style.display = 'block';
      }
      
      // Remove all cart items
      container.querySelectorAll('.cart-item').forEach(item => item.remove());
      return;
    }
    
    // Hide empty state
    if (emptyState) {
      emptyState.style.display = 'none';
    }
    
    // Update or create cart items
    this.items.forEach(item => {
      let cartItem = container.querySelector(`[data-sku="${item.sku}"]`);
      
      if (!cartItem) {
        cartItem = this.createCartItemElement(item);
        container.appendChild(cartItem);
        cartItem.classList.add('cart-item-enter');
      } else {
        this.updateCartItemElement(cartItem, item);
      }
    });
    
    // Remove items that are no longer in cart
    container.querySelectorAll('.cart-item').forEach(element => {
      const sku = element.dataset.sku;
      if (!this.items.find(item => item.sku === sku)) {
        element.remove();
      }
    });
  }
  
  createCartItemElement(item) {
    const template = document.getElementById('cart-item-template');
    if (!template) {
      console.error('Cart item template not found');
      return document.createElement('div');
    }
    
    const clone = template.content.cloneNode(true);
    const cartItem = clone.querySelector('.cart-item');
    
    // Set data attributes
    cartItem.dataset.sku = item.sku;
    
    // Update content
    this.updateCartItemElement(cartItem, item);
    
    return cartItem;
  }
  
  updateCartItemElement(element, item) {
    // Update image
    const img = element.querySelector('.item-image img');
    if (img) {
      img.src = item.thumbnail || '/images/default-product.jpg';
      img.alt = item.productName;
    }
    
    // Update text content
    const nameEl = element.querySelector('.item-name');
    if (nameEl) nameEl.textContent = item.productName;
    
    const variantEl = element.querySelector('.item-variant');
    if (variantEl) {
      variantEl.textContent = item.variant;
      variantEl.style.display = item.variant ? 'block' : 'none';
    }
    
    const priceEl = element.querySelector('.item-price');
    if (priceEl) priceEl.textContent = LotusUtils.formatPrice(item.price);
    
    const totalEl = element.querySelector('.item-total');
    if (totalEl) totalEl.textContent = LotusUtils.formatPrice(item.totalPrice);
    
    // Update quantity input
    const qtyInput = element.querySelector('.qty-input');
    if (qtyInput) {
      qtyInput.value = item.quantity;
      qtyInput.setAttribute('aria-label', `Số lượng cho ${item.productName}`);
    }
    
    // Update quantity buttons state
    const decreaseBtn = element.querySelector('.qty-decrease');
    const increaseBtn = element.querySelector('.qty-increase');
    
    if (decreaseBtn) {
      decreaseBtn.disabled = item.quantity <= LOTUS_CONFIG.MIN_QUANTITY;
    }
    
    if (increaseBtn) {
      increaseBtn.disabled = item.quantity >= LOTUS_CONFIG.MAX_QUANTITY;
    }
  }
  
  updateCartTotals() {
    const subtotalEl = document.getElementById('cart-subtotal');
    if (!subtotalEl) return;
    
    const subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
    subtotalEl.textContent = LotusUtils.formatPrice(subtotal);
  }
  
  updateCheckoutButton() {
    const checkoutBtn = document.getElementById('proceed-checkout');
    if (!checkoutBtn) return;
    
    const hasItems = this.items.length > 0;
    checkoutBtn.disabled = !hasItems;
    
    if (hasItems) {
      checkoutBtn.textContent = `Thanh toán (${this.items.length} sản phẩm)`;
    } else {
      checkoutBtn.textContent = 'Thanh toán';
    }
  }
  
  // --------------------------------------------------------------------------
  // ANIMATIONS & FEEDBACK
  // --------------------------------------------------------------------------
  
  animateAddToCart(sku) {
    // Find the add to cart button that was clicked
    const button = document.querySelector(`[data-sku="${sku}"] .add-to-cart-btn, .add-to-cart-btn[data-sku="${sku}"]`);
    if (!button) return;
    
    // Add success class temporarily
    button.classList.add('success');
    
    // Change button text temporarily
    const originalText = button.innerHTML;
    button.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg> Đã thêm';
    
    setTimeout(() => {
      button.classList.remove('success');
      button.innerHTML = originalText;
    }, LOTUS_CONFIG.ANIMATION.BUTTON_FEEDBACK);
  }
  
  showNotification(message, type = 'success') {
    const notification = document.getElementById('cart-notification');
    const messageEl = document.getElementById('notification-message');
    
    if (!notification || !messageEl) return;
    
    // Set message
    messageEl.textContent = message;
    
    // Set icon based on type
    const iconEl = notification.querySelector('.notification-icon');
    if (iconEl) {
      let iconPath = '';
      switch (type) {
        case 'success':
          iconPath = 'M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z';
          break;
        case 'error':
          iconPath = 'M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z';
          break;
        case 'warning':
          iconPath = 'M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z';
          break;
      }
      iconEl.innerHTML = `<path d="${iconPath}"/>`;
    }
    
    // Update notification class
    notification.className = `cart-notification ${type}`;
    
    // Show notification
    notification.classList.add('show');
    
    // Hide after delay
    setTimeout(() => {
      notification.classList.remove('show');
    }, LOTUS_CONFIG.ANIMATION.NOTIFICATION);
  }
  
  // --------------------------------------------------------------------------
  // CHECKOUT PROCESS
  // --------------------------------------------------------------------------
  
  proceedToCheckout() {
    if (this.items.length === 0) {
      this.showNotification('Giỏ hàng trống', 'warning');
      return;
    }
    
    try {
      // Validate cart before checkout
      this.validateCart().then((isValid) => {
        if (isValid) {
          this.openCheckoutModal();
        } else {
          this.showNotification('Có sản phẩm không hợp lệ trong giỏ hàng', 'error');
        }
      });
      
      // Analytics
      this.trackEvent('initiate_checkout', {
        value: this.getTotalValue(),
        items: this.items.length
      });
      
    } catch (error) {
      console.error('Checkout error:', error);
      this.showNotification('Có lỗi xảy ra khi tiến hành thanh toán', 'error');
    }
  }
  
  async validateCart() {
    // This would typically validate with the backend
    // For now, just check basic requirements
    return this.items.every(item => 
      item.sku && 
      item.productName && 
      item.price > 0 && 
      item.quantity > 0
    );
  }
  
  openCheckoutModal() {
    // This will be implemented in the next phase
    console.log('Opening checkout modal...');
    this.showNotification('Checkout functionality sẽ được triển khai trong Phase 1.2', 'info');
  }
  
  // --------------------------------------------------------------------------
  // DATA PERSISTENCE
  // --------------------------------------------------------------------------
  
  saveToStorage() {
    try {
      const cartData = {
        items: this.items,
        lastUpdated: new Date().toISOString(),
        version: '1.0'
      };
      
      localStorage.setItem(LOTUS_CONFIG.STORAGE_KEYS.CART, JSON.stringify(cartData));
    } catch (error) {
      console.error('Save to storage error:', error);
    }
  }
  
  loadFromStorage() {
    try {
      const stored = localStorage.getItem(LOTUS_CONFIG.STORAGE_KEYS.CART);
      if (!stored) return;
      
      const cartData = JSON.parse(stored);
      
      // Validate data structure
      if (cartData.items && Array.isArray(cartData.items)) {
        this.items = cartData.items;
        
        // Clean up invalid items
        this.items = this.items.filter(item => 
          item.sku && 
          item.productName && 
          typeof item.price === 'number' && 
          typeof item.quantity === 'number'
        );
      }
    } catch (error) {
      console.error('Load from storage error:', error);
      this.items = [];
    }
  }
  
  // --------------------------------------------------------------------------
  // ANALYTICS & TRACKING
  // --------------------------------------------------------------------------
  
  trackEvent(eventName, data = {}) {
    try {
      // Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', eventName, {
          event_category: 'ecommerce',
          event_label: 'lotus_cart',
          ...data
        });
      }
      
      // Custom analytics
      if (window.LotusAnalytics && typeof window.LotusAnalytics.track === 'function') {
        window.LotusAnalytics.track(eventName, data);
      }
      
      console.log(`📊 Event tracked: ${eventName}`, data);
    } catch (error) {
      console.error('Analytics error:', error);
    }
  }
  
  // --------------------------------------------------------------------------
  // UTILITY METHODS
  // --------------------------------------------------------------------------
  
  getTotalItems() {
    return this.items.reduce((sum, item) => sum + item.quantity, 0);
  }
  
  getTotalValue() {
    return this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  }
  
  getCartSummary() {
    return {
      items: this.items,
      totalItems: this.getTotalItems(),
      totalValue: this.getTotalValue(),
      lastUpdated: new Date().toISOString()
    };
  }
  
  // --------------------------------------------------------------------------
  // STATIC METHODS FOR GLOBAL ACCESS
  // --------------------------------------------------------------------------
  
  static getInstance() {
    if (!window.lotusCartInstance) {
      window.lotusCartInstance = new LotusCart();
    }
    return window.lotusCartInstance;
  }
}

// ============================================================================
// GLOBAL CART INSTANCE & API
// ============================================================================

// Create global instance
window.LotusCart = LotusCart.getInstance();

// Global convenience methods
window.LotusCart.addToCart = window.LotusCart.addToCart.bind(window.LotusCart);
window.LotusCart.removeItem = window.LotusCart.removeItem.bind(window.LotusCart);
window.LotusCart.updateQuantity = window.LotusCart.updateQuantity.bind(window.LotusCart);
window.LotusCart.setQuantity = window.LotusCart.setQuantity.bind(window.LotusCart);
window.LotusCart.openCart = window.LotusCart.openCart.bind(window.LotusCart);
window.LotusCart.closeCart = window.LotusCart.closeCart.bind(window.LotusCart);
window.LotusCart.proceedToCheckout = window.LotusCart.proceedToCheckout.bind(window.LotusCart);

// ============================================================================
// AUTO-INITIALIZATION
// ============================================================================

// Auto-initialize when script loads
(function() {
  'use strict';
  
  console.log('🚀 Lotus Glass Shopping Cart v1.0 loaded');
  
  // Initialize cart if not already done
  if (!window.LotusCart.isInitialized) {
    window.LotusCart.init();
  }
  
  // Expose useful utilities globally
  window.LotusUtils = LotusUtils;
  window.LOTUS_CONFIG = LOTUS_CONFIG;
  
  console.log('✅ Lotus Cart ready for use');
})();
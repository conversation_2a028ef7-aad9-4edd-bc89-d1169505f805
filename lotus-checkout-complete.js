/**
 * ============================================================================
 * LOTUS GLASS - COMPLETE CHECKOUT SYSTEM JAVASCRIPT
 * File: lotus-checkout-complete.js
 * 
 * Thêm vào cuối lotus-cart-complete.js
 * ============================================================================
 */

// ============================================================================
// CHECKOUT CONFIGURATION
// ============================================================================

const CHECKOUT_CONFIG = {
  // Validation settings
  PHONE_REGEX: /^(\+84|0)[1-9]\d{8}$/,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  TAX_ID_REGEX: /^\d{10,13}$/,
  
  // Form settings
  MAX_NOTES_LENGTH: 500,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  
  // Payment settings
  COD_FEE_RATE: 0.02, // 2% COD fee
  COD_MIN_FEE: 15000, // Minimum 15k VND
  COD_MAX_FEE: 50000, // Maximum 50k VND
  FREE_SHIPPING_THRESHOLD: 500000, // 500k VND
  
  // API endpoints
  ENDPOINTS: {
    CREATE_ORDER: 'create_order',
    VALIDATE_CART: 'validate_cart', 
    CALCULATE_SHIPPING: 'calculate_shipping',
    APPLY_PROMOTION: 'apply_promotion',
    VNPAY_CREATE: 'vnpay_create_payment',
    ORDER_STATUS: 'get_order_status'
  },
  
  // Error messages
  ERRORS: {
    REQUIRED_FIELD: 'Trường này là bắt buộc',
    INVALID_PHONE: 'Số điện thoại không hợp lệ (10-11 số, bắt đầu bằng 0)',
    INVALID_EMAIL: 'Địa chỉ email không hợp lệ',
    INVALID_TAX_ID: 'Mã số thuế phải có 10-13 chữ số',
    FORM_VALIDATION: 'Vui lòng kiểm tra và sửa các lỗi trong form',
    ORDER_CREATION: 'Có lỗi xảy ra khi tạo đơn hàng. Vui lòng thử lại.',
    NETWORK_ERROR: 'Không thể kết nối tới server. Vui lòng kiểm tra kết nối mạng.',
    EMPTY_CART: 'Giỏ hàng trống. Vui lòng thêm sản phẩm trước khi thanh toán.'
  }
};

// ============================================================================
// LOTUS CHECKOUT CLASS
// ============================================================================

class LotusCheckout {
  constructor(cartInstance) {
    this.cart = cartInstance;
    this.isOpen = false;
    this.currentStep = 1;
    this.formData = {};
    this.validationErrors = {};
    this.shippingCost = 0;
    this.discountAmount = 0;
    this.appliedPromotion = null;
    this.isSubmitting = false;
    
    // Bind methods
    this.openCheckout = this.openCheckout.bind(this);
    this.closeCheckout = this.closeCheckout.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
    this.handleProvinceChange = this.handleProvinceChange.bind(this);
    this.handlePaymentMethodChange = this.handlePaymentMethodChange.bind(this);
    
    this.init();
  }
  
  // --------------------------------------------------------------------------
  // INITIALIZATION
  // --------------------------------------------------------------------------
  
  init() {
    try {
      this.setupEventListeners();
      this.loadDistrictsData();
      this.setupFormValidation();
      this.setupAutoSave();
      
      console.log('🛒 Lotus Checkout initialized successfully');
    } catch (error) {
      console.error('❌ Checkout initialization failed:', error);
    }
  }
  
  setupEventListeners() {
    // Modal controls
    const checkoutClose = document.getElementById('checkout-close');
    const checkoutBack = document.getElementById('checkout-back');
    const successClose = document.getElementById('success-close');
    
    if (checkoutClose) {
      checkoutClose.addEventListener('click', this.closeCheckout);
    }
    
    if (checkoutBack) {
      checkoutBack.addEventListener('click', this.closeCheckout);
    }
    
    if (successClose) {
      successClose.addEventListener('click', () => {
        this.closeSuccessModal();
      });
    }
    
    // Form submission
    const checkoutForm = document.getElementById('checkout-form');
    if (checkoutForm) {
      checkoutForm.addEventListener('submit', this.handleFormSubmit);
    }
    
    // Province/District cascade
    const provinceSelect = document.getElementById('shipping-province');
    if (provinceSelect) {
      provinceSelect.addEventListener('change', this.handleProvinceChange);
    }
    
    // Payment method changes
    const paymentMethods = document.querySelectorAll('input[name="paymentMethod"]');
    paymentMethods.forEach(radio => {
      radio.addEventListener('change', this.handlePaymentMethodChange);
    });
    
    // Payment option clicks
    const paymentOptions = document.querySelectorAll('.payment-option');
    paymentOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        const radio = option.querySelector('input[type="radio"]');
        if (radio && !radio.checked) {
          radio.checked = true;
          this.handlePaymentMethodChange({ target: radio });
        }
      });
    });
    
    // Promotion code
    const applyPromotionBtn = document.getElementById('apply-promotion');
    if (applyPromotionBtn) {
      applyPromotionBtn.addEventListener('click', () => {
        this.applyPromotionCode();
      });
    }
    
    // Promotion code enter key
    const promotionInput = document.getElementById('promotion-code');
    if (promotionInput) {
      promotionInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.applyPromotionCode();
        }
      });
    }
    
    // ESC key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeCheckout();
      }
    });
    
    // Form field validations
    this.setupFieldValidations();
  }
  
  setupFieldValidations() {
    const fields = [
      'customer-name', 'customer-phone', 'customer-email',
      'shipping-province', 'shipping-district', 'shipping-address'
    ];
    
    fields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        field.addEventListener('blur', () => this.validateField(fieldId));
        field.addEventListener('input', () => this.clearFieldError(fieldId));
      }
    });
  }
  
  setupAutoSave() {
    // Auto-save form data periodically
    setInterval(() => {
      if (this.isOpen) {
        this.saveFormData();
      }
    }, CHECKOUT_CONFIG.AUTO_SAVE_INTERVAL);
  }
  
  loadDistrictsData() {
    try {
      const districtsScript = document.getElementById('districts-data');
      if (districtsScript) {
        this.districtsData = JSON.parse(districtsScript.textContent);
      }
    } catch (error) {
      console.error('Failed to load districts data:', error);
      this.districtsData = {};
    }
  }
  
  // --------------------------------------------------------------------------
  // CHECKOUT MODAL MANAGEMENT
  // --------------------------------------------------------------------------
  
  openCheckout() {
    try {
      // Validate cart first
      if (!this.cart.items || this.cart.items.length === 0) {
        this.cart.showNotification(CHECKOUT_CONFIG.ERRORS.EMPTY_CART, 'warning');
        return;
      }
      
      this.isOpen = true;
      document.body.classList.add('cart-open');
      
      const modal = document.getElementById('checkout-modal');
      if (modal) {
        modal.classList.add('active');
        modal.setAttribute('aria-hidden', 'false');
      }
      
      // Update order summary
      this.updateOrderSummary();
      
      // Load saved form data
      this.loadFormData();
      
      // Focus first input
      setTimeout(() => {
        const firstInput = document.getElementById('customer-name');
        if (firstInput) firstInput.focus();
      }, 300);
      
      // Analytics
      this.trackEvent('checkout_initiated', {
        cart_value: this.cart.getTotalValue(),
        items_count: this.cart.items.length
      });
      
    } catch (error) {
      console.error('Open checkout error:', error);
      this.cart.showNotification('Có lỗi xảy ra khi mở checkout', 'error');
    }
  }
  
  closeCheckout() {
    try {
      this.isOpen = false;
      document.body.classList.remove('cart-open');
      
      const modal = document.getElementById('checkout-modal');
      if (modal) {
        modal.classList.remove('active');
        modal.setAttribute('aria-hidden', 'true');
      }
      
      // Save form data before closing
      this.saveFormData();
      
      // Analytics
      this.trackEvent('checkout_closed');
      
    } catch (error) {
      console.error('Close checkout error:', error);
    }
  }
  
  closeSuccessModal() {
    const modal = document.getElementById('order-success-modal');
    if (modal) {
      modal.classList.remove('active');
      modal.setAttribute('aria-hidden', 'true');
    }
    
    // Clear cart after successful order
    this.cart.clearCart();
    
    // Close checkout modal too
    this.closeCheckout();
  }
  
  // --------------------------------------------------------------------------
  // ORDER SUMMARY MANAGEMENT
  // --------------------------------------------------------------------------
  
  updateOrderSummary() {
    try {
      const itemCountEl = document.getElementById('summary-item-count');
      const itemsContainer = document.getElementById('summary-items');
      const subtotalEl = document.getElementById('summary-subtotal');
      const shippingEl = document.getElementById('summary-shipping');
      const totalEl = document.getElementById('summary-total');
      
      if (!itemCountEl || !itemsContainer || !subtotalEl || !totalEl) return;
      
      // Update item count
      itemCountEl.textContent = this.cart.items.length;
      
      // Clear and populate items
      itemsContainer.innerHTML = '';
      
      this.cart.items.forEach(item => {
        const itemEl = this.createSummaryItemElement(item);
        itemsContainer.appendChild(itemEl);
      });
      
      // Calculate totals
      const subtotal = this.cart.getTotalValue();
      const shipping = this.calculateShippingCost();
      const discount = this.discountAmount;
      const total = subtotal + shipping - discount;
      
      // Update display
      subtotalEl.textContent = LotusUtils.formatPrice(subtotal);
      shippingEl.textContent = shipping > 0 ? LotusUtils.formatPrice(shipping) : 'Miễn phí';
      totalEl.textContent = LotusUtils.formatPrice(total);
      
      // Update discount row
      const discountRow = document.getElementById('summary-discount-row');
      const discountEl = document.getElementById('summary-discount');
      
      if (discount > 0 && discountRow && discountEl) {
        discountRow.style.display = 'flex';
        discountEl.textContent = `-${LotusUtils.formatPrice(discount)}`;
      } else if (discountRow) {
        discountRow.style.display = 'none';
      }
      
      this.shippingCost = shipping;
      
    } catch (error) {
      console.error('Update order summary error:', error);
    }
  }
  
  createSummaryItemElement(item) {
    const itemEl = document.createElement('div');
    itemEl.className = 'summary-item';
    
    itemEl.innerHTML = `
      <div class="summary-item-image">
        <img src="${item.thumbnail || '/images/default-product.jpg'}" alt="${item.productName}" loading="lazy">
      </div>
      <div class="summary-item-details">
        <h4 class="summary-item-name">${LotusUtils.sanitizeHtml(item.productName)}</h4>
        ${item.variant ? `<p class="summary-item-variant">${LotusUtils.sanitizeHtml(item.variant)}</p>` : ''}
        <p class="summary-item-quantity">Số lượng: ${item.quantity}</p>
      </div>
      <div class="summary-item-price">${LotusUtils.formatPrice(item.totalPrice)}</div>
    `;
    
    return itemEl;
  }
  
  calculateShippingCost() {
    const subtotal = this.cart.getTotalValue();
    
    // Free shipping threshold
    if (subtotal >= CHECKOUT_CONFIG.FREE_SHIPPING_THRESHOLD) {
      return 0;
    }
    
    const province = document.getElementById('shipping-province')?.value;
    if (!province) return 0;
    
    // Basic shipping rates (this could be enhanced with API call)
    const shippingRates = {
      'Hồ Chí Minh': 30000,
      'Hà Nội': 35000,
      'Đà Nẵng': 40000,
      'Cần Thơ': 35000
    };
    
    return shippingRates[province] || 50000; // Default rate for other provinces
  }
  
  // --------------------------------------------------------------------------
  // FORM VALIDATION
  // --------------------------------------------------------------------------
  
  validateField(fieldId) {
    const field = document.getElementById(fieldId);
    const value = field ? field.value.trim() : '';
    let error = '';
    
    switch (fieldId) {
      case 'customer-name':
        if (!value) {
          error = CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD;
        } else if (value.length < 2) {
          error = 'Họ tên phải có ít nhất 2 ký tự';
        }
        break;
        
      case 'customer-phone':
        if (!value) {
          error = CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD;
        } else if (!CHECKOUT_CONFIG.PHONE_REGEX.test(value)) {
          error = CHECKOUT_CONFIG.ERRORS.INVALID_PHONE;
        }
        break;
        
      case 'customer-email':
        if (value && !CHECKOUT_CONFIG.EMAIL_REGEX.test(value)) {
          error = CHECKOUT_CONFIG.ERRORS.INVALID_EMAIL;
        }
        break;
        
      case 'tax-id':
        if (value && !CHECKOUT_CONFIG.TAX_ID_REGEX.test(value)) {
          error = CHECKOUT_CONFIG.ERRORS.INVALID_TAX_ID;
        }
        break;
        
      case 'shipping-province':
      case 'shipping-district':
      case 'shipping-address':
        if (!value) {
          error = CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD;
        }
        break;
    }
    
    this.setFieldError(fieldId, error);
    return !error;
  }
  
  validateForm() {
    const requiredFields = [
      'customer-name', 'customer-phone', 
      'shipping-province', 'shipping-district', 'shipping-address'
    ];
    
    let isValid = true;
    
    // Validate required fields
    requiredFields.forEach(fieldId => {
      if (!this.validateField(fieldId)) {
        isValid = false;
      }
    });
    
    // Validate email if provided
    const email = document.getElementById('customer-email')?.value.trim();
    if (email && !this.validateField('customer-email')) {
      isValid = false;
    }
    
    // Validate payment method
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (!paymentMethod) {
      this.setFieldError('payment-method', 'Vui lòng chọn phương thức thanh toán');
      isValid = false;
    } else {
      this.clearFieldError('payment-method');
    }
    
    return isValid;
  }
  
  setFieldError(fieldId, error) {
    const field = document.getElementById(fieldId);
    const errorEl = document.getElementById(`${fieldId}-error`);
    const formGroup = field?.closest('.form-group');
    
    if (error) {
      this.validationErrors[fieldId] = error;
      
      if (field) {
        field.classList.add('error');
        field.classList.remove('success');
      }
      
      if (formGroup) {
        formGroup.classList.add('error');
        formGroup.classList.remove('success');
      }
      
      if (errorEl) {
        errorEl.textContent = error;
        errorEl.style.display = 'flex';
      }
    } else {
      delete this.validationErrors[fieldId];
      
      if (field) {
        field.classList.remove('error');
        field.classList.add('success');
      }
      
      if (formGroup) {
        formGroup.classList.remove('error');
        formGroup.classList.add('success');
      }
      
      if (errorEl) {
        errorEl.style.display = 'none';
      }
    }
  }
  
  clearFieldError(fieldId) {
    this.setFieldError(fieldId, '');
  }
  
  // --------------------------------------------------------------------------
  // FORM DATA MANAGEMENT
  // --------------------------------------------------------------------------
  
  saveFormData() {
    try {
      const form = document.getElementById('checkout-form');
      if (!form) return;
      
      const formData = new FormData(form);
      const data = {};
      
      for (let [key, value] of formData.entries()) {
        data[key] = value;
      }
      
      localStorage.setItem('lotus_checkout_form', JSON.stringify(data));
    } catch (error) {
      console.error('Save form data error:', error);
    }
  }
  
  loadFormData() {
    try {
      const saved = localStorage.getItem('lotus_checkout_form');
      if (!saved) return;
      
      const data = JSON.parse(saved);
      
      Object.entries(data).forEach(([key, value]) => {
        const field = document.querySelector(`[name="${key}"]`);
        if (field) {
          if (field.type === 'radio' && field.value === value) {
            field.checked = true;
          } else if (field.type !== 'radio') {
            field.value = value;
          }
        }
      });
      
      // Trigger province change if province is loaded
      const province = data.shippingProvince;
      if (province) {
        setTimeout(() => {
          this.handleProvinceChange({ target: { value: province } });
          
          // Set district after a delay
          const district = data.shippingDistrict;
          if (district) {
            setTimeout(() => {
              const districtSelect = document.getElementById('shipping-district');
              if (districtSelect) {
                districtSelect.value = district;
              }
            }, 100);
          }
        }, 100);
      }
      
    } catch (error) {
      console.error('Load form data error:', error);
    }
  }
  
  clearFormData() {
    localStorage.removeItem('lotus_checkout_form');
  }
  
  // --------------------------------------------------------------------------
  // EVENT HANDLERS
  // --------------------------------------------------------------------------
  
  handleProvinceChange(e) {
    const province = e.target.value;
    const districtSelect = document.getElementById('shipping-district');
    
    if (!districtSelect) return;
    
    // Clear districts
    districtSelect.innerHTML = '<option value="">Chọn Quận/Huyện</option>';
    districtSelect.disabled = !province;
    
    if (province && this.districtsData[province]) {
      // Populate districts
      this.districtsData[province].forEach(district => {
        const option = document.createElement('option');
        option.value = district;
        option.textContent = district;
        districtSelect.appendChild(option);
      });
      
      districtSelect.disabled = false;
    }
    
    // Recalculate shipping
    this.updateOrderSummary();
  }
  
  handlePaymentMethodChange(e) {
    const method = e.target.value;
    
    // Update payment option UI
    document.querySelectorAll('.payment-option').forEach(option => {
      option.classList.remove('selected');
    });
    
    const selectedOption = e.target.closest('.payment-option');
    if (selectedOption) {
      selectedOption.classList.add('selected');
    }
    
    // Update COD fee
    this.updateCODFee(method);
    
    // Clear payment method error
    this.clearFieldError('payment-method');
  }
  
  updateCODFee(paymentMethod) {
    const codFeeEl = document.getElementById('cod-fee');
    if (!codFeeEl) return;
    
    if (paymentMethod === 'cod') {
      const subtotal = this.cart.getTotalValue();
      const fee = Math.min(
        Math.max(subtotal * CHECKOUT_CONFIG.COD_FEE_RATE, CHECKOUT_CONFIG.COD_MIN_FEE),
        CHECKOUT_CONFIG.COD_MAX_FEE
      );
      
      codFeeEl.textContent = `Phí COD: ${LotusUtils.formatPrice(fee)}`;
    } else {
      codFeeEl.textContent = 'Phí COD: 0₫';
    }
  }
  
  async handleFormSubmit(e) {
    e.preventDefault();
    
    if (this.isSubmitting) return;
    
    try {
      // Validate form
      if (!this.validateForm()) {
        this.cart.showNotification(CHECKOUT_CONFIG.ERRORS.FORM_VALIDATION, 'error');
        return;
      }
      
      this.isSubmitting = true;
      this.setSubmitButtonLoading(true);
      
      // Collect form data
      const orderData = this.collectFormData();
      
      // Create order
      const result = await this.createOrder(orderData);
      
      if (result.success) {
        this.showOrderSuccess(result.data);
        this.clearFormData();
        
        // Handle payment redirect if necessary
        if (result.data.paymentUrl) {
          setTimeout(() => {
            window.location.href = result.data.paymentUrl;
          }, 2000);
        }
      } else {
        throw new Error(result.error || CHECKOUT_CONFIG.ERRORS.ORDER_CREATION);
      }
      
    } catch (error) {
      console.error('Form submit error:', error);
      this.cart.showNotification(error.message || CHECKOUT_CONFIG.ERRORS.ORDER_CREATION, 'error');
    } finally {
      this.isSubmitting = false;
      this.setSubmitButtonLoading(false);
    }
  }
  
  // --------------------------------------------------------------------------
  // ORDER PROCESSING
  // --------------------------------------------------------------------------
  
  collectFormData() {
    const form = document.getElementById('checkout-form');
    const formData = new FormData(form);
    
    const data = {
      // Customer info
      customer: {
        name: formData.get('customerName'),
        phone: formData.get('customerPhone'),
        email: formData.get('customerEmail') || null
      },
      
      // Company info (optional)
      company: {
        name: formData.get('companyName') || null,
        taxId: formData.get('taxId') || null,
        address: formData.get('companyAddress') || null
      },
      
      // Shipping address
      shippingAddress: {
        province: formData.get('shippingProvince'),
        district: formData.get('shippingDistrict'),
        address: formData.get('shippingAddress')
      },
      
      // Payment method
      paymentMethod: formData.get('paymentMethod'),
      
      // Order details
      items: this.cart.items,
      subtotal: this.cart.getTotalValue(),
      shippingCost: this.shippingCost,
      discountAmount: this.discountAmount,
      total: this.cart.getTotalValue() + this.shippingCost - this.discountAmount,
      
      // Additional info
      notes: formData.get('orderNotes') || null,
      appliedPromotion: this.appliedPromotion,
      
      // Metadata
      createdAt: new Date().toISOString(),
      source: 'website'
    };
    
    return data;
  }
  
  async createOrder(orderData) {
    try {
      const response = await fetch(LOTUS_CONFIG.API_BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: CHECKOUT_CONFIG.ENDPOINTS.CREATE_ORDER,
          orderData: JSON.stringify(orderData)
        })
      });
      
      if (!response.ok) {
        throw new Error(CHECKOUT_CONFIG.ERRORS.NETWORK_ERROR);
      }
      
      const result = await response.json();
      return result;
      
    } catch (error) {
      console.error('Create order API error:', error);
      throw error;
    }
  }
  
  // --------------------------------------------------------------------------
  // PROMOTION CODE HANDLING
  // --------------------------------------------------------------------------
  
  async applyPromotionCode() {
    const promoInput = document.getElementById('promotion-code');
    const applyBtn = document.getElementById('apply-promotion');
    
    if (!promoInput || !applyBtn) return;
    
    const code = promoInput.value.trim().toUpperCase();
    
    if (!code) {
      this.setFieldError('promotion-code', 'Vui lòng nhập mã giảm giá');
      return;
    }
    
    try {
      // Set loading state
      applyBtn.disabled = true;
      applyBtn.innerHTML = '<div class="checkout-spinner"></div>';
      
      const response = await fetch(LOTUS_CONFIG.API_BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: CHECKOUT_CONFIG.ENDPOINTS.APPLY_PROMOTION,
          items: JSON.stringify(this.cart.items),
          promotionCode: code
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.discountAmount = result.data.discountAmount;
        this.appliedPromotion = result.data.appliedPromotion;
        
        this.clearFieldError('promotion-code');
        this.setFieldSuccess('promotion-code', `Đã áp dụng mã giảm giá: -${LotusUtils.formatPrice(this.discountAmount)}`);
        
        promoInput.disabled = true;
        applyBtn.textContent = 'Đã áp dụng';
        
        this.updateOrderSummary();
        
        // Analytics
        this.trackEvent('promotion_applied', {
          code: code,
          discount_amount: this.discountAmount
        });
        
      } else {
        this.setFieldError('promotion-code', result.error || 'Mã giảm giá không hợp lệ');
      }
      
    } catch (error) {
      console.error('Apply promotion error:', error);
      this.setFieldError('promotion-code', 'Có lỗi xảy ra khi áp dụng mã giảm giá');
    } finally {
      if (!this.appliedPromotion) {
        applyBtn.disabled = false;
        applyBtn.textContent = 'Áp dụng';
      }
    }
  }
  
  setFieldSuccess(fieldId, message) {
    const successEl = document.getElementById(`${fieldId}-success`);
    if (successEl) {
      successEl.textContent = message;
      successEl.style.display = 'flex';
    }
  }
  
  // --------------------------------------------------------------------------
  // UI STATE MANAGEMENT
  // --------------------------------------------------------------------------
  
  setSubmitButtonLoading(loading) {
    const button = document.getElementById('place-order');
    const text = document.getElementById('place-order-text');
    const spinner = document.getElementById('place-order-spinner');
    
    if (!button || !text || !spinner) return;
    
    if (loading) {
      button.disabled = true;
      button.classList.add('loading');
      text.style.display = 'none';
      spinner.style.display = 'block';
    } else {
      button.disabled = false;
      button.classList.remove('loading');
      text.style.display = 'block';
      spinner.style.display = 'none';
    }
  }
  
  showOrderSuccess(orderData) {
    // Hide checkout modal
    const checkoutModal = document.getElementById('checkout-modal');
    if (checkoutModal) {
      checkoutModal.classList.remove('active');
    }
    
    // Show success modal
    const successModal = document.getElementById('order-success-modal');
    if (successModal) {
      successModal.classList.add('active');
      successModal.setAttribute('aria-hidden', 'false');
    }
    
    // Populate order details
    this.populateOrderDetails(orderData);
    
    // Analytics
    this.trackEvent('order_created', {
      order_id: orderData.orderId,
      order_value: orderData.total,
      payment_method: orderData.paymentMethod
    });
  }
  
  populateOrderDetails(orderData) {
    const detailsContainer = document.getElementById('order-details');
    if (!detailsContainer) return;
    
    detailsContainer.innerHTML = `
      <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
        <strong>Mã đơn hàng:</strong>
        <span style="color: var(--cart-primary); font-weight: 600;">#${orderData.orderId}</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
        <strong>Tổng tiền:</strong>
        <span style="color: var(--cart-primary); font-weight: 600;">${LotusUtils.formatPrice(orderData.total)}</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
        <strong>Phương thức thanh toán:</strong>
        <span>${this.getPaymentMethodName(orderData.paymentMethod)}</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
        <strong>Trạng thái:</strong>
        <span style="color: var(--cart-warning); font-weight: 500;">Đang xử lý</span>
      </div>
      ${orderData.estimatedDelivery ? `
        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
          <strong>Dự kiến giao hàng:</strong>
          <span>${orderData.estimatedDelivery}</span>
        </div>
      ` : ''}
      <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--cart-border); font-size: 14px; color: var(--cart-secondary);">
        <p style="margin: 0;">📞 Chúng tôi sẽ liên hệ xác nhận đơn hàng trong vòng 30 phút.</p>
        <p style="margin: 8px 0 0 0;">📧 Thông tin chi tiết đã được gửi qua email (nếu có).</p>
      </div>
    `;
  }
  
  getPaymentMethodName(method) {
    const names = {
      'vnpay': 'VNPay',
      'bank_transfer': 'Chuyển khoản ngân hàng',
      'cod': 'Thanh toán khi nhận hàng (COD)'
    };
    
    return names[method] || method;
  }
  
  // --------------------------------------------------------------------------
  // ANALYTICS & TRACKING
  // --------------------------------------------------------------------------
  
  trackEvent(eventName, data = {}) {
    try {
      // Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', eventName, {
          event_category: 'checkout',
          event_label: 'lotus_checkout',
          ...data
        });
      }
      
      // Custom analytics
      if (window.LotusAnalytics && typeof window.LotusAnalytics.track === 'function') {
        window.LotusAnalytics.track(eventName, data);
      }
      
      console.log(`📊 Checkout event tracked: ${eventName}`, data);
    } catch (error) {
      console.error('Checkout analytics error:', error);
    }
  }
  
  // --------------------------------------------------------------------------
  // UTILITY METHODS
  // --------------------------------------------------------------------------
  
  getFormSummary() {
    return {
      isOpen: this.isOpen,
      currentStep: this.currentStep,
      hasErrors: Object.keys(this.validationErrors).length > 0,
      shippingCost: this.shippingCost,
      discountAmount: this.discountAmount,
      appliedPromotion: this.appliedPromotion
    };
  }
}

// ============================================================================
// GLOBAL CHECKOUT INSTANCE & INTEGRATION
// ============================================================================

// Extend the existing LotusCart class to integrate checkout
(function() {
  'use strict';
  
  // Wait for cart to be available
  const initCheckout = () => {
    if (window.LotusCart && window.LotusCart.isInitialized) {
      // Create checkout instance
      window.LotusCheckout = new LotusCheckout(window.LotusCart);
      
      // Override the proceedToCheckout method in cart
      window.LotusCart.proceedToCheckout = function() {
        window.LotusCheckout.openCheckout();
      };
      
      console.log('✅ Lotus Checkout integrated successfully');
    } else {
      // Retry after a short delay
      setTimeout(initCheckout, 100);
    }
  };
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCheckout);
  } else {
    initCheckout();
  }
  
})();

// ============================================================================
// CHECKOUT UTILITY FUNCTIONS
// ============================================================================

const LotusCheckoutUtils = {
  /**
   * Format phone number for display
   */
  formatPhoneNumber(phone) {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
    } else if (cleaned.length === 11) {
      return cleaned.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
    }
    return phone;
  },
  
  /**
   * Validate Vietnamese phone number
   */
  isValidPhoneNumber(phone) {
    return CHECKOUT_CONFIG.PHONE_REGEX.test(phone);
  },
  
  /**
   * Validate email address
   */
  isValidEmail(email) {
    return CHECKOUT_CONFIG.EMAIL_REGEX.test(email);
  },
  
  /**
   * Get shipping cost estimation
   */
  estimateShippingCost(province, cartValue) {
    if (cartValue >= CHECKOUT_CONFIG.FREE_SHIPPING_THRESHOLD) {
      return 0;
    }
    
    const rates = {
      'Hồ Chí Minh': 30000,
      'Hà Nội': 35000,
      'Đà Nẵng': 40000,
      'Cần Thơ': 35000
    };
    
    return rates[province] || 50000;
  },
  
  /**
   * Calculate COD fee
   */
  calculateCODFee(orderValue) {
    const fee = orderValue * CHECKOUT_CONFIG.COD_FEE_RATE;
    return Math.min(Math.max(fee, CHECKOUT_CONFIG.COD_MIN_FEE), CHECKOUT_CONFIG.COD_MAX_FEE);
  }
};

// Make checkout utils globally available
window.LotusCheckoutUtils = LotusCheckoutUtils;
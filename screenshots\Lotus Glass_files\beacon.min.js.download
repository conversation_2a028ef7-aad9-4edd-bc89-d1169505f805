!function(){var e={344:function(){!function(e){var t="currentScript",n=e.getElementsByTagName("script");t in e||Object.defineProperty(e,t,{get:function(){try{throw new Error}catch(r){var e,t=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(r.stack)||[!1])[1];for(e in n)if(n[e].src==t||"interactive"==n[e].readyState)return n[e];return null}}})}(document)},264:function(e,t,n){!function(e){var t=function(){try{return!!Symbol.iterator}catch(e){return!1}}(),n=function(e){var n={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return t&&(n[Symbol.iterator]=function(){return n}),n},r=function(e){return encodeURIComponent(e).replace(/%20/g,"+")},i=function(e){return decodeURIComponent(String(e).replace(/\+/g," "))};(function(){try{var t=e.URLSearchParams;return"a=1"===new t("?a=1").toString()&&"function"==typeof t.prototype.set&&"function"==typeof t.prototype.entries}catch(e){return!1}})()||function(){var i=function(e){Object.defineProperty(this,"_entries",{writable:!0,value:{}});var t=typeof e;if("undefined"===t);else if("string"===t)""!==e&&this._fromString(e);else if(e instanceof i){var n=this;e.forEach((function(e,t){n.append(t,e)}))}else{if(null===e||"object"!==t)throw new TypeError("Unsupported input's type for URLSearchParams");if("[object Array]"===Object.prototype.toString.call(e))for(var r=0;r<e.length;r++){var o=e[r];if("[object Array]"!==Object.prototype.toString.call(o)&&2===o.length)throw new TypeError("Expected [string, any] as entry at index "+r+" of URLSearchParams's input");this.append(o[0],o[1])}else for(var a in e)e.hasOwnProperty(a)&&this.append(a,e[a])}},o=i.prototype;o.append=function(e,t){e in this._entries?this._entries[e].push(String(t)):this._entries[e]=[String(t)]},o.delete=function(e){delete this._entries[e]},o.get=function(e){return e in this._entries?this._entries[e][0]:null},o.getAll=function(e){return e in this._entries?this._entries[e].slice(0):[]},o.has=function(e){return e in this._entries},o.set=function(e,t){this._entries[e]=[String(t)]},o.forEach=function(e,t){var n;for(var r in this._entries)if(this._entries.hasOwnProperty(r)){n=this._entries[r];for(var i=0;i<n.length;i++)e.call(t,n[i],r,this)}},o.keys=function(){var e=[];return this.forEach((function(t,n){e.push(n)})),n(e)},o.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),n(e)},o.entries=function(){var e=[];return this.forEach((function(t,n){e.push([n,t])})),n(e)},t&&(o[Symbol.iterator]=o.entries),o.toString=function(){var e=[];return this.forEach((function(t,n){e.push(r(n)+"="+r(t))})),e.join("&")},e.URLSearchParams=i}();var o=e.URLSearchParams.prototype;"function"!=typeof o.sort&&(o.sort=function(){var e=this,t=[];this.forEach((function(n,r){t.push([r,n]),e._entries||e.delete(r)})),t.sort((function(e,t){return e[0]<t[0]?-1:e[0]>t[0]?1:0})),e._entries&&(e._entries={});for(var n=0;n<t.length;n++)this.append(t[n][0],t[n][1])}),"function"!=typeof o._fromString&&Object.defineProperty(o,"_fromString",{enumerable:!1,configurable:!1,writable:!1,value:function(e){if(this._entries)this._entries={};else{var t=[];this.forEach((function(e,n){t.push(n)}));for(var n=0;n<t.length;n++)this.delete(t[n])}var r,o=(e=e.replace(/^\?/,"")).split("&");for(n=0;n<o.length;n++)r=o[n].split("="),this.append(i(r[0]),r.length>1?i(r[1]):"")}})}(void 0!==n.g?n.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:this),function(e){if(function(){try{var t=new e.URL("b","http://a");return t.pathname="c d","http://a/c%20d"===t.href&&t.searchParams}catch(e){return!1}}()||function(){var t=e.URL,n=function(t,n){"string"!=typeof t&&(t=String(t)),n&&"string"!=typeof n&&(n=String(n));var r,i=document;if(n&&(void 0===e.location||n!==e.location.href)){n=n.toLowerCase(),(r=(i=document.implementation.createHTMLDocument("")).createElement("base")).href=n,i.head.appendChild(r);try{if(0!==r.href.indexOf(n))throw new Error(r.href)}catch(e){throw new Error("URL unable to set base "+n+" due to "+e)}}var o=i.createElement("a");o.href=t,r&&(i.body.appendChild(o),o.href=o.href);var a=i.createElement("input");if(a.type="url",a.value=t,":"===o.protocol||!/:/.test(o.href)||!a.checkValidity()&&!n)throw new TypeError("Invalid URL");Object.defineProperty(this,"_anchorElement",{value:o});var c=new e.URLSearchParams(this.search),u=!0,s=!0,p=this;["append","delete","set"].forEach((function(e){var t=c[e];c[e]=function(){t.apply(c,arguments),u&&(s=!1,p.search=c.toString(),s=!0)}})),Object.defineProperty(this,"searchParams",{value:c,enumerable:!0});var d=void 0;Object.defineProperty(this,"_updateSearchParams",{enumerable:!1,configurable:!1,writable:!1,value:function(){this.search!==d&&(d=this.search,s&&(u=!1,this.searchParams._fromString(this.search),u=!0))}})},r=n.prototype;["hash","host","hostname","port","protocol"].forEach((function(e){!function(e){Object.defineProperty(r,e,{get:function(){return this._anchorElement[e]},set:function(t){this._anchorElement[e]=t},enumerable:!0})}(e)})),Object.defineProperty(r,"search",{get:function(){return this._anchorElement.search},set:function(e){this._anchorElement.search=e,this._updateSearchParams()},enumerable:!0}),Object.defineProperties(r,{toString:{get:function(){var e=this;return function(){return e.href}}},href:{get:function(){return this._anchorElement.href.replace(/\?$/,"")},set:function(e){this._anchorElement.href=e,this._updateSearchParams()},enumerable:!0},pathname:{get:function(){return this._anchorElement.pathname.replace(/(^\/?)/,"/")},set:function(e){this._anchorElement.pathname=e},enumerable:!0},origin:{get:function(){var e={"http:":80,"https:":443,"ftp:":21}[this._anchorElement.protocol],t=this._anchorElement.port!=e&&""!==this._anchorElement.port;return this._anchorElement.protocol+"//"+this._anchorElement.hostname+(t?":"+this._anchorElement.port:"")},enumerable:!0},password:{get:function(){return""},set:function(e){},enumerable:!0},username:{get:function(){return""},set:function(e){},enumerable:!0}}),n.createObjectURL=function(e){return t.createObjectURL.apply(t,arguments)},n.revokeObjectURL=function(e){return t.revokeObjectURL.apply(t,arguments)},e.URL=n}(),void 0!==e.location&&!("origin"in e.location)){var t=function(){return e.location.protocol+"//"+e.location.hostname+(e.location.port?":"+e.location.port:"")};try{Object.defineProperty(e.location,"origin",{get:t,enumerable:!0})}catch(n){setInterval((function(){e.location.origin=t()}),100)}}}(void 0!==n.g?n.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:this)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),function(){"use strict";n(344),n(264);var e,t=function(e,t,n){var r={value:t,expiry:(new Date).getTime()+60*n*1e3};localStorage.setItem(e,JSON.stringify(r))},r=function(e){var t=localStorage.getItem(e);if(!t)return null;try{var n=JSON.parse(t);return(new Date).getTime()>n.expiry?(localStorage.removeItem(e),null):n.value}catch(e){}return null},i=new Uint8Array(16);function o(){if(!e&&!(e="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return e(i)}for(var a=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,c=function(e){return"string"==typeof e&&a.test(e)},u=[],s=0;s<256;++s)u.push((s+256).toString(16).substr(1));var p,d=function(e,t,n){var r=(e=e||{}).random||(e.rng||o)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase();if(!c(n))throw TypeError("Stringified UUID is invalid");return n}(r)},l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},l.apply(this,arguments)},v="hrvBeacon_a",f=525600;function h(){return d()}function m(e){var n,r=g(),i=null==r?void 0:r.properties;if(e&&"function"==typeof e)try{n=e(i)}catch(e){}else n=l(l({},i),e);r.properties=n,t(v,p=r,f)}function g(){return p||((p=r(v))&&p.id||(p={id:h()}),t(v,p,f),p)}var y,_="hrvBeacon_s";function b(e){var n=k();if(n){n.events||(n.events=[]);var r=n.events.find((function(t){return t.event==e}));r||(n.events.push({event:e,state:y.track}),t(_,n,30))}}function k(){var e,n,i=r(_),o=!1;if(e=null==i?void 0:i.campaign,!(n=w())||(null==e?void 0:e.utm_source)===(null==n?void 0:n.utm_source)&&(null==e?void 0:e.utm_medium)===(null==n?void 0:n.utm_medium)&&(null==e?void 0:e.utm_campaign)===(null==n?void 0:n.utm_campaign)&&(null==e?void 0:e.utm_term)===(null==n?void 0:n.utm_term)&&(null==e?void 0:e.utm_content)===(null==n?void 0:n.utm_content)||(o=!0),(!i||o)&&(i={sessionId:h(),campaign:w()},o=!0),o){var a={event:"startSession",state:y.track};i.events?i.events.push(a):i.events=[a]}return t(_,i,30),i}function w(){var e=new URLSearchParams(window.location.search),t=e.get("utm_source"),n=e.get("utm_medium"),r=e.get("utm_campaign"),i=e.get("utm_term"),o=e.get("utm_content");if(t||n||r||i||o)return{utm_source:t,utm_medium:n,utm_campaign:r,utm_term:i,utm_content:o}}function T(e,t){if(e.AutoTrack&&e.ga4){var n=window.gtag;if(n||e.enhancedEcommercev4){var r=function(e,t){i(e),function(e,t){var r,i;void 0===t&&(t="add_to_cart");var o=e.properties;e.coupon&&(o.coupon=e.coupon),n("event",t,{currency:(null==o?void 0:o.currency)||"",coupon:(null==o?void 0:o.coupon)||0,items:[{item_id:(null==o?void 0:o.sku)||(null===(r=null==o?void 0:o.variantId)||void 0===r?void 0:r.toString())||(null===(i=null==o?void 0:o.id)||void 0===i?void 0:i.toString())||"",item_name:(null==o?void 0:o.name)||(null==o?void 0:o.title)||"",affiliation:"",coupon:"",item_variant:(null==o?void 0:o.variant)||"",location_id:"",price:(null==o?void 0:o.price)||0,quantity:(null==o?void 0:o.quantity)||0,currency:(null==o?void 0:o.currency)||0}]})}(e,t),a(e)};"product"==t.pageType?function(e){r(e,"view_item")}(t):"ajaxCart"==t.pageType?function(e){r(e)}(t):"ajaxUpdateCart"==t.pageType?function(e){r(e,"remove_from_cart")}(t):"checkout"==t.pageType?t.properties.cartToken?o(t):function(e){var t,r,o=e.products,c=e.properties;i(e);for(var u=[],s=0;s<o.length;s++){var p=o[s],d=(null==p?void 0:p.price)||0;u.push({index:s,item_id:(null==p?void 0:p.sku)||(null===(t=null==p?void 0:p.variantId)||void 0===t?void 0:t.toString())||(null===(r=null==p?void 0:p.id)||void 0===r?void 0:r.toString())||"",item_name:(null==p?void 0:p.name)||"",affiliation:"",coupon:p.coupon||"",discount:p.discount||"",item_variant:(null==p?void 0:p.variant)||"",location_id:"",price:d,quantity:(null==p?void 0:p.quantity)||0,currency:(null==p?void 0:p.currency)||""})}n("event","begin_checkout",{currency:(null==c?void 0:c.currency)||"",value:(null==c?void 0:c.total_price)||0,coupon:(null==c?void 0:c.coupon)||"",items:u}),a(e)}(t):"thankyou"==t.pageType&&(t.properties.cartToken?o(t):function(e){var t,r,o,c,u,s,p,d,l,v,f,h,m=e.properties,g=null===(t=null==m?void 0:m.orderId)||void 0===t?void 0:t.toString(),y=(null==e?void 0:e.products)||[];if(g){i(e);for(var _=[],b=0;b<y.length;b++){var k=(null===(r=y[b])||void 0===r?void 0:r.price)||0,w={item_id:(null===(o=y[b])||void 0===o?void 0:o.sku)||(null===(u=null===(c=y[b])||void 0===c?void 0:c.variantId)||void 0===u?void 0:u.toString())||(null===(p=null===(s=y[b])||void 0===s?void 0:s.id)||void 0===p?void 0:p.toString())||"",item_name:null===(d=y[b])||void 0===d?void 0:d.name,affiliation:"",coupon:(null===(l=y[b])||void 0===l?void 0:l.coupon)||"",discount:(null===(v=y[b])||void 0===v?void 0:v.discount)||"",index:b,item_variant:(null===(f=y[b])||void 0===f?void 0:f.variant)||"",price:k,quantity:(null===(h=y[b])||void 0===h?void 0:h.quantity)||0};_.push(w)}n("event","purchase",{value:(null==m?void 0:m.total_price)||0,currency:(null==m?void 0:m.currency)||"",transaction_id:g,tax:"",shipping:null==m?void 0:m.shipping,coupon:(null==m?void 0:m.coupon)||"",items:_}),a(e)}}(t))}}function i(e){e.options_GA.enhancedEcommerceLoaded||n("set","ec"),n("set","&cu",e.properties.currency)}function o(e){var t,r=e.properties,i={eventAction:r.event,eventCategory:r.category||"All",eventLabel:r.label,eventValue:null===(t=r.total_price)||void 0===t?void 0:t.toString(),nonInteraction:!0};n("event","send",i)}function a(e){var t=e.properties;n("event","send",{eventCategory:t.category||"EnhancedEcommerce",eventAction:t.event,eventLabel:t.label,nonInteraction:!0})}}function E(e,t){if(e.AutoTrack&&e.fb){var n=window.fbq;n&&t&&n("track","AddToCart",{content_ids:[t.properties.id],content_name:t.properties.name,content_type:"product_group",value:t.properties.price,num_items:t.properties.quantity,currency:t.properties.currency})}}function S(e,t,n){e.addEventListener?e.addEventListener(t,n):e.attachEvent&&e.attachEvent("on"+t,n)}function I(e,t){if(e.AutoTrack&&e.tt){var n=window.ttq;n&&t&&n.track("AddToCart",{content_ids:t.properties.id?[t.properties.id]:[],content_type:"product",contents:[{content_id:t.properties.id,content_name:t.properties.name,price:t.properties.price,quantity:t.properties.quantity}],value:t.properties.price,currency:t.properties.currency})}}function A(e,t,n,r,i,o){if(null!=n&&-1!=n.url.indexOf("/cart/add")){if(null!=t&&t.responseJSON){var a=t.responseJSON,c="",u=function(e){for(var t=e.split("&"),n=0;n<t.length;n++){var r=t[n].split("=");if("quantity"===r[0])return parseInt(r[1])}return 1}(n.data);a.variant_options.forEach((function(e){c=c+e+" / "})),r.pageType="ajaxCart";var s=r.properties;s.id=a.product_id,s.variantId=a.variant_id,s.name=a.title+" - "+c.slice(0,-3),s.variant=c.slice(0,-3),s.category="",s.quantity=u,s.price=a.price/100,s.sku=a.sku,s.vendor=a.vendor,s.type="",s.event="Added Product",E(i,r),I(i,r),function(e,t){if(e.AutoTrack&&e.ga){var n=window.ga;(n||e.enhancedEcommerce)&&("product"==t.pageType?function(e){var t=e.properties;r(e),c(t,"detail"),a(e)}(t):"ajaxCart"==t.pageType?function(e){var t=e.properties;r(e),c(t,"add"),a(e)}(t):"ajaxUpdateCart"==t.pageType?function(e){var t=e.properties;r(e),c(t,"remove"),a(e)}(t):"checkout"==t.pageType?t.properties.cartToken?i(t):function(e){var t=e.products;r(e);for(var i=0;i<e.products.length;i++)o(t[i]);n("ec:setAction","checkout",{step:1}),a(e)}(t):"thankyou"==t.pageType&&(t.properties.cartToken?i(t):function(e){var t=e.properties,i=t.total_price.toString()||t.subtotal_price.toString()||"",c=t.orderId.toString(),u=e.products||[];if(c){r(e);for(var s=0;s<u.length;s++)o(u[s]);n("ec:setAction","purchase",{id:c,affiliation:void 0,revenue:i,tax:"",shipping:t.shipping.toString(),coupon:t.coupon}),a(e)}}(t)))}function r(e){var t=e.options_GA;t.enhancedEcommerceLoaded||(n("require","ec"),t.enhancedEcommerceLoaded=!0),n("set","&cu",e.properties.currency)}function i(e){var t=e.properties,r={eventAction:t.event,eventCategory:t.category||"All",eventLabel:t.label,eventValue:t.total_price.toString(),nonInteraction:!0};n("send","event",r)}function o(e){var t={id:e.sku||e.variantId.toString()||e.id.toString(),name:e.name,category:"",quantity:e.quantity,price:e.price.toString(),vendor:e.vendor,variant:e.variant,currency:e.currency};e.coupon&&(t.coupon=e.coupon),n("ec:addProduct",t)}function a(e){var t=e.properties;n("send","event",{eventCategory:t.category||"EnhancedEcommerce",eventAction:t.event,eventLabel:t.label,nonInteraction:!0})}function c(e,t){o(e),n("ec:setAction",t)}}(i,r),T(i,r),o.track("AddToCart",r.properties)}}else(null!=n&&-1!=n.url.indexOf("/cart/update")||null!=n&&-1!=n.url.indexOf("/cart/change")||null!=n&&-1!=n.url.indexOf("/cart/clear"))&&null!=t&&t.responseJSON&&(a=t.responseJSON,r.pageType="ajaxUpdateCart",a&&(a.items&&a.items.length>0&&a.items.forEach((function(e){e.line_price=e.line_price/100,e.line_price_orginal=e.line_price_orginal/100,e.price=e.price/100,e.price_original=e.price_original/100})),a.total_price=a.total_price/100),r.carts=a,E(i,r),I(i,r),T(i,r),o.track("UpdateToCart",r.carts))}!function(e){e[e.track=0]="track",e[e.sent=1]="sent"}(y||(y={}));var P,C=window,L=C.HaravanAnalytics,O={pageType:"",products:[],properties:{arrProductId:"",id:"",variantId:"",name:"",variant:"",category:"",quantity:1,price:0,sku:"",vendor:"",type:"",keySearch:"",coupon:"",orderId:"",shipping:0,subtotal_price:0,total_price:0,cartToken:!1,event:"Action not defined!",currency:null===(P=null==L?void 0:L.meta)||void 0===P?void 0:P.currency},carts:{attributes:{},customer_id:null,items:null,item_count:0,location_id:0,note:"",requires_shipping:!1,total_price:0,total_weight:0},options_GA:{anonymizeIp:!1,doubleClick:!0,enhancedEcommerceLoaded:!1,enhancedEcommerce:(null==L?void 0:L.enhancedEcommerce)||!1,enhancedEcommercev4:(null==L?void 0:L.enhancedEcommercev4)||!1,enhancedLinkAttribution:!1,includeSearch:!0,nonInteraction:!1}};var q=function(){return q=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},q.apply(this,arguments)};function j(e){var n={},i=e.send;e.send=function(e,t){n.properties=q({eventName:e},t)},function(e){L&&function(e){var n,i,o,a,c,u=null===(n=L.meta.page)||void 0===n?void 0:n.pageType;if(u){if(O.pageType=u,"thankyou"==O.pageType)e.userId=null===(o=null===(i=C.Haravan)||void 0===i?void 0:i.checkout)||void 0===o?void 0:o.customer_id;else{var s=null===(c=null===(a=L.meta)||void 0===a?void 0:a.page)||void 0===c?void 0:c.customerId;e.userId=s}var p=new URLSearchParams(window.location.search).get("ref_f4l");switch(p&&m({ref_f4l:p}),O.pageType){case"cart":break;case"product":!function(e,t,n){var r=t.meta.product;if(r){n.properties.id=r.id,r.selected_or_first_available_variant?(n.properties.name=r.selected_or_first_available_variant.title,n.properties.variantId=r.selected_or_first_available_variant.id,n.properties.variant=r.selected_or_first_available_variant.variant_title,n.properties.price=r.selected_or_first_available_variant.price/100,n.properties.sku=r.selected_or_first_available_variant.sku):(n.properties.title=r.title,n.properties.price=r.price/100,n.properties.variant="Default Title"),n.properties.vendor=r.vendor,n.properties.type=r.type,n.properties.event="Viewed Product";var i={product:t.meta.product};e.send("ViewProduct",i)}}(e,L,O);break;case"searchresults":!function(e,t,n){var r=t.meta.search;if(r){n.properties.keySearch=r.query;var i={search:t.meta.search};e.send("Search",i)}}(e,L,O);break;case"thankyou":!function(e,n,i){var o,a,c,u=window.Haravan;if(u&&u.checkout){var s=u.checkout,p=[],d=[],l=0;s.line_items.forEach((function(e){var t=(null==e?void 0:e.price)||0,r={id:e.product_id,variantId:e.variant_id,name:e.title,variant:e.variant_title,category:"",quantity:e.quantity,price:t,sku:e.sku,vendor:e.vendor,coupon:e.coupon||"",discount:e.discount||"",type:"",currency:n.meta.currency};d.push(r),0==p.includes(e.product_id)&&p.push(e.product_id),l+=e.quantity})),i.products=d,i.properties.arrProductId=p,i.properties.coupon=(null===(o=s.discount)||void 0===o?void 0:o.code)||"",i.properties.orderId=s.order_number,i.properties.shipping=(null===(a=s.shipping_rate)||void 0===a?void 0:a.price)||0,i.properties.subtotal_price=s.subtotal_price,i.properties.total_price=s.total_price,i.properties.quantity=l,i.properties.event="Completed Order",r("__hrv_tracked_start_checkout")==s.token?t("__hrv_tracked_start_checkout","",129600):i.properties.cartToken=!0;var v={purchase:u.checkout};(null===(c=null==i?void 0:i.properties)||void 0===c?void 0:c.cartToken)||(b("Purchase"),e.send("Purchase",v))}}(e,L,O);break;case"checkout":!function(e,n,i){var o,a=n.meta.cart,c=n.meta.page;if(a&&c){var u=[],s=[];a.products.forEach((function(e){var t=(null==e?void 0:e.price)||0,r={id:e.productId,variantId:e.variantId,name:e.name,variant:e.variant,category:"",quantity:e.quantity,price:t/100,sku:e.sku,vendor:e.vendor,type:e.type,currency:n.meta.currency,coupon:e.coupon||"",discount:e.discount||""};s.push(r),0==u.includes(e.productId)&&u.push(e.productId)})),i.products=s,i.properties.arrProductId=u,i.properties.quantity=a.item_count,i.properties.total_price=a.total_price/100,i.properties.event="Viewed Checkout",r("__hrv_tracked_start_checkout")!=c.resourceId||""==r("__hrv_tracked_start_checkout")||null==r("__hrv_tracked_start_checkout")||"undefined"==r("__hrv_tracked_start_checkout")?t("__hrv_tracked_start_checkout",c.resourceId,129600):i.properties.cartToken=!0}var p={cart:n.meta.cart};(null===(o=null==i?void 0:i.properties)||void 0===o?void 0:o.cartToken)||(b("InitiateCheckout"),e.send("InitiateCheckout",p))}(e,L,O)}L.ga4&&function(e,t,n){if(t.AutoTrack&&t.ga4){var r=window.gtag;r&&n&&n.options_GA.enhancedEcommercev4&&(n.options_GA.doubleClick&&r("require","displayfeatures"),n.options_GA.enhancedLinkAttribution&&r("require","linkid"),n.options_GA.anonymizeIp&&r("set","anonymizeIp",n.options_GA.anonymizeIp),n.options_GA.enhancedEcommercev4&&T(t,n))}}(0,L,O),L.fb&&function(e,t,n){if(t.AutoTrack&&t.fb){var r=window.fbq;r&&n&&(r("set","autoConfig",!1,t.fb),"product"==n.pageType?r("track","ViewContent",{content_ids:[n.properties.id],content_name:n.properties.name,content_type:"product_group",value:n.properties.price,currency:n.properties.currency}):"searchresults"==n.pageType?r("track","Search",{search_string:n.properties.keySearch}):"thankyou"==n.pageType?(r("track","AddPaymentInfo",{value:n.properties.total_price,currency:n.properties.currency}),r("track","Purchase",{content_ids:n.properties.arrProductId,content_type:"product_group",value:n.properties.total_price,num_items:n.properties.quantity,currency:n.properties.currency},{eventID:n.properties.orderId})):"checkout"!=n.pageType||n.properties.cartToken||r("track","InitiateCheckout",{content_ids:n.properties.arrProductId,content_type:"product_group",value:n.properties.total_price,num_items:n.properties.quantity,currency:n.properties.currency}))}}(0,L,O),L.tt&&function(e,t,n){var r,i,o,a,c;if(t.AutoTrack&&t.tt){var u=window.ttq;if(u&&n){var s=null!==(i=null===(r=n.products)||void 0===r?void 0:r.map((function(e){return{content_id:e.id,content_name:e.name,price:e.price,quantity:e.quantity}})))&&void 0!==i?i:[];"product"==n.pageType?u.track("ViewContent",{content_ids:n.properties.id?[n.properties.id]:[],content_type:"product",contents:[{content_id:n.properties.id,content_name:n.properties.name,price:n.properties.price,quantity:n.properties.quantity}],value:n.properties.price,currency:n.properties.currency}):"searchresults"==n.pageType?u.track("Search",{query:n.properties.keySearch}):"checkout"==n.pageType?u.track("InitiateCheckout",{content_ids:null!==(o=n.properties.arrProductId)&&void 0!==o?o:[],content_type:"product_group",contents:s,value:n.properties.total_price,currency:n.properties.currency}):"thankyou"==n.pageType&&(u.track("AddPaymentInfo",{content_type:"product_group",content_ids:null!==(a=n.properties.arrProductId)&&void 0!==a?a:[],contents:s,value:n.properties.total_price,currency:n.properties.currency}),u.track("PlaceAnOrder",{content_ids:null!==(c=n.properties.arrProductId)&&void 0!==c?c:[],content_type:"product_group",contents:s,value:n.properties.total_price,currency:n.properties.currency},{event_id:n.properties.orderId}))}}}(0,L,O),L.gtag&&function(e,t,n){if(t.AutoTrack&&t.gtag){var r=window.gtag;r&&n&&"thankyou"==n.pageType&&r("event","conversion",{send_to:t.gtag,value:n.properties.total_price,currency:n.properties.currency,transaction_id:n.properties.orderId})}}(0,L,O),function(e){function t(t){var n=(t=t||window.event).target||t.srcElement;if(n&&(n.getAttribute("action")||n.getAttribute("href"))){if(!e.AutoTrack)return;var r=window.ga;r&&r((function(e){var t=e.get("linkerParam");document.cookie="_haravan_ga="+t+"; path=/"}))}}S(window,"load",(function(){for(var e=0;e<document.forms.length;e++){var n=document.forms[e].getAttribute("action");n&&n.indexOf("/cart")>=0&&S(document.forms[e],"submit",t)}for(e=0;e<document.links.length;e++){var r=document.links[e].getAttribute("href");r&&r.indexOf("/checkout")>=0&&S(document.links[e],"click",t)}}))}(L),function(e,t,n){var r,i,o;i="load",o=function(){var r=window.jQuery||window.$;r&&r(document).ajaxSuccess((function(r,i,o){return A(0,i,o,n,t,e)}))},(r=window).addEventListener?r.addEventListener(i,o):r.attachEvent&&r.attachEvent("onload",o)}(e,L,O)}}(e)}(e),e.send=i,e.send("page",n)}var x,R=-1,U=function(e){addEventListener("pageshow",(function(t){t.persisted&&(R=t.timeStamp,e(t))}),!0)},B=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},M=function(){var e=B();return e&&e.activationStart||0},V=function(e,t){var n=B(),r="navigate";return R>=0?r="back-forward-cache":n&&(document.prerendering||M()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},N=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},D=function(e,t,n,r){var i,o;return function(a){t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},G=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},H=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},z=function(e){var t=!1;return function(){t||(e(),t=!0)}},F=-1,J=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},$=function(e){"hidden"===document.visibilityState&&F>-1&&(F="visibilitychange"===e.type?e.timeStamp:0,X())},Q=function(){addEventListener("visibilitychange",$,!0),addEventListener("prerenderingchange",$,!0)},X=function(){removeEventListener("visibilitychange",$,!0),removeEventListener("prerenderingchange",$,!0)},K=function(){return F<0&&(F=J(),Q(),U((function(){setTimeout((function(){F=J(),Q()}),0)}))),{get firstHiddenTime(){return F}}},W=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Y=[1800,3e3],Z=function(e,t){t=t||{},W((function(){var n,r=K(),i=V("FCP"),o=N("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-M(),0),i.entries.push(e),n(!0)))}))}));o&&(n=D(e,i,Y,t.reportAllChanges),U((function(r){i=V("FCP"),n=D(e,i,Y,t.reportAllChanges),G((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},ee=[.1,.25],te=0,ne=1/0,re=0,ie=function(e){e.forEach((function(e){e.interactionId&&(ne=Math.min(ne,e.interactionId),re=Math.max(re,e.interactionId),te=re?(re-ne)/7+1:0)}))},oe=function(){return x?te:performance.interactionCount||0},ae=function(){"interactionCount"in performance||x||(x=N("event",ie,{type:"event",buffered:!0,durationThreshold:0}))},ce=[],ue=new Map,se=0,pe=[],de=function(e){if(pe.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=ce[ce.length-1],n=ue.get(e.interactionId);if(n||ce.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};ue.set(r.id,r),ce.push(r)}ce.sort((function(e,t){return t.latency-e.latency})),ce.length>10&&ce.splice(10).forEach((function(e){return ue.delete(e.id)}))}}},le=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=z(e),"hidden"===document.visibilityState?e():(n=t(e),H(e)),n},ve=[200,500],fe=[2500,4e3],he={},me=[800,1800],ge=function e(t){document.prerendering?W((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},ye=function(e,t){t=t||{};var n=V("TTFB"),r=D(e,n,me,t.reportAllChanges);ge((function(){var i=B();i&&(n.value=Math.max(i.responseStart-M(),0),n.entries=[i],r(!0),U((function(){n=V("TTFB",0),(r=D(e,n,me,t.reportAllChanges))(!0)})))}))},_e=(new Date,window,new Set);function be(e){var t,n=e.name,r=e.value,i=e.rating,o=e.attribution;e.entries,"good"!==i&&(console.debug(e),"LCP"===n&&(t={elm:null==o?void 0:o.element,url:null==o?void 0:o.url})),_e.add({name:n,value:r,rating:i,attribution:t})}function ke(e){addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&function(e){if(_e.size>0){var t={};_e.forEach((function(e){var n=e.name;delete e.name,t[n]=e})),e.send("webVitals",{webVitals:t}),_e.clear()}}(e)})),ye(be),Z(be),function(e,t){t=t||{},Z(z((function(){var n,r=V("CLS",0),i=0,o=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,n())},c=N("layout-shift",a);c&&(n=D(e,r,ee,t.reportAllChanges),H((function(){a(c.takeRecords()),n(!0)})),U((function(){i=0,r=V("CLS",0),n=D(e,r,ee,t.reportAllChanges),G((function(){return n()}))})),setTimeout(n,0))})))}(be),function(e,t){t=t||{},W((function(){var n,r=K(),i=V("LCP"),o=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-M(),0),i.entries=[e],n())}))},a=N("largest-contentful-paint",o);if(a){n=D(e,i,fe,t.reportAllChanges);var c=z((function(){he[i.id]||(o(a.takeRecords()),a.disconnect(),he[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return le(c)}),!0)})),H(c),U((function(r){i=V("LCP"),n=D(e,i,fe,t.reportAllChanges),G((function(){i.value=performance.now()-r.timeStamp,he[i.id]=!0,n(!0)}))}))}}))}(be),function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},W((function(){var n;ae();var r,i=V("INP"),o=function(e){le((function(){e.forEach(de);var t=function(){var e=Math.min(ce.length-1,Math.floor((oe()-se)/50));return ce[e]}();t&&t.latency!==i.value&&(i.value=t.latency,i.entries=t.entries,r())}))},a=N("event",o,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});r=D(e,i,ve,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),H((function(){o(a.takeRecords()),r(!0)})),U((function(){se=oe(),ce.length=0,ue.clear(),i=V("INP"),r=D(e,i,ve,t.reportAllChanges)})))})))}(be)}var we=function(){return we=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},we.apply(this,arguments)},Te=window,Ee=function(e,t){"AddToCart"!==e&&"UpdateToCart"!==e||b("AddToCart");var n={event:e,properties:we({},t)};Se("track",n)},Se=function(e,n){var r=Te.hrvBeacon;if(r.token){var i=g(),o=function(){var e=k(),n=[];if(e.events){var r=e.events.filter((function(e){return e.state==y.track}));r.forEach((function(e){e.state=y.sent,n.push(e.event)})),r.length>0&&t(_,e,30)}return{session:e,events:n}}(),a=o.session,c=o.events,u=we({timestamp:(new Date).getTime(),orgId:r.token,userId:r.userId,anonymousId:r.userAgent.anonymous_id,sessionId:a.sessionId,context:{location:location.href,pageLoadId:r.pageLoadId,userAgent:{browser:r.userAgent.browser,mobile:r.userAgent.mobile,raw:r.userAgent.raw}}},n);i.properties&&0!==Object.keys(i.properties).length&&(u.identities=i.properties),c&&c.length>0&&(u.sessionEvents=c),document.referrer&&(u.context.referrer=document.referrer);var s=we({type:e},u);!function(e,t){var n=JSON.stringify(t);if(!navigator.sendBeacon||!navigator.sendBeacon(e,n)){var r=new XMLHttpRequest;r.open("POST",e),r.send(n)}}("".concat(r.host,"/analytics"),s)}},Ie=function(e){m(e)},Ae=function(e,t,n){if(!Te.hrvBeacon){var r={browserName:(s=navigator.userAgent).match(/chrome|chromium|crios/i)?"chrome":s.match(/firefox|fxios/i)?"firefox":s.match(/safari/i)?"safari":s.match(/opr\//i)?"opera":s.match(/edg/i)?"edge":s.match(/fbios|fban/i)?"facebook":s.match(/zalo/i)?"zalo":"Unknown",isMobile:/Mobile/i.test(navigator.userAgent),userAgent:s},i=r.browserName,o=r.isMobile,a=r.userAgent,c={host:n,token:e,pageLoadId:t,userAgent:{anonymous_id:g().id,browser:i,mobile:o,raw:a},send:Se,track:Ee,identity:Ie};Te.hrvBeacon=c;var u=Te.hrvBeacon;j(u),ke(u)}var s};try{var Pe=document.currentScript,Ce=parseInt(Pe.getAttribute("hrv-beacon-t")),Le=h(),Oe=new URL(Pe.src).origin;Ce||console.error("Haravan web analytics load error, invalid token"),Ae(Ce,Le,Oe),Te.clearBeacon=function(){Te.hrvBeacon=null,Ae(Ce,Le,Oe)}}catch(x){console.log(x)}}()}();
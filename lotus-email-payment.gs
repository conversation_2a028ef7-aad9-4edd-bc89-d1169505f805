/**
 * ============================================================================
 * LOTUS GLASS - EMAIL SYSTEM & PAYMENT INTEGRATION
 * File: lotus-email-payment.gs
 * 
 * Thêm vào cuối lotus-order-management.gs
 * ============================================================================ */

// ============================================================================
// EMAIL SYSTEM
// ============================================================================

/**
 * Send order confirmation email to customer
 * @param {Object} order - Order information
 */
function sendOrderConfirmationEmail(order) {
  try {
    if (!order.customerEmail) {
      Logger.log('No email provided for order:', order.orderId);
      return;
    }
    
    const subject = `Xác nhận đơn hàng #${order.orderId} - Lotus Glass Vietnam`;
    const htmlBody = getOrderConfirmationEmailTemplate(order);
    
    GmailApp.sendEmail(
      order.customerEmail,
      subject,
      '', // Plain text version
      {
        htmlBody: htmlBody,
        name: ORDER_CONFIG.EMAIL.FROM_NAME,
        replyTo: ORDER_CONFIG.EMAIL.REPLY_TO,
        bcc: ORDER_CONFIG.EMAIL.BCC
      }
    );
    
    Logger.log('Order confirmation email sent:', order.orderId, order.customerEmail);
    
  } catch (error) {
    Logger.log('Send confirmation email error:', error);
  }
}

/**
 * Send order status update email
 * @param {string} orderId - Order ID
 * @param {string} status - New status
 * @param {string} customerEmail - Customer email
 */
function sendStatusUpdateEmail(orderId, status, customerEmail) {
  try {
    const statusMessages = {
      'paid': 'Đã thanh toán - Đang xử lý',
      'confirmed': 'Đã xác nhận đơn hàng',
      'processing': 'Đang chuẩn bị hàng',
      'shipped': 'Đã giao cho đơn vị vận chuyển',
      'delivered': 'Đã giao hàng thành công',
      'cancelled': 'Đơn hàng đã bị hủy'
    };
    
    const statusMessage = statusMessages[status] || status;
    const subject = `Cập nhật đơn hàng #${orderId} - ${statusMessage}`;
    const htmlBody = getStatusUpdateEmailTemplate(orderId, status, statusMessage);
    
    GmailApp.sendEmail(
      customerEmail,
      subject,
      '',
      {
        htmlBody: htmlBody,
        name: ORDER_CONFIG.EMAIL.FROM_NAME,
        replyTo: ORDER_CONFIG.EMAIL.REPLY_TO
      }
    );
    
    Logger.log('Status update email sent:', orderId, status, customerEmail);
    
  } catch (error) {
    Logger.log('Send status email error:', error);
  }
}

/**
 * Get order confirmation email template
 * @param {Object} order - Order information
 * @return {string} HTML email template
 */
function getOrderConfirmationEmailTemplate(order) {
  const orderDetails = getOrderDetails(order.orderId);
  
  let itemsHtml = '';
  orderDetails.forEach(item => {
    itemsHtml += `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #eee;">
          <strong>${item.productName}</strong><br>
          ${item.notes ? `<small style="color: #666;">${item.notes}</small><br>` : ''}
          <small>SL: ${item.quantity} x ${formatCurrency(item.unitPrice)}</small>
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #eee; text-align: right;">
          <strong style="color: #f37021;">${formatCurrency(item.totalPrice)}</strong>
        </td>
      </tr>
    `;
  });
  
  const paymentInstructions = getPaymentInstructions(order);
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Xác nhận đơn hàng #${order.orderId}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
      <div style="max-width: 600px; margin: 0 auto; background-color: white;">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #f37021 0%, #d85c15 100%); color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold;">
            🛒 Xác nhận đơn hàng
          </h1>
          <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">
            #${order.orderId}
          </p>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px;">
          <h2 style="color: #333; margin: 0 0 20px 0;">Xin chào ${order.customerName},</h2>
          
          <p style="color: #666; line-height: 1.6; margin: 0 0 25px 0;">
            Cảm ơn bạn đã đặt hàng tại <strong style="color: #f37021;">Lotus Glass Vietnam</strong>. 
            Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.
          </p>
          
          <!-- Order Info -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin: 0 0 15px 0; color: #333;">📋 Thông tin đơn hàng</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Mã đơn hàng:</strong></td>
                <td style="padding: 8px 0; text-align: right; color: #f37021; font-weight: bold;">#${order.orderId}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Ngày đặt:</strong></td>
                <td style="padding: 8px 0; text-align: right;">${new Date(order.orderDate).toLocaleDateString('vi-VN')}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #666;"><strong>Thanh toán:</strong></td>
                <td style="padding: 8px 0; text-align: right;">${getPaymentMethodName(order.paymentMethod)}</td>
              </tr>
            </table>
          </div>
          
          <!-- Order Items -->
          <div style="margin: 25px 0;">
            <h3 style="margin: 0 0 15px 0; color: #333;">📦 Chi tiết đơn hàng</h3>
            <table style="width: 100%; border-collapse: collapse; border: 1px solid #eee;">
              ${itemsHtml}
              
              <!-- Totals -->
              <tr>
                <td style="padding: 12px; border-top: 2px solid #f37021; font-weight: bold;">Tạm tính:</td>
                <td style="padding: 12px; border-top: 2px solid #f37021; text-align: right; font-weight: bold;">${formatCurrency(order.subtotal)}</td>
              </tr>
              ${order.shippingCost > 0 ? `
                <tr>
                  <td style="padding: 12px;">Phí vận chuyển:</td>
                  <td style="padding: 12px; text-align: right;">${formatCurrency(order.shippingCost)}</td>
                </tr>
              ` : `
                <tr>
                  <td style="padding: 12px;">Phí vận chuyển:</td>
                  <td style="padding: 12px; text-align: right; color: #28a745;">Miễn phí</td>
                </tr>
              `}
              ${order.discountAmount > 0 ? `
                <tr>
                  <td style="padding: 12px; color: #28a745;">Giảm giá:</td>
                  <td style="padding: 12px; text-align: right; color: #28a745;">-${formatCurrency(order.discountAmount)}</td>
                </tr>
              ` : ''}
              <tr>
                <td style="padding: 12px; font-size: 18px; font-weight: bold; color: #f37021;">Tổng cộng:</td>
                <td style="padding: 12px; text-align: right; font-size: 18px; font-weight: bold; color: #f37021;">${formatCurrency(order.total)}</td>
              </tr>
            </table>
          </div>
          
          <!-- Shipping Address -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin: 0 0 15px 0; color: #333;">🚚 Địa chỉ giao hàng</h3>
            <p style="margin: 0; line-height: 1.6; color: #666;">
              <strong>${order.customerName}</strong><br>
              📞 ${order.customerPhone}<br>
              📍 ${order.shippingAddress}<br>
              ${order.shippingDistrict}, ${order.shippingProvince}
            </p>
          </div>
          
          ${paymentInstructions}
          
          <!-- Contact Info -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin: 0 0 15px 0; color: #333;">📞 Hỗ trợ khách hàng</h3>
            <p style="margin: 0; line-height: 1.6; color: #666;">
              Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ:<br>
              📞 <strong>Hotline: 0981 500 400</strong><br>
              📧 <strong>Email: <EMAIL></strong><br>
              🌐 <strong>Website: lotusglassvietnam.blogspot.com</strong>
            </p>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin: 25px 0 0 0; text-align: center;">
            Cảm ơn bạn đã tin tưởng và lựa chọn sản phẩm của chúng tôi!<br>
            <strong style="color: #f37021;">Đội ngũ Lotus Glass Vietnam</strong>
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2024 Lotus Glass Vietnam - Thủy tinh cao cấp cho mọi gia đình</p>
          <p style="margin: 5px 0 0 0; opacity: 0.7;">
            Công ty TNHH Pha Lê Việt Tiếp | Email được gửi tự động, vui lòng không reply
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Get status update email template
 * @param {string} orderId - Order ID
 * @param {string} status - Order status
 * @param {string} statusMessage - Status message
 * @return {string} HTML email template
 */
function getStatusUpdateEmailTemplate(orderId, status, statusMessage) {
  const statusIcons = {
    'paid': '💳',
    'confirmed': '✅',
    'processing': '📦',
    'shipped': '🚚',
    'delivered': '🎉',
    'cancelled': '❌'
  };
  
  const icon = statusIcons[status] || '📋';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Cập nhật đơn hàng #${orderId}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
      <div style="max-width: 600px; margin: 0 auto; background-color: white;">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #f37021 0%, #d85c15 100%); color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold;">
            ${icon} Cập nhật đơn hàng
          </h1>
          <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">
            #${orderId}
          </p>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px; text-align: center;">
          <h2 style="color: #333; margin: 0 0 20px 0;">Trạng thái đơn hàng đã được cập nhật</h2>
          
          <div style="background: #f8f9fa; padding: 30px 20px; border-radius: 12px; margin: 25px 0;">
            <div style="font-size: 48px; margin-bottom: 15px;">${icon}</div>
            <h3 style="margin: 0 0 10px 0; color: #f37021; font-size: 20px;">${statusMessage}</h3>
            <p style="margin: 0; color: #666;">Đơn hàng #${orderId}</p>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin: 25px 0;">
            Bạn có thể theo dõi chi tiết đơn hàng tại website của chúng tôi.
          </p>
          
          <div style="margin: 30px 0;">
            <a href="https://lotusglassvietnam.blogspot.com/order-tracking?id=${orderId}" 
               style="display: inline-block; background: #f37021; color: white; padding: 12px 30px; 
                      text-decoration: none; border-radius: 6px; font-weight: bold;">
              Theo dõi đơn hàng
            </a>
          </div>
          
          <!-- Contact Info -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin: 0 0 15px 0; color: #333;">📞 Hỗ trợ khách hàng</h3>
            <p style="margin: 0; line-height: 1.6; color: #666;">
              📞 <strong>Hotline: 0981 500 400</strong><br>
              📧 <strong>Email: <EMAIL></strong>
            </p>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin: 25px 0 0 0;">
            Cảm ơn bạn đã tin tưởng Lotus Glass Vietnam!<br>
            <strong style="color: #f37021;">Đội ngũ Lotus Glass Vietnam</strong>
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2024 Lotus Glass Vietnam</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Get payment instructions based on payment method
 * @param {Object} order - Order information
 * @return {string} Payment instructions HTML
 */
function getPaymentInstructions(order) {
  switch (order.paymentMethod) {
    case 'bank_transfer':
      return `
        <div style="background: #e8f5e8; border: 1px solid #28a745; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin: 0 0 15px 0; color: #28a745;">💳 Hướng dẫn chuyển khoản</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666;"><strong>Ngân hàng:</strong></td>
              <td style="padding: 8px 0;">Vietcombank</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;"><strong>Số tài khoản:</strong></td>
              <td style="padding: 8px 0; font-family: monospace; font-weight: bold;">**********</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;"><strong>Chủ tài khoản:</strong></td>
              <td style="padding: 8px 0;">CONG TY TNHH PHA LE VIET TIEP</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;"><strong>Số tiền:</strong></td>
              <td style="padding: 8px 0; font-weight: bold; color: #f37021;">${formatCurrency(order.total)}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;"><strong>Nội dung:</strong></td>
              <td style="padding: 8px 0; font-family: monospace; font-weight: bold;">LOTUS ${order.orderId}</td>
            </tr>
          </table>
          <p style="margin: 15px 0 0 0; font-size: 14px; color: #666; font-style: italic;">
            ⚠️ Vui lòng chuyển khoản đúng số tiền và nội dung để được xử lý nhanh nhất.
          </p>
        </div>
      `;
      
    case 'cod':
      return `
        <div style="background: #fff3cd; border: 1px solid #ffc107; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin: 0 0 15px 0; color: #856404;">💵 Thanh toán khi nhận hàng (COD)</h3>
          <p style="margin: 0; color: #856404; line-height: 1.6;">
            Bạn sẽ thanh toán <strong>${formatCurrency(order.total)}</strong> 
            bằng tiền mặt khi nhận hàng từ nhân viên giao hàng.
          </p>
          <p style="margin: 10px 0 0 0; font-size: 14px; color: #856404; font-style: italic;">
            💡 Vui lòng chuẩn bị đủ tiền mặt để thanh toán.
          </p>
        </div>
      `;
      
    case 'vnpay':
      return `
        <div style="background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin: 0 0 15px 0; color: #1976d2;">💳 Thanh toán VNPay</h3>
          <p style="margin: 0; color: #1976d2; line-height: 1.6;">
            Bạn sẽ được chuyển hướng đến cổng thanh toán VNPay để hoàn tất thanh toán 
            <strong>${formatCurrency(order.total)}</strong>.
          </p>
          <p style="margin: 10px 0 0 0; font-size: 14px; color: #1976d2; font-style: italic;">
            🔒 Thanh toán an toàn với thẻ ATM, Internet Banking hoặc ví điện tử.
          </p>
        </div>
      `;
      
    default:
      return '';
  }
}

/**
 * Get payment method display name
 * @param {string} method - Payment method code
 * @return {string} Display name
 */
function getPaymentMethodName(method) {
  const names = {
    'vnpay': 'VNPay',
    'bank_transfer': 'Chuyển khoản ngân hàng',
    'cod': 'Thanh toán khi nhận hàng (COD)'
  };
  return names[method] || method;
}

// ============================================================================
// VNPAY PAYMENT INTEGRATION
// ============================================================================

/**
 * Generate VNPay payment URL
 * @param {Object} order - Order information
 * @return {string} VNPay payment URL
 */
function generateVNPayURL(order) {
  try {
    const vnpParams = {
      'vnp_Version': ORDER_CONFIG.VNPAY.VERSION,
      'vnp_Command': ORDER_CONFIG.VNPAY.COMMAND,
      'vnp_TmnCode': ORDER_CONFIG.VNPAY.TMN_CODE,
      'vnp_Locale': ORDER_CONFIG.VNPAY.LOCALE,
      'vnp_CurrCode': ORDER_CONFIG.VNPAY.CURRENCY,
      'vnp_TxnRef': order.orderId,
      'vnp_OrderInfo': `Thanh toan don hang ${order.orderId} - Lotus Glass`,
      'vnp_OrderType': 'other',
      'vnp_Amount': order.total * 100, // VNPay requires amount in VND minor unit
      'vnp_ReturnUrl': ORDER_CONFIG.VNPAY.RETURN_URL,
      'vnp_IpAddr': '127.0.0.1',
      'vnp_CreateDate': formatDate(new Date(), 'yyyyMMddHHmmss')
    };
    
    // Sort parameters
    const sortedParams = {};
    Object.keys(vnpParams).sort().forEach(key => {
      sortedParams[key] = vnpParams[key];
    });
    
    // Create signature string
    const signData = Object.entries(sortedParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    
    // Generate hash (simplified - in production use proper HMAC-SHA512)
    const vnpSecureHash = Utilities.computeHmacSha512Signature(signData, ORDER_CONFIG.VNPAY.HASH_SECRET);
    const hashString = vnpSecureHash.map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2)).join('');
    
    // Build final URL
    const finalUrl = `${ORDER_CONFIG.VNPAY.URL}?${signData}&vnp_SecureHash=${hashString}`;
    
    Logger.log('VNPay URL generated for order:', order.orderId);
    return finalUrl;
    
  } catch (error) {
    Logger.log('Generate VNPay URL error:', error);
    return null;
  }
}

/**
 * Handle VNPay payment callback
 * @param {Object} params - VNPay callback parameters
 * @return {Object} Callback handling result
 */
function handleVNPayCallback(params) {
  try {
    // Verify signature (simplified)
    const orderId = params.vnp_TxnRef;
    const responseCode = params.vnp_ResponseCode;
    const transactionStatus = params.vnp_TransactionStatus;
    
    if (responseCode === '00' && transactionStatus === '00') {
      // Payment successful
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.PAID, 'Thanh toán VNPay thành công');
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.CONFIRMED, 'Đơn hàng đã được xác nhận sau thanh toán');
      
      return createSuccessResponse({
        message: 'Thanh toán thành công',
        orderId: orderId
      });
    } else {
      // Payment failed
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.CANCELLED, `VNPay payment failed: ${responseCode}`);
      
      return createErrorResponse('Thanh toán không thành công');
    }
    
  } catch (error) {
    Logger.log('VNPay callback error:', error);
    return createErrorResponse('Có lỗi xử lý callback thanh toán');
  }
}

/**
 * Format date for VNPay
 * @param {Date} date - Date to format
 * @param {string} format - Format string
 * @return {string} Formatted date
 */
function formatDate(date, format) {
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2);
  const day = ('0' + date.getDate()).slice(-2);
  const hours = ('0' + date.getHours()).slice(-2);
  const minutes = ('0' + date.getMinutes()).slice(-2);
  const seconds = ('0' + date.getSeconds()).slice(-2);
  
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

// ============================================================================
// INTEGRATION INSTRUCTIONS
// ============================================================================

/*
INTEGRATION STEPS FOR PHASE 2:

1. Thêm toàn bộ code này vào cuối file code.gs hiện tại

2. Update doPost function để handle order APIs:

function doPost(e) {
  try {
    const action = e.parameter.action;
    
    // Phase 1: Cart actions
    if (['validate_cart', 'apply_promotion', 'calculate_shipping', 'reserve_stock', 'get_promotions'].includes(action)) {
      return ContentService
        .createTextOutput(JSON.stringify(handleCartAPI(e)))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Phase 2: Order actions  
    if (['create_order', 'get_order_status', 'update_order_status', 'vnpay_callback'].includes(action)) {
      return ContentService
        .createTextOutput(JSON.stringify(handleOrderAPI(e)))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Default response
    return ContentService
      .createTextOutput(JSON.stringify(createErrorResponse('Invalid action')))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    Logger.log('API Error:', error);
    return ContentService
      .createTextOutput(JSON.stringify(createErrorResponse('Server error')))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

3. Configure VNPay settings:
   - Replace YOUR_TMN_CODE và YOUR_HASH_SECRET với actual VNPay credentials
   - Update RETURN_URL để match your domain

4. Configure email settings:
   - Update ORDER_CONFIG.EMAIL với proper from/reply addresses
   - Test email delivery

5. Test order creation:
   - Run testOrderCreation() function
   - Verify data saves to Orders và OrderDetails sheets
   - Test email delivery

*/
<?xml version="1.0" encoding="UTF-8" ?>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.w3.org/1999/xhtml/expr'>
<head>
  <title><data:blog.pageTitle/></title>
  <b:include data='blog' name='all-head-content'/>
  <meta content='width=device-width, initial-scale=1' name='viewport'/>
  <!-- Preconnect to font origins for faster font loading. Using preconnect reduces DNS and TLS negotiation time for third‑party resources【216606448219029†L242-L252】. -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="true"/>
  <!-- Fallback dns-prefetch hints for browsers that do not support preconnect. These hints allow the browser to resolve DNS early without initiating TLS handshakes【743722374146002†L221-L246】. -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com"/>
  <link rel="dns-prefetch" href="//fonts.gstatic.com"/>
  <link href='https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&amp;display=swap' rel='stylesheet'/>

  <!-- Removed preload image to avoid unused resource warning -->
  
  <b:skin><![CDATA[
    /* --- CSS Styles --- */
    :root {
      /* Define brand colors for Lotus Glass. The primary orange color reflects the official logo and will be reused throughout the theme. */
      --brand-color: #f37021;
      --brand-color-dark: #d95e18;
      --brand-color-light: #f7a35c;
      scroll-behavior: smooth;
    }
    body { background: #f8f9fa; color: #212529; font-family: 'Roboto', sans-serif; margin:0; line-height: 1.6; }
    .header-container, .footer-container { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.04); }
    .header-widgets, .footer-widgets { max-width: 1200px; margin: auto; padding: 1rem 1.5rem; }
    .container { max-width: 1200px; margin: auto; padding: 2rem 1.5rem; }
    .filters-toolbar { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem; flex-wrap: wrap; gap: 1rem;}
    .sort-options select { padding: .6rem .8rem; border: 1px solid #ced4da; border-radius: 8px; font-size: .95rem; background-color: #fff; }
    .categories-bar { flex: 1; min-width: 300px; }
    .search-bar { display: flex; margin-bottom: 2rem; }
    .search-bar input { flex:1; padding: .7rem 1.1rem; border-radius: 12px 0 0 12px; border: 1px solid #ced4da; font-size: 1rem; transition: border-color .2s, box-shadow .2s;}
    .search-bar input:focus {
      outline: none;
      border-color: var(--brand-color);
      box-shadow: 0 0 0 2px rgba(243,112,33,0.25);
    }
    .search-bar button {
      border:none;
      background: var(--brand-color);
      color:#fff;
      border-radius:0 12px 12px 0;
      padding:0 1.5rem;
      font-size:1rem;
      cursor:pointer;
      transition: background-color .2s ease;
    }
    .search-bar button:hover {
      background: var(--brand-color-dark);
    }
    .products-list { display: grid; gap: 1.5rem; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); }
    .product-card { background: #fff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); transition: transform .2s ease-in-out, box-shadow .2s ease-in-out; display:flex; flex-direction:column; position:relative; overflow:hidden; cursor:pointer; }
    .product-card:hover { transform: translateY(-5px); box-shadow:0 8px 15px rgba(0,0,0,0.08);}
    .product-card .img-container { width:100%; height:180px; display:flex; align-items:center; justify-content:center; padding: 1rem; box-sizing:border-box;}
    .product-card img { max-width:100%; max-height:100%; object-fit:contain; transition: transform .3s ease; }
    .product-card:hover img { transform: scale(1.05); }
    .product-card .info { padding: 0 1.2rem 1.2rem 1.2rem; text-align: left;}
    .product-name { font-weight:700; font-size:1.05rem; margin: .8rem 0 .4rem 0; line-height:1.4; height: 2.8em; overflow: hidden; color: #212529; }
    .product-price { font-weight:500; color: var(--brand-color); font-size:1.1rem; }
    .price-original { color: #6c757d; text-decoration: line-through; font-size: 0.9em; margin-left: 0.5rem; }
    .discount-badge { position:absolute; top:12px; left:12px; background: #dc3545; color: white; padding: 3px 8px; font-size: .8rem; border-radius: 4px; font-weight:bold;}
    .pagination { display: flex; justify-content: center; align-items: center; gap: .5rem; margin: 2.5rem 0 0 0; }
    .pagination button {
      background:#fff;
      color: var(--brand-color);
      border:1px solid var(--brand-color);
      padding:.5rem 1rem;
      border-radius:5px;
      cursor:pointer;
      font-weight: 500;
      transition: all .2s;
    }
    .pagination button.active,
    .pagination button:hover {
      background: var(--brand-color);
      color:#fff;
    }

    /* Footer styling */
    .site-footer {
      background-color: #fff;
      border-top: 1px solid #e9ecef;
      padding: 2rem 1.5rem;
      margin-top: 3rem;
      color: #343a40;
    }
    .site-footer .footer-inner {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    .site-footer h3 {
      margin: 0 0 .75rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--brand-color);
    }
    .footer-newsletter form {
      display: flex;
      gap: .5rem;
      margin-top: .5rem;
    }
    .footer-newsletter input {
      flex: 1;
      padding: .5rem .75rem;
      border-radius: 6px;
      border: 1px solid #ced4da;
      font-size: .95rem;
    }
    .footer-newsletter button {
      background: var(--brand-color);
      color: #fff;
      border: none;
      padding: .5rem 1rem;
      border-radius: 6px;
      font-size: .95rem;
      cursor: pointer;
      transition: background-color .2s;
    }
    .footer-newsletter button:hover {
      background: var(--brand-color-dark);
    }
    .footer-contact p {
      margin: .3rem 0;
      font-size: .95rem;
    }
    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: .3rem;
    }
    .footer-links li a {
      color: var(--brand-color);
      text-decoration: none;
      font-size: .95rem;
    }
    .footer-links li a:hover {
      text-decoration: underline;
    }
    .loading-placeholder { text-align:center; padding:2rem; color:#6c757d;}
    .skeleton-card { background:#fff; border-radius:12px; overflow: hidden; }
    .skeleton-img { background:#e9ecef; height:180px; }
    .skeleton-info { padding: 1.2rem; }
    .skeleton-text { background:#e9ecef; height:1.2em; margin-top:1rem; border-radius:4px; }
    .skeleton-text.short { width:60%; margin-top: .8rem; height: 1em; }
    @keyframes skeleton-glow { 0% { background-color: #e9ecef; } 50% { background-color: #ced4da; } 100% { background-color: #e9ecef; } }
    .skeleton-img, .skeleton-text { animation: skeleton-glow 1.5s infinite ease-in-out; }
    /* Modal */
    .product-modal { position:fixed; z-index:1000; left:0;top:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,.5); opacity:0; visibility:hidden; transition: opacity .3s ease; }
    .product-modal.show { opacity:1; visibility:visible; }
    .modal-content { background:#fff; border-radius:18px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); max-width: 800px; width: 94vw; display: flex; gap: 2rem; padding: 2rem; position:relative; transform: scale(0.95); transition: transform .3s ease;}

    /*
     * Custom enhancements to align the Blogger theme with the official
     * Lotus Glass website. These styles adjust the top notification bar,
     * hero banner, featured categories slider and responsive breakpoints
     * so the blogspot site better matches the look and feel of
     * https://lotusglass.vn while remaining flexible on all devices.
     */
    /* Top notification bar styling */
    .top-notice {
      background: var(--brand-color);
      color: #fff;
      text-align: center;
      font-size: 0.9rem;
      padding: 0.4rem 1rem;
      font-weight: 500;
    }

    /* Adjust hero section to be more immersive like lotusglass.vn */
    .hero-section {
      padding: 6rem 1rem;
      border-radius: 0;
    }
    .hero-content h1 {
      font-size: 3rem;
    }
    .hero-content p {
      font-size: 1.3rem;
      margin-bottom: 2rem;
    }
    .hero-button {
      padding: 0.8rem 2rem;
      font-size: 1.1rem;
      border-radius: 40px;
    }

    /* Enhance featured categories slider */
    .featured-categories-slider {
      gap: 0;
    }
    .featured-wrapper {
      padding: 1rem 0;
    }
    .featured-card {
      min-width: 280px;
      height: 220px;
      border-radius: 14px;
    }
    .featured-overlay {
      padding: 1rem;
    }
    .featured-overlay h4 {
      font-size: 1.2rem;
      margin: 0 0 .3rem;
      font-weight: 600;
    }
    .featured-overlay p {
      font-size: 0.85rem;
      line-height: 1.3;
    }
    .featured-arrow {
      cursor: pointer;
      font-size: 2rem;
      padding: 0 .5rem;
      color: var(--brand-color);
      user-select: none;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
      .hero-content h1 { font-size: 2.4rem; }
      .hero-content p { font-size: 1.1rem; }
      .featured-card { min-width: 240px; height: 200px; }
    }
    @media (max-width: 768px) {
      .hero-section { padding: 4rem 1rem; }
      .hero-content h1 { font-size: 1.8rem; }
      .hero-content p { font-size: 1rem; }
      .hero-button { padding: 0.6rem 1rem; font-size: 0.95rem; }
      .hero-buttons { flex-direction: column; align-items: center; }
      .hero-features { grid-template-columns: 1fr; gap: 1rem; }
      .feature-item { padding: 1rem; }
      .featured-card { min-width: 200px; height: 160px; }
      .featured-overlay h4 { font-size: 1rem; }
      .featured-overlay p { font-size: 0.75rem; }
      .custom-nav { flex-wrap: wrap; gap: 0.5rem; }
      .custom-header { padding: 0.8rem 1rem; }
      .products-list { grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); }
      .filters-toolbar { flex-direction: column; align-items: stretch; }
      .featured-products-grid { grid-template-columns: 1fr; }
      .blog-posts-grid { grid-template-columns: 1fr; }
      .section-header h2 { font-size: 2rem; }
      .blog-header h1 { font-size: 2rem; }
    }
    @media (max-width: 576px) {
      .products-list { grid-template-columns: 1fr; }
      .hero-section { padding: 3rem 1rem; }
    }
    .product-modal.show .modal-content { transform: scale(1); }
    .modal-close { position:absolute;top:15px;right:20px; font-size:1.8rem; color:#6c757d; cursor:pointer; line-height: 1; transition: color .2s; }
    .modal-close:hover { color: #212529; }
    .modal-image-col { flex: 1; }
    .modal-info-col { flex: 1; }
    .modal-title { font-weight:700; font-size:1.6rem; margin:0; }
    .modal-breadcrumbs { font-size: 0.9em; color: #6c757d; margin: 0.25rem 0 1rem 0; }
    .modal-variants { margin: 1rem 0; }
    .variant-btn { background: #f1f3f5; border: 1px solid #dee2e6; border-radius: 8px; padding: .5rem 1rem; margin-right: .5rem; margin-bottom: .5rem; cursor: pointer; }
    .variant-btn.active {
      background: var(--brand-color);
      color: #fff;
      border-color: var(--brand-color);
    }
    .modal-details-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: .8rem; margin-top: 1rem; }
    .detail-item { background: #f8f9fa; padding: .5rem .8rem; border-radius: 8px; }
    .detail-item strong { color: #495057; }
    .modal-promo { background: #fff3cd; border-left: 4px solid #ffc107; padding: 1rem; border-radius: 8px; color:#664d03; margin:1rem 0;}

    /* Thumbnails in product modal */
    .modal-thumbnails {
      display: flex;
      flex-wrap: wrap;
      gap: .5rem;
      margin-top: .75rem;
    }
    .modal-thumbnail {
      width: 60px;
      height: 60px;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      object-fit: cover;
      cursor: pointer;
      transition: border-color .2s ease;
    }
    .modal-thumbnail.active {
      border-color: var(--brand-color);
    }
    @media (max-width: 768px) { .modal-content { flex-direction: column; max-height: 90vh; overflow-y: auto; } }

    /* --- CSS NÂNG CẤP CHO CÂY DANH MỤC --- */
    .category-filter-header {
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #dee2e6;
    }
    .category-tree {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .category-tree ul {
      list-style: none;
      padding-left: 20px;
      margin-top: 8px;
    }
    .category-tree li {
      margin-bottom: 5px;
    }
    .category-tree-item {
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color .2s ease, color .2s ease;
      display: flex;
      align-items: center;
      gap: 12px;
      color: #343a40;
      text-decoration: none;
      position: relative;
    }
    .category-tree-item-link {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      color: inherit;
      cursor: pointer;
    }
    .category-tree-item:hover {
      background-color: #f1f3f5;
    }
    .category-tree-item.active {
      background-color: var(--brand-color);
      color: #fff;
    }
    .category-tree-image {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 6px;
      flex-shrink: 0;
      background-color: #f8f9fa;
    }
    .category-tree-image-placeholder {
      width: 40px;
      height: 40px;
      flex-shrink: 0;
    }
    .category-tree-content {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
    .category-tree-name {
      font-weight: 500;
    }
    .category-tree-item.active .category-tree-name {
      font-weight: 700;
    }
    .category-tree-description {
      font-size: 0.85em;
      color: #6c757d;
      margin-top: 2px;
      transition: color .2s ease;
    }
    .category-tree-item.active .category-tree-description {
      color: rgba(255, 255, 255, 0.8);
    }
    .category-tree ul ul {
      padding-left: 25px;
    }
    /* Styles for collapsible feature */
    .category-toggle {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color .2s, transform .3s ease;
      flex-shrink: 0;
      margin-left: auto; /* Push toggle to the right */
    }
    .category-toggle:hover {
      background-color: rgba(0,0,0,0.05);
    }
    .category-tree-item.active .category-toggle:hover {
      background-color: rgba(255,255,255,0.1);
    }
    .category-toggle::before {
      content: '▸'; /* Chevron right */
      font-size: 16px;
      font-weight: bold;
      line-height: 1;
      transition: transform 0.3s ease;
    }
    .category-tree li > ul {
      display: none;
    }
    .category-tree li.open > ul {
      display: block;
    }
    .category-tree li.open > .category-tree-item .category-toggle::before {
      transform: rotate(90deg);
    }

    /* === CUSTOM HEADER NAVIGATION === */
    .custom-header {
      background: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      position: sticky;
      top: 0;
      z-index: 101;
    }
    .custom-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .custom-logo img {
      height: 40px;
      width: auto;
      display: block;
    }
    .custom-logo span {
      font-size: 1.6rem;
      font-weight: bold;
      color: var(--brand-color);
    }
    .custom-nav {
      display: flex;
      gap: 1rem;
    }
    .custom-nav .nav-link {
      color: #212529;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 0.8rem;
      border-radius: 6px;
      transition: background-color .2s ease, color .2s ease;
    }
    .custom-nav .nav-link:hover {
      background-color: var(--brand-color);
      color: #fff;
    }

    /* === HEADER SEARCH === */
    .header-action {
      position: relative;
      display: inline-block;
    }

    .header-action_text {
      cursor: pointer;
    }

    .header-action__link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: #333;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .header-action__link:hover {
      color: var(--brand-color);
    }

    .box-icon svg {
      width: 20px;
      height: 20px;
      fill: currentColor;
    }

    .box-icon--close {
      display: none;
    }

    .header-action.active .box-icon svg:not(.box-icon--close svg) {
      display: none;
    }

    .header-action.active .box-icon--close {
      display: block;
    }

    .header-action_dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      min-width: 300px;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
    }

    .header-action.active .header-action_dropdown {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .box-triangle {
      position: absolute;
      top: -8px;
      right: 20px;
      width: 20px;
      height: 9px;
    }

    .box-triangle svg {
      width: 100%;
      height: 100%;
      fill: white;
    }

    .header-dropdown_content {
      padding: 20px;
    }

    .ttbold {
      font-weight: 600;
      margin-bottom: 15px;
      color: #333;
    }

    .search-box {
      position: relative;
    }

    .searchform {
      display: flex;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      overflow: hidden;
    }

    .wpo-search-inner {
      flex: 1;
    }

    .searchinput {
      width: 100%;
      padding: 12px 15px;
      border: none;
      outline: none;
      font-size: 14px;
    }

    .btn-search {
      background: var(--brand-color);
      border: none;
      padding: 12px 15px;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .btn-search:hover {
      background: var(--brand-color-dark);
    }

    .btn-search svg {
      width: 16px;
      height: 16px;
      fill: white;
    }

    .ajaxSearchResults {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #e0e0e0;
      border-top: none;
      border-radius: 0 0 6px 6px;
      max-height: 300px;
      overflow-y: auto;
      z-index: 1001;
    }

    .search-result-item {
      display: flex;
      align-items: center;
      padding: 12px 15px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .search-result-item:hover {
      background: #f8f9fa;
    }

    .search-result-item:last-child {
      border-bottom: none;
    }

    .search-result-item img {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 6px;
      margin-right: 12px;
    }

    .search-result-info h4 {
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: #333;
    }

    .search-result-price {
      font-size: 13px;
      color: var(--brand-color);
      font-weight: 600;
      margin: 0;
    }

    .search-no-results {
      padding: 20px 15px;
      text-align: center;
      color: #6c757d;
      font-style: italic;
    }

    /* === ACCOUNT DROPDOWN === */
    .site_account_header {
      margin-bottom: 20px;
    }

    .site_account_title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .site_account_legend {
      font-size: 0.9rem;
      color: #6c757d;
      margin: 0;
    }

    .form__input-wrapper {
      position: relative;
      margin-bottom: 20px;
    }

    .form__field {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.3s ease;
    }

    .form__field:focus {
      border-color: var(--brand-color);
    }

    .form__floating-label {
      position: absolute;
      top: 12px;
      left: 15px;
      font-size: 14px;
      color: #6c757d;
      pointer-events: none;
      transition: all 0.3s ease;
    }

    .form__field:focus + .form__floating-label,
    .form__field:not(:placeholder-shown) + .form__floating-label {
      top: -8px;
      left: 10px;
      font-size: 12px;
      background: white;
      padding: 0 5px;
      color: var(--brand-color);
    }

    .form__submit {
      width: 100%;
      padding: 12px 20px;
      background: var(--brand-color);
      color: white;
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.3s ease;
      margin-bottom: 15px;
    }

    .form__submit:hover {
      background: var(--brand-color-dark);
    }

    .site_account_secondary-action {
      text-align: center;
    }

    .site_account_secondary-action p {
      margin: 8px 0;
      font-size: 0.9rem;
      color: #6c757d;
    }

    .js-link {
      background: none;
      border: none;
      color: var(--brand-color);
      text-decoration: underline;
      cursor: pointer;
      font-size: inherit;
    }

    .js-link:hover {
      color: var(--brand-color-dark);
    }

    /* === STATIC PAGES === */
    .static-page-section {
      padding: 3rem 0;
      max-width: 1200px;
      margin: 0 auto;
    }

    .static-page-content {
      padding: 0 2rem;
    }

    .about-page h1,
    .news-page h1,
    .default-static-page h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--brand-color);
      margin-bottom: 2rem;
      text-align: center;
    }

    .about-intro {
      text-align: center;
      margin-bottom: 3rem;
    }

    .about-intro h2 {
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
    }

    .about-intro p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: #6c757d;
      max-width: 800px;
      margin: 0 auto;
    }

    .about-values {
      margin-top: 3rem;
    }

    .about-values h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 2rem;
      text-align: center;
      color: #333;
    }

    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .value-item {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .value-item:hover {
      transform: translateY(-5px);
    }

    .value-item h4 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--brand-color);
    }

    .value-item p {
      color: #6c757d;
      line-height: 1.5;
    }

    .news-page p {
      text-align: center;
      font-size: 1.1rem;
      color: #6c757d;
      margin-bottom: 3rem;
    }

    .default-static-page {
      max-width: 800px;
      margin: 0 auto;
    }

    .page-content {
      font-size: 1rem;
      line-height: 1.6;
      color: #333;
    }

    .page-content h2,
    .page-content h3,
    .page-content h4 {
      color: var(--brand-color);
      margin-top: 2rem;
      margin-bottom: 1rem;
    }

    .page-content p {
      margin-bottom: 1rem;
    }

    .page-content ul,
    .page-content ol {
      margin-bottom: 1rem;
      padding-left: 2rem;
    }

    .page-content li {
      margin-bottom: 0.5rem;
    }

    /* === FEATURED CATEGORIES SLIDER === */
    .featured-categories-slider {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 2rem;
    }
    .featured-wrapper {
      display: flex;
      overflow-x: auto;
      scroll-behavior: smooth;
      gap: 1rem;
      flex: 1;
    }
    .featured-card {
      min-width: 200px;
      height: 180px;
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      cursor: pointer;
      flex-shrink: 0;
    }
    .featured-card img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .featured-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.5rem 0.8rem;
      background: linear-gradient(0deg, rgba(0,0,0,0.7), rgba(0,0,0,0));
      color: #fff;
    }
    .featured-overlay h4 {
      margin: 0;
      font-size: 1rem;
      font-weight: 700;
    }
    .featured-overlay p {
      margin: 0.2rem 0 0 0;
      font-size: 0.75rem;
    }
    .featured-arrow {
      width: 32px;
      height: 32px;
      background: var(--brand-color);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
      flex-shrink: 0;
      user-select: none;
      transition: background-color .2s ease;
    }
    .featured-arrow:hover {
      background: var(--brand-color-dark);
    }

    /* Highlight card khi danh mục cha được chọn */
    .featured-card.active {
      outline: 3px solid var(--brand-color);
    }

    /* Subcategory chips styles */
    .subcategory-filter {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    .subcategory-chip {
      background: #f1f3f5;
      color: #212529;
      padding: 0.4rem 0.8rem;
      border-radius: 20px;
      cursor: pointer;
      user-select: none;
      transition: background-color .2s ease, color .2s ease;
    }
    .subcategory-chip:hover {
      background-color: #e2e6ea;
    }
    .subcategory-chip.active {
      background-color: var(--brand-color);
      color: #fff;
    }

    /* === HERO SECTION === */
    .hero-section {
      position: relative;
      background: linear-gradient(135deg, var(--brand-color) 0%, #e67e22 100%);
      color: #fff;
      padding: 4rem 1rem;
      margin-bottom: 2rem;
      border-radius: 12px;
      overflow: hidden;
    }
    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
      z-index: 0;
    }
    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    .hero-content h1 {
      font-size: 2.4rem;
      margin-bottom: 0.5rem;
      font-weight: 700;
      line-height: 1.3;
    }
    .hero-content p {
      font-size: 1.1rem;
      margin-bottom: 1.5rem;
    }
    .hero-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 3rem;
    }

    .hero-button {
      display: inline-block;
      padding: 1rem 2rem;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .hero-button.primary {
      background: white;
      color: var(--brand-color);
    }

    .hero-button.secondary {
      background: transparent;
      color: white;
      border-color: white;
    }

    .hero-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .hero-button.secondary:hover {
      background: white;
      color: var(--brand-color);
    }

    .hero-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
      position: relative;
      z-index: 2;
    }

    .feature-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .feature-item h3 {
      font-size: 1.2rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .feature-item p {
      font-size: 0.9rem;
      opacity: 0.9;
      margin: 0;
    }

    /* === NEW PRODUCTS SECTION === */
    .new-products-section {
      margin: 3rem 0;
      padding: 2rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
    }

    .new-products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    /* === FEATURED PRODUCTS SECTION === */
    .featured-products-section {
      margin: 3rem 0;
      padding: 2rem 0;
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .section-header h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--brand-color);
      margin-bottom: 1rem;
    }

    .section-header p {
      font-size: 1.1rem;
      color: #6c757d;
      max-width: 600px;
      margin: 0 auto;
    }

    .featured-products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .featured-product-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      position: relative;
    }

    .featured-product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .featured-product-image {
      position: relative;
      height: 250px;
      overflow: hidden;
    }

    .featured-product-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .featured-product-card:hover .featured-product-image img {
      transform: scale(1.05);
    }

    .featured-product-badge {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: var(--brand-color);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .featured-product-badge.new-badge {
      background: #28a745;
    }

    .featured-product-info {
      padding: 1.5rem;
    }

    .featured-product-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .featured-product-price {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--brand-color);
      margin-bottom: 1rem;
    }

    .featured-product-description {
      color: #6c757d;
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
    }

    .featured-product-card .add-to-cart-btn {
      width: 100%;
      padding: 12px 20px;
      background: var(--brand-color);
      color: white;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .featured-product-card .add-to-cart-btn:hover {
      background: var(--brand-color-dark);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(243, 112, 33, 0.3);
    }

    .featured-product-card .add-to-cart-btn svg {
      width: 16px;
      height: 16px;
    }

    /* === BLOG SECTION === */
    .blog-section {
      padding: 3rem 0;
      max-width: 1200px;
      margin: 0 auto;
    }

    .blog-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .blog-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--brand-color);
      margin-bottom: 1rem;
    }

    .blog-header p {
      font-size: 1.1rem;
      color: #6c757d;
      max-width: 600px;
      margin: 0 auto;
    }

    .blog-posts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }

    .blog-post-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;
    }

    .blog-post-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .blog-post-image {
      position: relative;
      height: 200px;
      overflow: hidden;
    }

    .blog-post-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .blog-post-card:hover .blog-post-image img {
      transform: scale(1.05);
    }

    .blog-post-content {
      padding: 1.5rem;
    }

    .blog-post-meta {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
      font-size: 0.85rem;
    }

    .blog-post-date {
      color: #6c757d;
    }

    .blog-post-category {
      background: var(--brand-color);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-weight: 500;
    }

    .blog-post-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
      line-height: 1.4;
    }

    .blog-post-excerpt {
      color: #6c757d;
      font-size: 0.95rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .blog-post-link {
      color: var(--brand-color);
      text-decoration: none;
      font-weight: 600;
      font-size: 0.9rem;
      transition: color 0.3s ease;
    }

    .blog-post-link:hover {
      color: var(--brand-color-dark);
      text-decoration: underline;
    }
    :root {
    --cart-primary: #f37021;
    --cart-primary-dark: #d85c15;
    --cart-secondary: #6c757d;
    --cart-success: #28a745;
    --cart-warning: #ffc107;
    --cart-danger: #dc3545;
    --cart-light: #f8f9fa;
    --cart-dark: #343a40;
    --cart-border: #dee2e6;
    --cart-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --cart-shadow-lg: 0 4px 20px rgba(0,0,0,0.15);
    --cart-radius: 8px;
    --cart-transition: all 0.3s ease;
  }

  /* === CART BADGE === */
  .cart-badge {
    position: relative;
    cursor: pointer;
    padding: 8px 12px;
    color: var(--cart-primary);
    transition: var(--cart-transition);
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: var(--cart-radius);
    user-select: none;
  }

  .cart-badge:hover {
    color: var(--cart-primary-dark);
    background: rgba(243, 112, 33, 0.1);
    transform: translateY(-1px);
  }

  .cart-badge svg {
    width: 24px;
    height: 24px;
    transition: var(--cart-transition);
  }

  .cart-badge:hover svg {
    transform: scale(1.1);
  }

  .cart-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--cart-danger);
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 700;
    transform: scale(0);
    transition: var(--cart-transition);
    border: 2px solid white;
    box-shadow: var(--cart-shadow);
  }

  .cart-count.has-items {
    transform: scale(1);
    animation: cartBounce 0.6s ease;
  }

  @keyframes cartBounce {
    0% { transform: scale(0); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
  }

  /* === CART SIDEBAR === */
  .cart-sidebar {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    max-width: 100vw;
    height: 100vh;
    background: white;
    box-shadow: var(--cart-shadow-lg);
    z-index: 1000;
    transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .cart-sidebar.active {
    right: 0;
  }

  .cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.6);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--cart-transition);
    backdrop-filter: blur(2px);
  }

  .cart-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  body.cart-open {
    overflow: hidden;
  }

  /* === CART HEADER === */
  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--cart-border);
    background: white;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .cart-header h3 {
    margin: 0;
    color: var(--cart-dark);
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .cart-header h3::before {
    content: '🛒';
    font-size: 18px;
  }

  .cart-close {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: var(--cart-secondary);
    padding: 8px;
    line-height: 1;
    border-radius: var(--cart-radius);
    transition: var(--cart-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
  }

  .cart-close:hover {
    color: var(--cart-dark);
    background: var(--cart-light);
    transform: rotate(90deg);
  }

  /* === CART ITEMS === */
  .cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--cart-border) transparent;
  }

  .cart-items::-webkit-scrollbar {
    width: 6px;
  }

  .cart-items::-webkit-scrollbar-track {
    background: transparent;
  }

  .cart-items::-webkit-scrollbar-thumb {
    background: var(--cart-border);
    border-radius: 3px;
  }

  .cart-empty {
    text-align: center;
    padding: 60px 24px;
    color: var(--cart-secondary);
  }

  .cart-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    opacity: 0.3;
    fill: currentColor;
  }

  .cart-empty h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--cart-dark);
  }

  .cart-empty p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
  }

  .cart-item {
    display: flex;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid var(--cart-light);
    transition: var(--cart-transition);
    position: relative;
  }

  .cart-item:hover {
    background: rgba(243, 112, 33, 0.02);
  }

  .cart-item:last-child {
    border-bottom: none;
  }

  .item-image {
    width: 70px;
    height: 70px;
    flex-shrink: 0;
    border-radius: var(--cart-radius);
    overflow: hidden;
    background: var(--cart-light);
    position: relative;
  }

  .item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--cart-transition);
  }

  .item-image:hover img {
    transform: scale(1.05);
  }

  .item-image::after {
    content: '';
    position: absolute;
    inset: 0;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: var(--cart-radius);
  }

  .item-details {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .item-name {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    line-height: 1.4;
    color: var(--cart-dark);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .item-variant {
    margin: 0;
    font-size: 13px;
    color: var(--cart-secondary);
    font-weight: 500;
  }

  .item-price {
    margin: 4px 0 0 0;
    font-size: 15px;
    color: var(--cart-primary);
    font-weight: 600;
  }

  .item-controls {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12px;
    flex-shrink: 0;
  }

  .quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--cart-light);
    border-radius: var(--cart-radius);
    padding: 4px;
    border: 1px solid var(--cart-border);
  }

  .qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 600;
    color: var(--cart-primary);
    transition: var(--cart-transition);
    user-select: none;
  }

  .qty-btn:hover:not(:disabled) {
    background: var(--cart-primary);
    color: white;
    transform: scale(1.1);
  }

  .qty-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .qty-input {
    width: 45px;
    height: 32px;
    text-align: center;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 600;
    color: var(--cart-dark);
    outline: none;
  }

  .item-total {
    font-size: 15px;
    font-weight: 700;
    color: var(--cart-primary);
    text-align: right;
  }

  .remove-item {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--cart-secondary);
    padding: 6px;
    border-radius: var(--cart-radius);
    transition: var(--cart-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
  }

  .remove-item:hover {
    color: var(--cart-danger);
    background: rgba(220, 53, 69, 0.1);
    transform: scale(1.1);
  }

  .remove-item svg {
    width: 16px;
    height: 16px;
  }

  /* === CART FOOTER === */
  .cart-footer {
    border-top: 1px solid var(--cart-border);
    padding: 24px;
    background: white;
    position: sticky;
    bottom: 0;
  }

  .cart-totals {
    margin-bottom: 20px;
  }

  .subtotal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: var(--cart-dark);
    margin-bottom: 8px;
  }

  .subtotal-amount {
    color: var(--cart-primary);
    font-size: 20px;
    font-weight: 700;
  }

  .shipping-note {
    font-size: 12px;
    color: var(--cart-secondary);
    font-style: italic;
    text-align: center;
    padding: 8px 12px;
    background: rgba(243, 112, 33, 0.05);
    border-radius: var(--cart-radius);
    border: 1px dashed rgba(243, 112, 33, 0.2);
  }

  .cart-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .cart-btn {
    padding: 14px 20px;
    border: none;
    border-radius: var(--cart-radius);
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--cart-transition);
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
  }

  .cart-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
  }

  .cart-btn:hover::before {
    width: 300px;
    height: 300px;
  }

  .cart-btn-primary {
    background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
    color: white;
    box-shadow: var(--cart-shadow);
  }

  .cart-btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--cart-shadow-lg);
  }

  .cart-btn-primary:disabled {
    background: var(--cart-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .cart-btn-secondary {
    background: white;
    color: var(--cart-primary);
    border: 2px solid var(--cart-primary);
  }

  .cart-btn-secondary:hover {
    background: var(--cart-primary);
    color: white;
    transform: translateY(-1px);
  }

  /* === ADD TO CART BUTTONS === */
  .add-to-cart-btn {
    background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
    color: white;
    border: none;
    padding: 12px 18px;
    border-radius: var(--cart-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--cart-transition);
    margin-top: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
  }

  .add-to-cart-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
  }

  .add-to-cart-btn:hover::before {
    left: 100%;
  }

  .add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--cart-shadow-lg);
  }

  .add-to-cart-btn:active {
    transform: translateY(0);
  }

  .add-to-cart-btn.loading {
    pointer-events: none;
    opacity: 0.8;
  }

  .add-to-cart-btn.success {
    background: var(--cart-success);
    transform: scale(0.95);
  }

  /* === CART NOTIFICATION === */
  .cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
    color: white;
    padding: 16px 24px;
    border-radius: var(--cart-radius);
    box-shadow: var(--cart-shadow-lg);
    z-index: 1001;
    transform: translateX(120%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 350px;
    min-width: 280px;
  }

  .cart-notification.show {
    transform: translateX(0);
  }

  .notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    animation: notificationPulse 0.6s ease;
  }

  @keyframes notificationPulse {
    0% { transform: scale(0.5) rotate(-45deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(0deg); opacity: 1; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
  }

  .notification-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
  }

  /* === MOBILE RESPONSIVE === */
  @media (max-width: 768px) {
    .cart-sidebar {
      width: 100vw;
      right: -100vw;
    }
    
    .cart-sidebar.active {
      right: 0;
    }
    
    .cart-header {
      padding: 16px 20px;
    }
    
    .cart-header h3 {
      font-size: 18px;
    }
    
    .cart-item {
      padding: 16px 20px;
      gap: 12px;
    }
    
    .item-image {
      width: 60px;
      height: 60px;
    }
    
    .item-name {
      font-size: 14px;
    }
    
    .cart-footer {
      padding: 20px;
    }
    
    .cart-notification {
      top: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
      transform: translateY(-120%);
    }
    
    .cart-notification.show {
      transform: translateY(0);
    }
  }

  @media (max-width: 480px) {
    .cart-badge {
      padding: 6px 8px;
    }
    
    .cart-badge svg {
      width: 20px;
      height: 20px;
    }
    
    .cart-count {
      width: 18px;
      height: 18px;
      font-size: 10px;
      top: -1px;
      right: -1px;
    }
    
    .cart-item {
      padding: 12px 16px;
    }
    
    .item-image {
      width: 50px;
      height: 50px;
    }
    
    .cart-header {
      padding: 12px 16px;
    }
    
    .cart-footer {
      padding: 16px;
    }
  }

  /* === LOADING STATES === */
  .cart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--cart-secondary);
  }

  .cart-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--cart-light);
    border-top-color: var(--cart-primary);
    border-radius: 50%;
    animation: cartSpin 1s linear infinite;
    margin-right: 12px;
  }

  @keyframes cartSpin {
    to { transform: rotate(360deg); }
  }

  /* === ANIMATIONS === */
  .cart-item-enter {
    animation: cartItemSlideIn 0.3s ease;
  }

  @keyframes cartItemSlideIn {
    from {
      opacity: 0;
      transform: translateX(100%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .cart-item-exit {
    animation: cartItemSlideOut 0.3s ease forwards;
  }

  @keyframes cartItemSlideOut {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-100%);
    }
  }

  /* === ACCESSIBILITY === */
  .cart-badge:focus,
  .cart-btn:focus,
  .add-to-cart-btn:focus,
  .qty-btn:focus,
  .cart-close:focus {
    outline: 2px solid var(--cart-primary);
    outline-offset: 2px;
  }

  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* ============================================================================
     LOTUS GLASS - PHASE 2 CHECKOUT SYSTEM STYLES
     ============================================================================ */

  /* === CHECKOUT MODAL STYLES === */
  .checkout-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.8);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
  }

  .checkout-modal.active {
    opacity: 1;
    visibility: visible;
  }

  .checkout-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .checkout-modal.active .checkout-container {
    transform: translate(-50%, -50%) scale(1);
  }

  .checkout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
  }

  .checkout-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #212529;
    margin: 0;
  }

  .checkout-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .checkout-close:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
  }

  .checkout-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    display: flex;
  }

  /* === CHECKOUT STEPS === */
  .checkout-steps {
    display: flex;
    justify-content: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e9ecef;
  }

  .step {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .step.active {
    color: var(--cart-primary);
  }

  .step.completed {
    color: var(--cart-success);
  }

  .step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 8px;
  }

  .step.active .step-number {
    background: var(--cart-primary);
    color: white;
  }

  .step.completed .step-number {
    background: var(--cart-success);
    color: white;
  }

  .step:not(:last-child)::after {
    content: '';
    width: 40px;
    height: 2px;
    background: #e9ecef;
    margin: 0 16px;
  }

  .step.completed:not(:last-child)::after {
    background: var(--cart-success);
  }

  /* === CHECKOUT CONTENT === */
  .checkout-content {
    display: flex;
    flex: 1;
    min-height: 500px;
  }

  .checkout-form {
    flex: 2;
    padding: 24px;
    overflow-y: auto;
  }

  .checkout-summary {
    flex: 1;
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    padding: 24px;
    overflow-y: auto;
  }

  /* === FORM STYLES === */
  .form-section {
    margin-bottom: 32px;
  }

  .form-section:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #212529;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .section-title svg {
    width: 20px;
    height: 20px;
    color: var(--cart-primary);
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-row {
    display: flex;
    gap: 16px;
  }

  .form-row .form-group {
    flex: 1;
  }

  .form-label {
    display: block;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
    font-size: 0.9rem;
  }

  .form-label.required::after {
    content: ' *';
    color: #dc3545;
  }

  .form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--cart-primary);
    box-shadow: 0 0 0 3px rgba(243, 112, 33, 0.1);
  }

  .form-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  }

  .form-input.success {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
  }

  .form-error {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 4px;
    display: none;
  }

  .form-error.show {
    display: block;
  }

  .form-textarea {
    min-height: 80px;
    resize: vertical;
  }

  .form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
  }

  /* === PAYMENT METHODS === */
  .payment-methods {
    display: grid;
    gap: 12px;
  }

  .payment-method {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
  }

  .payment-method:hover {
    border-color: var(--cart-primary);
    background: rgba(243, 112, 33, 0.02);
  }

  .payment-method.selected {
    border-color: var(--cart-primary);
    background: rgba(243, 112, 33, 0.05);
  }

  .payment-method-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  .payment-radio {
    width: 18px;
    height: 18px;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
  }

  .payment-method.selected .payment-radio {
    border-color: var(--cart-primary);
  }

  .payment-method.selected .payment-radio::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--cart-primary);
    border-radius: 50%;
  }

  .payment-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
  }

  .payment-icon svg {
    width: 20px;
    height: 20px;
  }

  .payment-info h4 {
    margin: 0 0 4px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #212529;
  }

  .payment-info p {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
  }

  .payment-details {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
    display: none;
  }

  .payment-method.selected .payment-details {
    display: block;
  }

  /* === ORDER SUMMARY === */
  .order-summary {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
  }

  .summary-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin: 0 0 16px 0;
  }

  .summary-items {
    margin-bottom: 16px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
  }

  .summary-item:last-child {
    border-bottom: none;
  }

  .item-info {
    flex: 1;
  }

  .item-name {
    font-weight: 500;
    color: #212529;
    margin: 0 0 2px 0;
    font-size: 0.9rem;
  }

  .item-details {
    font-size: 0.8rem;
    color: #6c757d;
  }

  .item-price {
    font-weight: 600;
    color: var(--cart-primary);
  }

  .summary-totals {
    border-top: 2px solid #e9ecef;
    padding-top: 16px;
  }

  .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .total-row:last-child {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
  }

  .total-label {
    color: #6c757d;
  }

  .total-value {
    font-weight: 500;
  }

  /* === CHECKOUT ACTIONS === */
  .checkout-actions {
    display: flex;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
  }

  .checkout-btn {
    flex: 1;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .checkout-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .checkout-btn-secondary {
    background: white;
    color: #6c757d;
    border: 2px solid #e9ecef;
  }

  .checkout-btn-secondary:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #6c757d;
  }

  .checkout-btn-primary {
    background: var(--cart-primary);
    color: white;
  }

  .checkout-btn-primary:hover:not(:disabled) {
    background: var(--cart-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(243, 112, 33, 0.3);
  }

  /* === LOADING STATES === */
  .checkout-loading {
    display: none;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
  }

  .checkout-loading.show {
    display: flex;
  }

  .checkout-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e9ecef;
    border-top: 3px solid var(--cart-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* === SUCCESS/ERROR MODALS === */
  .result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.8);
    z-index: 2100;
    display: none;
    align-items: center;
    justify-content: center;
  }

  .result-modal.show {
    display: flex;
  }

  .result-content {
    background: white;
    border-radius: 12px;
    padding: 32px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  }

  .result-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .result-icon.success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
  }

  .result-icon.error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  .result-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  .result-message {
    color: #6c757d;
    margin: 0 0 24px 0;
    line-height: 1.5;
  }

  /* === MOBILE RESPONSIVE === */
  @media (max-width: 768px) {
    .checkout-container {
      width: 100%;
      height: 100vh;
      max-height: 100vh;
      border-radius: 0;
      transform: translate(-50%, -50%);
    }

    .checkout-modal.active .checkout-container {
      transform: translate(-50%, -50%);
    }

    .checkout-content {
      flex-direction: column;
    }

    .checkout-form {
      order: 2;
    }

    .checkout-summary {
      order: 1;
      border-left: none;
      border-bottom: 1px solid #e9ecef;
      max-height: 200px;
    }

    .checkout-steps {
      padding: 12px 16px;
    }

    .step {
      font-size: 0.8rem;
    }

    .step-number {
      width: 20px;
      height: 20px;
      font-size: 0.7rem;
    }

    .step:not(:last-child)::after {
      width: 20px;
      margin: 0 8px;
    }

    .form-row {
      flex-direction: column;
      gap: 0;
    }

    .checkout-actions {
      flex-direction: column;
    }

    .checkout-btn {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .checkout-header {
      padding: 16px 20px;
    }

    .checkout-title {
      font-size: 1.3rem;
    }

    .checkout-form,
    .checkout-summary {
      padding: 16px;
    }

    .section-title {
      font-size: 1.1rem;
    }

    .form-input {
      padding: 10px 12px;
    }

    .payment-method {
      padding: 12px;
    }

    .checkout-actions {
      padding: 16px 20px;
    }
  }

  /* === ACCESSIBILITY === */
  .checkout-modal:focus-within {
    outline: none;
  }

  .form-input:focus,
  .checkout-btn:focus,
  .payment-method:focus {
    outline: 2px solid var(--cart-primary);
    outline-offset: 2px;
  }

  .checkout-close:focus {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
  }

  /* === ANIMATIONS === */
  .checkout-modal {
    animation-duration: 0.3s;
    animation-fill-mode: both;
  }

  .checkout-modal.active {
    animation-name: fadeIn;
  }

  .checkout-container {
    animation-duration: 0.3s;
    animation-fill-mode: both;
  }

  .checkout-modal.active .checkout-container {
    animation-name: slideIn;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translate(-50%, -50%) scale(0.9);
    }
    to {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  /* === PRINT STYLES === */
  @media print {
    .checkout-modal {
      position: static;
      background: white;
    }

    .checkout-container {
      position: static;
      transform: none;
      box-shadow: none;
      max-width: none;
      max-height: none;
    }

    .checkout-header,
    .checkout-actions {
      display: none;
    }

    .checkout-content {
      flex-direction: column;
    }

    .checkout-summary {
      border: 1px solid #000;
      margin-top: 20px;
    }
  }

  ]]>
  </b:skin>
</head>
<body>
  <!-- Top notification bar like the official site: displays a brief welcome or disclaimer message. -->
  <div class="top-notice">Website Chính Thức Và Duy Nhất Của Thương Hiệu Lotus Glass.</div>
  <div class='header-container'>
    <b:section class='header-widgets' id='header' maxwidgets='1' showaddelement='yes'/>
  </div>

  <!-- Custom sticky header with navigation -->
  <header class="custom-header">
    <!-- Brand logo: using official Lotus Glass logo for a consistent identity -->
    <div class="custom-logo">
      <img src="https://file.hstatic.net/200000605565/file/logo_lotus_glass_final_copy-01_5cfb8511188f400c8a985246fc22760c.png" alt="Lotus Glass logo" loading="lazy" />
      <span>Lotus Glass</span>
    </div>
    <nav class="custom-nav">
      <a href="/" class="nav-link">Trang chủ</a>
      <!-- Link to the dedicated product page instead of using a placeholder. -->
      <a href="/p/san-pham.html" class="nav-link">Sản phẩm</a>
      <a href="/p/gioi-thieu.html" class="nav-link">Giới thiệu</a>
      <!-- Link to the news (Tin tức) static page that aggregates Blogger posts. -->
      <a href="/p/tin-tuc.html" class="nav-link">Tin tức</a>

      <!-- Search Icon -->
      <div class="header-action header-action_search">
        <div class="header-action_text">
          <a class="header-action__link header-action-toggle" href="javascript:void(0)" id="site-search-handle" aria-label="Tìm kiếm" title="Tìm kiếm">
            <span class="box-icon">
              <svg class="svg-ico-search" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 27" style="enable-background:new 0 0 24 27;" xml:space="preserve">
                <path d="M10,2C4.5,2,0,6.5,0,12s4.5,10,10,10s10-4.5,10-10S15.5,2,10,2z M10,19c-3.9,0-7-3.1-7-7s3.1-7,7-7s7,3.1,7,7S13.9,19,10,19z"></path>
                <rect x="17" y="17" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -9.2844 19.5856)" width="4" height="8"></rect>
              </svg>
              <span class="box-icon--close">
                <svg viewBox="0 0 19 19" role="presentation">
                  <path d="M9.1923882 8.39339828l7.7781745-7.7781746 1.4142136 1.********-7.7781746 7.******** 7.7781746 7.77817456L16.9705627 19l-7.7781745-7.7781746L1.******** 19 0 17.5857864l7.7781746-7.77817456L0 2.******** 1.********.******** 9.1923882 8.39339828z" fill="currentColor" fill-rule="evenodd"></path>
                </svg>
              </span>
            </span>
          </a>
        </div>
        <div class="header-action_dropdown">
          <span class="box-triangle">
            <svg viewBox="0 0 20 9" role="presentation">
              <path d="M.******** 9c.2694725-.********.********-.********.********-.89986354C3.******** 6.******** 5.******** 3.******** 9.2467995.30653888c.4145057-.4095171 1.0844277-.******** 1.4977971.00205122L19.4935156 9H.********z" fill="#ffffff"></path>
            </svg>
          </span>
          <div class="header-dropdown_content">
            <p class="ttbold">Tìm kiếm</p>
            <div class="site_search">
              <div class="search-box wpo-wrapper-search">
                <form class="searchform searchform-categoris ultimate-search">
                  <div class="wpo-search-inner">
                    <input required="" id="inputSearchAuto" name="q" maxlength="40" autocomplete="off" class="searchinput input-search search-input" type="text" size="20" placeholder="Tìm kiếm sản phẩm..." aria-label="Search"/>
                  </div>
                  <button type="submit" class="btn-search btn" id="search-header-btn" aria-label="Tìm kiếm">
                    <svg version="1.1" class="svg search" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 27" style="enable-background:new 0 0 24 27;" xml:space="preserve">
                      <path d="M10,2C4.5,2,0,6.5,0,12s4.5,10,10,10s10-4.5,10-10S15.5,2,10,2z M10,19c-3.9,0-7-3.1-7-7s3.1-7,7-7s7,3.1,7,7S13.9,19,10,19z"></path>
                      <rect x="17" y="17" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -9.2844 19.5856)" width="4" height="8"></rect>
                    </svg>
                  </button>
                </form>
                <div id="ajaxSearchResults" class="smart-search-wrapper ajaxSearchResults" style="display: none">
                  <div class="resultsContent"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Account/Login -->
      <div class="header-action header-action_account">
        <div class="header-action_text">
          <a class="header-action__link header-action-toggle" href="javascript:void(0)" id="site-account-handle" aria-label="Tài khoản" title="Tài khoản">
            <span class="box-icon">
              <svg class="svg-ico-account" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 510 510" style="enable-background:new 0 0 510 510;" xml:space="preserve">
                <g><g id="account-circle">
                  <path d="M255,0C114.75,0,0,114.75,0,255s114.75,255,255,255s255-114.75,255-255S395.25,0,255,0z M255,76.5
                           c43.35,0,76.5,33.15,76.5,76.5s-33.15,76.5-76.5,76.5c-43.35,0-76.5-33.15-76.5-76.5S211.65,76.5,255,76.5z M255,438.6
                           c-63.75,0-119.85-33.149-153-81.6c0-51,102-79.05,153-79.05S408,306,408,357C374.85,405.45,318.75,438.6,255,438.6z"></path>
                </g></g>
              </svg>
              <span class="box-icon--close">
                <svg viewBox="0 0 19 19" role="presentation">
                  <path d="M9.1923882 8.39339828l7.7781745-7.7781746 1.4142136 1.********-7.7781746 7.******** 7.7781746 7.77817456L16.9705627 19l-7.7781745-7.7781746L1.******** 19 0 17.5857864l7.7781746-7.77817456L0 2.******** 1.********.******** 9.1923882 8.39339828z" fill="currentColor" fill-rule="evenodd"></path>
                </svg>
              </span>
            </span>
          </a>
        </div>
        <div class="header-action_dropdown">
          <span class="box-triangle">
            <svg viewBox="0 0 20 9" role="presentation">
              <path d="M.******** 9c.2694725-.********.********-.********.********-.89986354C3.******** 6.******** 5.******** 3.******** 9.2467995.30653888c.4145057-.4095171 1.0844277-.******** 1.4977971.00205122L19.4935156 9H.********z" fill="#ffffff"></path>
            </svg>
          </span>
          <div class="header-dropdown_content">
            <div class="site_account" id="siteNav-account">
              <div class="site_account_panel_list">
                <div id="header-login-panel" class="site_account_panel site_account_default is-selected">
                  <header class="site_account_header">
                    <h2 class="site_account_title heading">Đăng nhập tài khoản</h2>
                    <p class="site_account_legend">Nhập email và mật khẩu của bạn:</p>
                  </header>
                  <div class="site_account_inner">
                    <form id="customer_login" class="login-form">
                      <div class="form__input-wrapper form__input-wrapper--labelled">
                        <input type="email" id="login-customer-email" class="form__field form__field--text" name="email" required="required"/>
                        <label for="login-customer-email" class="form__floating-label">Email</label>
                      </div>
                      <div class="form__input-wrapper form__input-wrapper--labelled">
                        <input type="password" id="login-customer-password" class="form__field form__field--text" name="password" required="required" autocomplete="current-password"/>
                        <label for="login-customer-password" class="form__floating-label">Mật khẩu</label>
                      </div>
                      <button type="submit" class="form__submit button dark" id="form_submit-login">Đăng nhập</button>
                    </form>
                    <div class="site_account_secondary-action">
                      <p>Khách hàng mới?
                        <button class="js-link link" id="show-register">Tạo tài khoản</button>
                      </p>
                      <p>Quên mật khẩu?
                        <button class="js-link link" id="show-recover">Khôi phục mật khẩu</button>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Shopping Cart Badge -->
      <div id="cart-badge" class="cart-badge" onclick="LotusCart.openCart()" role="button" aria-label="Mở giỏ hàng">
        <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
        </svg>
        <span id="cart-count" class="cart-count" aria-live="polite">0</span>
      </div>
    </nav>
  </header>

  <main class='container' id='main-content'>
    <!-- Hero section introducing the brand. This banner uses a semi-transparent overlay and highlights the brand message. -->
    <!-- Only render the hero banner on the homepage -->
    <b:if cond='data:blog.url == data:blog.homepageUrl'>
    <section class="hero-section">
      <div class="hero-content">
        <h1>Lotus Glass – Thủy tinh cao cấp</h1>
        <p>Nâng tầm mỗi khoảnh khắc với những sản phẩm tinh tế và sang trọng.</p>
        <div class="hero-buttons">
          <a href="#products" class="hero-button primary" id="heroExplore">Khám phá bộ sưu tập</a>
          <a href="/p/gioi-thieu.html" class="hero-button secondary">Về chúng tôi</a>
        </div>
      </div>
      <div class="hero-features">
        <div class="feature-item">
          <div class="feature-icon">🏆</div>
          <h3>Chất lượng cao cấp</h3>
          <p>Sản phẩm thủy tinh được chế tác từ nguyên liệu cao cấp</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🚚</div>
          <h3>Giao hàng toàn quốc</h3>
          <p>Miễn phí vận chuyển cho đơn hàng từ 500.000đ</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">💎</div>
          <h3>Thiết kế độc đáo</h3>
          <p>Mỗi sản phẩm đều mang dấu ấn riêng biệt</p>
        </div>
      </div>
    </section>
    <div class='search-bar'>
      <input id='searchInput' placeholder='Tìm sản phẩm theo tên hoặc mã SKU...' type='text' aria-label='Tìm kiếm sản phẩm'/>
      <button id='searchButton' aria-label='Tìm kiếm'>Tìm kiếm</button>
    </div>

    <!-- New Products Section -->
    <section class="new-products-section" id="new-products">
      <div class="section-header">
        <h2>Sản phẩm mới</h2>
        <p>Khám phá những sản phẩm mới nhất từ Lotus Glass</p>
      </div>
      <div class="new-products-grid" id="newProductsGrid">
        <!-- New products will be loaded here -->
      </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-products-section" id="products">
      <div class="section-header">
        <h2>Sản phẩm nổi bật</h2>
        <p>Khám phá những sản phẩm thủy tinh cao cấp được yêu thích nhất</p>
      </div>
      <div class="featured-products-grid" id="featuredProductsGrid">
        <!-- Featured products will be loaded here -->
      </div>
    </section>

    </b:if>

    <!-- Products page content - only show on dedicated products page -->
    <b:if cond='data:blog.pageName == "Sản phẩm"'>
    <!-- Search is now in header, no need for duplicate search bar here -->

    <!-- Slider hiển thị danh mục nổi bật -->
    <div id="featuredCategoriesSlider" class="featured-categories-slider"></div>
    <div class='filters-toolbar'>
      <div class='categories-bar' id='categoriesBar'>
        <!-- Cây danh mục sẽ được render vào đây bởi JavaScript -->
      </div>
      <div class='sort-options'>
        <label for='sortSelect' style='margin-right: .5rem; font-weight: 500;'>Sắp xếp:</label>
        <select id='sortSelect' aria-label='Sắp xếp sản phẩm'>
          <option value=''>Mặc định</option>
          <option value='price_asc'>Giá tăng dần</option>
          <option value='price_desc'>Giá giảm dần</option>
        </select>
      </div>
    </div>
    <div id='productsContainer'>
      <div class='products-list' id='productsList'/>
      <div class='loading-placeholder' id='loadingBox' style='display:none;'/>
      <div class='pagination' id='paginationBar'/>
      <!-- Sentinel element used for infinite scroll. When this element enters the viewport,
           additional pages will be loaded automatically. -->
      <div id='infiniteSentinel' style='height:1px'></div>
    </div>
    </b:if>

    <!-- Blog Posts Layout for News Page -->
    <b:if cond='data:blog.url != data:blog.homepageUrl and data:blog.pageName != "Sản phẩm" and data:blog.pageType != "static_page"'>
    <section class="blog-section">
      <div class="blog-header">
        <h1>Tin tức &amp; Cập nhật</h1>
        <p>Khám phá những tin tức mới nhất về sản phẩm và ngành thủy tinh</p>
      </div>

      <div class="blog-posts-grid" id="blogPostsGrid">
        <!-- Real Blogger posts -->
        <b:loop values='data:posts' var='post'>
        <article class="blog-post-card">
          <div class="blog-post-image">
            <b:if cond='data:post.featuredImage'>
              <img expr:src='data:post.featuredImage.isResizable ? resizeImage(data:post.featuredImage, 400, "1:1") : data:post.featuredImage' expr:alt='data:post.title' loading="lazy"/>
            <b:else/>
              <img src="https://phaleviettiep.com/wp-content/uploads/2025/05/Do-thuy-tinh-gia-dung-scaled.jpg" expr:alt='data:post.title' loading="lazy"/>
            </b:if>
          </div>
          <div class="blog-post-content">
            <div class="blog-post-meta">
              <span class="blog-post-date"><data:post.date/></span>
              <b:if cond='data:post.labels'>
                <span class="blog-post-category"><data:post.labels.first.name/></span>
              </b:if>
            </div>
            <h3 class="blog-post-title"><data:post.title/></h3>
            <p class="blog-post-excerpt">
              <b:if cond='data:post.snippet'>
                <data:post.snippet/>
              <b:else/>
                <b:eval expr='data:post.body snippet { length: 150, links: false, linebreaks: false, ellipsis: true }'/>
              </b:if>
            </p>
            <a expr:href='data:post.url' class="blog-post-link">Đọc thêm</a>
          </div>
        </article>
        </b:loop>
      </div>
    </section>
    </b:if>

    <!-- Static Pages Content -->
    <b:if cond='data:blog.pageType == "static_page"'>
    <section class="static-page-section">
      <div class="static-page-content">
        <b:if cond='data:blog.pageName == "Giới thiệu"'>
          <div class="about-page">
            <h1>Về Lotus Glass</h1>
            <div class="about-content">
              <div class="about-intro">
                <h2>Chào mừng đến với Lotus Glass</h2>
                <p>Lotus Glass là thương hiệu hàng đầu trong lĩnh vực sản xuất và phân phối các sản phẩm thủy tinh cao cấp tại Việt Nam. Với hơn 10 năm kinh nghiệm, chúng tôi tự hào mang đến cho khách hàng những sản phẩm chất lượng nhất.</p>
              </div>

              <div class="about-values">
                <h3>Giá trị cốt lõi</h3>
                <div class="values-grid">
                  <div class="value-item">
                    <h4>🏆 Chất lượng</h4>
                    <p>Cam kết sử dụng nguyên liệu cao cấp và công nghệ tiên tiến</p>
                  </div>
                  <div class="value-item">
                    <h4>💎 Thiết kế</h4>
                    <p>Sản phẩm với thiết kế độc đáo, tinh tế và sang trọng</p>
                  </div>
                  <div class="value-item">
                    <h4>🤝 Dịch vụ</h4>
                    <p>Phục vụ khách hàng tận tâm với chế độ bảo hành uy tín</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </b:if>

        <b:if cond='data:blog.pageName == "Tin tức"'>
          <div class="news-page">
            <h1>Tin tức &amp; Cập nhật</h1>
            <p>Theo dõi những tin tức mới nhất từ Lotus Glass</p>
            <!-- This will show blog posts -->
            <div class="blog-posts-grid">
              <b:loop values='data:posts' var='post'>
              <article class="blog-post-card">
                <div class="blog-post-image">
                  <b:if cond='data:post.featuredImage'>
                    <img expr:src='data:post.featuredImage.isResizable ? resizeImage(data:post.featuredImage, 400, "1:1") : data:post.featuredImage' expr:alt='data:post.title' loading="lazy"/>
                  <b:else/>
                    <img src="https://phaleviettiep.com/wp-content/uploads/2025/05/Do-thuy-tinh-gia-dung-scaled.jpg" expr:alt='data:post.title' loading="lazy"/>
                  </b:if>
                </div>
                <div class="blog-post-content">
                  <div class="blog-post-meta">
                    <span class="blog-post-date"><data:post.date/></span>
                    <b:if cond='data:post.labels'>
                      <span class="blog-post-category"><data:post.labels.first.name/></span>
                    </b:if>
                  </div>
                  <h3 class="blog-post-title"><data:post.title/></h3>
                  <p class="blog-post-excerpt">
                    <b:if cond='data:post.snippet'>
                      <data:post.snippet/>
                    <b:else/>
                      <b:eval expr='data:post.body snippet { length: 150, links: false, linebreaks: false, ellipsis: true }'/>
                    </b:if>
                  </p>
                  <a expr:href='data:post.url' class="blog-post-link">Đọc thêm</a>
                </div>
              </article>
              </b:loop>
            </div>
          </div>
        </b:if>

        <!-- Default static page content -->
        <b:if cond='data:blog.pageName != "Giới thiệu" and data:blog.pageName != "Tin tức"'>
          <div class="default-static-page">
            <h1><data:blog.pageName/></h1>
            <div class="page-content">
              <!-- Page content will be inserted by Blogger -->
            </div>
          </div>
        </b:if>
      </div>
    </section>
    </b:if>
  </main>
  
  <!-- Static footer: newsletter signup, contact information and policy links -->
  <footer class="site-footer">
    <div class="footer-inner">
      <div class="footer-newsletter">
        <h3>Đăng ký nhận tin</h3>
        <form id="subscribeForm" onsubmit="subscribeNewsletter(event)">
          <input type="email" id="newsletterEmail" placeholder="Email của bạn" required="required" />
          <button type="submit">Đăng ký</button>
        </form>
        <p id="newsletterSuccessMsg" style="display:none;margin-top:.5rem;color:var(--brand-color);font-weight:500;">Cảm ơn bạn đã đăng ký!</p>
      </div>
      <div class="footer-contact">
        <p><strong>Hỗ trợ / Mua hàng:</strong> 0981 500 400</p>
        <p><strong>Địa chỉ:</strong> Lô N-9A, đường số 6, KCN Long Hậu mở rộng, Ấp 3, Xã Cần Giuộc, Tỉnh Tây Ninh</p>
        <p><strong>Điện thoại:</strong> 0938 141 966</p>
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Giờ làm việc:</strong> 8:00 - 17:30 (Thứ 2 - Thứ 7)</p>
      </div>
      <ul class="footer-links">
        <li><a href="/p/chinh-sach-doi-tra.html">Chính sách đổi trả</a></li>
        <li><a href="/p/chinh-sach-bao-mat.html">Chính sách bảo mật</a></li>
        <li><a href="/p/dieu-khoan-dich-vu.html">Điều khoản dịch vụ</a></li>
      </ul>
    </div>
  </footer>

  <div class='product-modal' id='productModal' onclick='closeModal(event)' role='dialog' aria-modal='true'/>
  
  <template id='modalTemplate'>
    <div class='modal-content' onclick='event.stopPropagation()'>
      <button class='modal-close' onclick='closeModal(event)' aria-label='Đóng'>&#215;</button>
      <div class='modal-image-col'>
        <img id='modalImg' src='' alt='Hình ảnh sản phẩm chi tiết' style='width:100%;border-radius:12px;object-fit:contain;'/>
        <!-- Thumbnail strip for showing multiple images of the selected variant. Thumbnails are clickable
             and update the main image when selected. -->
        <div class='modal-thumbnails' id='modalThumbnails'></div>
      </div>
      <div class='modal-info-col'>
        <div class='modal-breadcrumbs' id='modalBreadcrumbs'></div>
        <h3 class='modal-title' id='modalName'></h3>
        <div class='modal-variants' id='modalVariants'></div>
        <div class='modal-details-grid' id='modalDetailsGrid'></div>
        <div class='modal-promo' id='modalPromo'></div>
        <p class='modal-desc' id='modalDesc'></p>
      </div>
    </div>
  </template>

  <b:section class='hidden' id='hidden-data' showaddelement='no' style='display:none'>
    <b:widget id='HTML100' locked='true' title='Theme Config' type='HTML' visible='true'>
      <b:includable id='main'>
        <!-- SCRIPT CẤU HÌNH ĐÃ ĐƯỢC DI CHUYỂN RA NGOÀI WIDGET NÀY -->
      </b:includable>
    </b:widget>
    <b:widget id='Blog1' locked='true' type='Blog' visible='false'>
      <b:includable id='main'></b:includable>
    </b:widget>
  </b:section>

  <!-- SCRIPT CẤU HÌNH - Đặt ở đây để đảm bảo tải trước script chính -->
  <script>
    window.THEME_CONFIG = {
      API_URL: "https://script.google.com/macros/s/AKfycbzKMKloNLg4AzzqCc4CPPXhCALge38LprQKeM242qDicVf8fflpRslAGKWbFb8uOlGBYA/exec",
      PAGE_SIZE: 12
    };
  </script>

  <!-- SCRIPT LOGIC CHÍNH - PHIÊN BẢN HỖ TRỢ DANH MỤC ĐA CẤP VÀ HÌNH ẢNH -->
  <script>
  //<![CDATA[
    const CONFIG = window.THEME_CONFIG;

    // Danh sách danh mục cố định được nhúng trực tiếp trong template.
    // Điều này cho phép hiển thị danh mục ngay cả khi API gặp lỗi.
    const DEFAULT_CATEGORIES = [
      {"CategoryID":"CTG-001","TenDanhMuc":"Đồ Uống","DanhMucChaID":"NULL","MoTa":"Khám phá bộ sưu tập ly thủy tinh cao cấp đa dạng cho mọi thức uống. Từ ly giải khát đến ly rượu sang trọng,","HinhAnh":"https://file.hstatic.net/200000605565/file/untitled_design__2__fc4d5f5f4cdd44b1af38ca10050f2be3.png","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-002","TenDanhMuc":"Cốc Giải Khát","DanhMucChaID":"CTG-001","MoTa":"Chọn ngay những mẫu cốc thủy tinh giải khát đẹp, bền. Từ cốc thấp tiện dụng đến cốc cao sành điệu, tô điểm mọi khoảnh khắc.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2023/04/z4280581792498_19ab1c7e007c70061abfbacd8dca0a22.jpg","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-003","TenDanhMuc":"Cốc Giải Khát Thấp","DanhMucChaID":"CTG-002","MoTa":"Tìm cốc thủy tinh thấp lý tưởng cho nước lọc, nước ép, sinh tố. Thiết kế nhỏ gọn, dễ cầm, bền đẹp, tô điểm bàn ăn hàng ngày.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2023/01/VT-Drinkkkk-1400x788.jpg","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-004","TenDanhMuc":"Cốc Giải Khát Cao","DanhMucChaID":"CTG-002","MoTa":"Sở hữu cốc thủy tinh cao phong cách cho trà đá, nước ngọt, cocktail. Thiết kế thanh lịch, sang trọng, mang lại trải nghiệm uống thú vị.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2023/01/VT-COFFEE-1400x787.jpg","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-005","TenDanhMuc":"Ly Quai","DanhMucChaID":"CTG-001","MoTa":"Khám phá các mẫu ly thủy tinh có quai tiện dụng, bền đẹp. Lý tưởng cho trà, cà phê, nước trái cây, mang lại sự thoải mái tối đa.","HinhAnh":"https://file.hstatic.net/200000605565/file/beer_ce34ab2b13484d31b39ec658de902d87.png","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-006","TenDanhMuc":"Ly Rượu","DanhMucChaID":"CTG-001","MoTa":"Bộ sưu tập ly rượu thủy tinh và pha lê cao cấp. Từ ly vang đến ly cocktail sang trọng, nâng tầm mỗi bữa tiệc của bạn.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2023/01/VT-COCKTAIL-1400x788.jpg","ThuTuHienThi":3,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-007","TenDanhMuc":"Ly Trà Nóng","DanhMucChaID":"CTG-001","MoTa":"Chọn ly thủy tinh chịu nhiệt cho trà nóng, cà phê. Thiết kế tinh tế, giữ nhiệt hiệu quả, an toàn và sang trọng cho khoảnh khắc thư giãn.","HinhAnh":"https://file.hstatic.net/200000605565/file/coffee___tea_2dbdc118ce8a4963868e280f73fbfde2.png","ThuTuHienThi":4,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-008","TenDanhMuc":"Ly Cà Phê Nóng","DanhMucChaID":"CTG-001","MoTa":"Sở hữu ly thủy tinh cà phê chịu nhiệt, giữ ấm hoàn hảo. Thiết kế đẹp, bền, giúp bạn tận hưởng trọn vẹn hương vị cà phê mỗi ngày.","HinhAnh":"https://product.hstatic.net/200000605565/product/vtc412-2_503b347aa058495780e748e7a943a800_master.jpg","ThuTuHienThi":5,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-009","TenDanhMuc":"Đồ Dùng Bàn Ăn","DanhMucChaID":"NULL","MoTa":"Khám phá bộ đồ dùng bàn ăn thủy tinh sang trọng, bền đẹp. Từ tô, bát, chén đến dĩa thủy tinh, hoàn hảo cho mọi bữa ăn.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2025/05/Do-thuy-tinh-gia-dung-scaled.jpg","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-010","TenDanhMuc":"Tô","DanhMucChaID":"CTG-009","MoTa":"Tìm tô thủy tinh đa năng, bền đẹp cho mọi món ăn. Lý tưởng đựng canh, súp, salad hay các món ăn cần trưng bày đẹp mắt.","HinhAnh":"https://phaleviettiep.com/wp-content/uploads/2022/12/Tableware.webp","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-011","TenDanhMuc":"Bát","DanhMucChaID":"CTG-009","MoTa":"Sở hữu bát thủy tinh ăn cơm, đựng thức ăn nhỏ gọn, tiện lợi. Bền đẹp, an toàn, tô điểm thêm vẻ sang trọng cho bữa cơm.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-012","TenDanhMuc":"Chén","DanhMucChaID":"CTG-009","MoTa":"Chọn chén thủy tinh nhỏ xinh, lý tưởng đựng nước chấm, gia vị. Thiết kế tinh xảo, bền đẹp, tăng thêm sự tiện nghi cho bữa ăn.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":3,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-013","TenDanhMuc":"Dĩa","DanhMucChaID":"CTG-009","MoTa":"Tìm dĩa thủy tinh các loại: dĩa ăn, dĩa salad, dĩa trái cây. Thiết kế sang trọng, bền đẹp, lý tưởng cho mọi bữa tiệc và bữa ăn hàng ngày.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":4,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-014","TenDanhMuc":"Đồ Chứa/ Trang Trí","DanhMucChaID":"NULL","MoTa":"Khám phá bình, hũ, lọ thủy tinh độc đáo để chứa đựng và trang trí. Thiết kế tinh xảo, đa năng, tô điểm không gian sống của bạn.","HinhAnh":"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR4MtoAj802i6uhjHFMNgmEjmYLbpxQw2vpJQ&s","ThuTuHienThi":3,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-015","TenDanhMuc":"Bình","DanhMucChaID":"CTG-014","MoTa":"Chọn bình thủy tinh đựng nước, cắm hoa, hay trang trí. Thiết kế đa dạng, chất liệu bền đẹp, làm đẹp không gian sống và bàn ăn.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-016","TenDanhMuc":"Hũ","DanhMucChaID":"CTG-014","MoTa":"Sở hữu hũ thủy tinh chất lượng cao để bảo quản thực phẩm, gia vị, bánh kẹo. Kín hơi, an toàn, giữ độ tươi ngon lâu hơn.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-017","TenDanhMuc":"Lọ","DanhMucChaID":"CTG-014","MoTa":"Tìm lọ thủy tinh nhỏ xinh để đựng tinh dầu, gia vị, hay dùng trang trí. Kiểu dáng đa dạng, mang lại vẻ đẹp độc đáo.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":3,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-018","TenDanhMuc":"Bộ Sản Phẩm","DanhMucChaID":"NULL","MoTa":"Khám phá các bộ sản phẩm thủy tinh hoàn chỉnh: bộ đồ uống, bộ bát đĩa, bộ quà tặng. Lý tưởng cho gia đình và quà biếu.","HinhAnh":"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT6_Wy6XyFy2Rqijwu5eDfjfyh7kGGSFgJNmw&s","ThuTuHienThi":4,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-019","TenDanhMuc":"Bộ Quà Tặng","DanhMucChaID":"CTG-018","MoTa":"Chọn bộ quà tặng thủy tinh độc đáo, sang trọng và ý nghĩa. Món quà hoàn hảo cho mọi dịp, thể hiện sự tinh tế của người tặng.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":1,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-020","TenDanhMuc":"Bộ Đồ Uống","DanhMucChaID":"CTG-018","MoTa":"Sở hữu bộ đồ uống thủy tinh cao cấp, đa dạng kiểu dáng. Lý tưởng cho gia đình, nhà hàng, quán bar, mang lại trải nghiệm chuyên nghiệp.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":2,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"},
      {"CategoryID":"CTG-021","TenDanhMuc":"Bộ Bát Đĩa","DanhMucChaID":"CTG-018","MoTa":"Khám phá bộ bát đĩa thủy tinh sang trọng, hiện đại. Bền đẹp, an toàn, tô điểm bàn ăn thêm phần tinh tế, ấm cúng.","HinhAnh":"https://file.hstatic.net/200000605565/file/phong_cach_chau_au_trong_gia_dinh_viet_d8513bfc3021447aa84e1bb56471c1cf.png","ThuTuHienThi":3,"TrangThai":true,"NgayTao":"2025-07-09T17:00:00.000Z","NgayCapNhat":"2025-07-09T17:00:00.000Z"}
    ];
    const state = { topCategory: '', category: '', page: 1, sort: '', query: '' };

    // Global variables for infinite scroll. totalPagesGlobal keeps track of the total number
    // of pages available from the API. isFetchingMore prevents duplicate requests when
    // the sentinel enters the viewport multiple times. infiniteObserver holds the
    // IntersectionObserver instance used for the infinite scroll sentinel.
    let totalPagesGlobal = 1;
    let isFetchingMore = false;
    let infiniteObserver = null;
    let fetchController = new AbortController();

    function buildCategoryTree(categories) {
      const categoryMap = {};
      categories.forEach(cat => {
        categoryMap[cat.CategoryID] = { ...cat, children: [] };
      });

      const tree = [];
      categories.forEach(cat => {
        if (cat.DanhMucChaID && categoryMap[cat.DanhMucChaID]) {
          categoryMap[cat.DanhMucChaID].children.push(categoryMap[cat.CategoryID]);
        } else {
          tree.push(categoryMap[cat.CategoryID]);
        }
      });
      return tree;
    }

    function createCategoryHTML(categories) {
      if (!categories || categories.length === 0) return '';
      let html = '<ul>';
      for (const cat of categories) {
        const hasChildren = cat.children && cat.children.length > 0;
        const hasImage = cat.HinhAnh && cat.HinhAnh.trim() !== '';
        const description = cat.MoTa ? (cat.MoTa.length > 60 ? cat.MoTa.substring(0, 60) + '...' : cat.MoTa) : '';

        html += `
          <li class="${hasChildren ? 'has-children' : ''}">
            <div class="category-tree-item" data-cat-id="${cat.CategoryID}">
              <a href="#" class="category-tree-item-link">
                ${hasImage 
                  ? `<img src="${cat.HinhAnh}" alt="${cat.TenDanhMuc}" class="category-tree-image" onerror="this.style.display='none'"/>` 
                  : '<div class="category-tree-image-placeholder"></div>'
                }
                <div class="category-tree-content">
                  <div class="category-tree-name">${cat.TenDanhMuc}</div>
                  ${description ? `<div class="category-tree-description">${description}</div>` : ''}
                </div>
              </a>
              ${hasChildren ? '<span class="category-toggle"></span>' : ''}
            </div>
            ${createCategoryHTML(cat.children)}
          </li>
        `;
      }
      html += '</ul>';
      return html;
    }
    
    function renderCategories(list) {
      const bar = document.getElementById('categoriesBar');
      bar.innerHTML = ''; 

      const categoryTreeData = buildCategoryTree(list);

      let html = `
        <div class="category-tree-container">
          <h3 class="category-filter-header">Danh mục sản phẩm</h3>
          <div class="category-tree">
            <ul>
              <li>
                <div class="category-tree-item" data-cat-id="">
                  <a href="#" class="category-tree-item-link">
                    <div class="category-tree-image-placeholder"></div>
                    <div class="category-tree-content">
                        <div class="category-tree-name">Tất cả sản phẩm</div>
                    </div>
                  </a>
                </div>
              </li>
            </ul>
            ${createCategoryHTML(categoryTreeData)}
          </div>
        </div>
      `;
      
      bar.innerHTML = html;
      updateActiveCategoryButton();
    }

    /**
     * Hàm tìm danh mục cha cấp cao nhất của một danh mục.
     * Nếu danh mục đã ở cấp 1 thì trả về chính nó.
     */
    function getTopCategoryId(catId) {
      const map = {};
      DEFAULT_CATEGORIES.forEach(cat => { map[cat.CategoryID] = cat; });
      let current = map[catId];
      while (current && current.DanhMucChaID && current.DanhMucChaID !== 'NULL' && map[current.DanhMucChaID]) {
        current = map[current.DanhMucChaID];
      }
      return current ? current.CategoryID : catId;
    }

    /**
     * Hiển thị danh sách danh mục con (chips) dựa trên danh mục cha.
     * Khi chọn chip, state.category được cập nhật và sản phẩm được lọc.
     */
    function renderSubcategories(parentId) {
      const bar = document.getElementById('categoriesBar');
      if (!bar) return;
      // Nếu không có parentId (không chọn danh mục cha), ẩn thanh lọc danh mục con
      if (!parentId) {
        bar.innerHTML = '';
        return;
      }
      // Lọc danh mục con theo danh mục cha
      const subcats = DEFAULT_CATEGORIES.filter(cat => cat.DanhMucChaID === parentId);
      let html = '<div class="subcategory-filter">';
      // Chip "Tất cả" để hiển thị tất cả sản phẩm thuộc danh mục cha
      html += `<span class="subcategory-chip ${state.category === parentId ? 'active' : ''}" data-cat-id="${parentId}">Tất cả</span>`;
      subcats.sort((a, b) => (a.ThuTuHienThi || 0) - (b.ThuTuHienThi || 0));
      subcats.forEach(cat => {
        html += `<span class="subcategory-chip ${state.category === cat.CategoryID ? 'active' : ''}" data-cat-id="${cat.CategoryID}">${cat.TenDanhMuc}</span>`;
      });
      html += '</div>';
      bar.innerHTML = html;
    }

    /**
     * Render slider danh mục nổi bật ở đầu trang.
     * Chỉ hiển thị các danh mục cấp 1 (không có DanhMucChaID hoặc DanhMucChaID === 'NULL').
     */
    function renderFeaturedCategories(list) {
      const container = document.getElementById('featuredCategoriesSlider');
      if (!container) return;
      // Sắp xếp danh mục để hiển thị slider. Các danh mục cấp 1 (không có DanhMucChaID hoặc DanhMucChaID === 'NULL') sẽ được ưu tiên lên đầu.
      // Chỉ hiển thị các danh mục cấp 1 (DanhMucChaID trống hoặc 'NULL') trong slider
      const categoriesSorted = list.slice().filter(cat => !cat.DanhMucChaID || cat.DanhMucChaID === 'NULL')
        .sort((a, b) => (a.ThuTuHienThi || 0) - (b.ThuTuHienThi || 0));
      if (categoriesSorted.length === 0) {
        container.innerHTML = '';
        return;
      }
      let itemsHtml = '';
      categoriesSorted.forEach(cat => {
        const imgSrc = cat.HinhAnh && cat.HinhAnh.trim() !== '' ? cat.HinhAnh.trim() : 'https://dummyimage.com/250x180/f7931e/ffffff&text=No+Image';
        const desc = cat.MoTa ? (cat.MoTa.length > 60 ? cat.MoTa.substring(0, 60) + '...' : cat.MoTa) : '';
        const activeClass = state.topCategory === cat.CategoryID ? 'active' : '';
        itemsHtml += `
          <div class="featured-card ${activeClass}" data-cat-id="${cat.CategoryID}">
            <img src="${imgSrc}" alt="${cat.TenDanhMuc}" width="200" height="180" loading="lazy" onerror="this.src='https://dummyimage.com/200x180/cccccc/ffffff&text=No+Image'" />
            <div class="featured-overlay">
              <h4>${cat.TenDanhMuc}</h4>
              ${desc ? `<p>${desc}</p>` : ''}
            </div>
          </div>`;
      });
      container.innerHTML = `
        <div class="featured-arrow" id="featuredPrev">&#10094;</div>
        <div class="featured-wrapper" id="featuredWrapper">${itemsHtml}</div>
        <div class="featured-arrow" id="featuredNext">&#10095;</div>
      `;
      const wrapper = document.getElementById('featuredWrapper');
      const prevBtn = document.getElementById('featuredPrev');
      const nextBtn = document.getElementById('featuredNext');
      prevBtn.addEventListener('click', () => {
        wrapper.scrollBy({ left: -300, behavior: 'smooth' });
      });
      nextBtn.addEventListener('click', () => {
        wrapper.scrollBy({ left: 300, behavior: 'smooth' });
      });
      wrapper.addEventListener('click', (e) => {
        const card = e.target.closest('.featured-card');
        if (!card) return;
        // Cập nhật trạng thái và lọc sản phẩm theo danh mục được chọn
        state.topCategory = card.dataset.catId;
        state.category = card.dataset.catId;
        state.page = 1;
        updateURL();
        fetchProducts();
        // Hiển thị lại slider và danh sách danh mục con theo danh mục cha được chọn
        renderFeaturedCategories(DEFAULT_CATEGORIES);
        renderSubcategories(state.topCategory);
        updateActiveCategoryButton();
        // Cuộn xuống phần sản phẩm
        document.getElementById('main-content').scrollIntoView({ behavior: 'smooth' });
      });
    }
    
    function updateActiveCategoryButton() {
      // Cập nhật active cho danh mục cây (nếu còn) và chip danh mục con
      document.querySelectorAll('#categoriesBar .category-tree-item').forEach(b => {
        b.classList.toggle('active', b.dataset.catId === state.category);
      });
      document.querySelectorAll('#categoriesBar .subcategory-chip').forEach(chip => {
        chip.classList.toggle('active', chip.dataset.catId === state.category);
      });
    }

    function handleCategoryClick(event) {
      // Bắt sự kiện click cho chip danh mục con
      const chip = event.target.closest('.subcategory-chip');
      if (!chip) return;
      event.preventDefault();
      // Cập nhật state.category theo chip được chọn
      state.category = chip.dataset.catId;
      state.page = 1;
      updateURL();
      fetchProducts();
      renderSubcategories(state.topCategory);
    }

    function initApp() {
      const params = new URLSearchParams(window.location.search);
      Object.keys(state).forEach(key => { state[key] = params.get(key) || state[key]; });
      state.page = parseInt(state.page, 10) || 1;

      // Safely set values only if elements exist
      const searchInput = document.getElementById('searchInput');
      const sortSelect = document.getElementById('sortSelect');

      if (searchInput) searchInput.value = state.query;
      if (sortSelect) sortSelect.value = state.sort;
      
      addEventListeners();
      // Xác định danh mục cha hiện tại dựa trên tham số URL hoặc chọn danh mục gốc đầu tiên
      if (state.category) {
        // Nếu URL chỉ định category, xác định danh mục cha tương ứng
        state.topCategory = getTopCategoryId(state.category);
      } else {
        // Không chọn danh mục cụ thể theo mặc định để hiển thị toàn bộ sản phẩm
        state.topCategory = '';
        state.category = '';
      }
      // Hiển thị slider danh mục cấp 1 và danh sách danh mục con
      renderFeaturedCategories(DEFAULT_CATEGORIES);
      renderSubcategories(state.topCategory);
      // Sau đó tải sản phẩm theo trạng thái hiện tại
      fetchProducts();
    }

    function addEventListeners() {
      document.getElementById('searchButton').addEventListener('click', onSearch);
      document.getElementById('searchInput').addEventListener('keydown', e => { if (e.key === "Enter") onSearch(); });
      document.getElementById('sortSelect').addEventListener('change', onSort);
      document.getElementById('categoriesBar').addEventListener('click', handleCategoryClick);
      document.getElementById('paginationBar').addEventListener('click', goToPage);
      window.addEventListener('popstate', handlePopState);

      // Gắn sự kiện cho menu "Sản phẩm" để cuộn tới khu vực sản phẩm
      const navProducts = document.getElementById('navProducts');
      if (navProducts) {
        navProducts.addEventListener('click', (e) => {
          e.preventDefault();
          document.getElementById('main-content').scrollIntoView({ behavior: 'smooth' });
        });
      }

      // Sự kiện cho nút "Khám phá bộ sưu tập" trong hero section
      const heroExplore = document.getElementById('heroExplore');
      if (heroExplore) {
        heroExplore.addEventListener('click', (e) => {
          e.preventDefault();
          // Khi click, cuộn xuống danh sách sản phẩm
          document.getElementById('main-content').scrollIntoView({ behavior: 'smooth' });
        });
      }
    }
    
    function handlePopState() {
      const params = new URLSearchParams(window.location.search);
      Object.keys(state).forEach(key => { state[key] = params.get(key) || (key === 'page' ? 1 : ''); });
      state.page = parseInt(state.page, 10) || 1;
      
      document.getElementById('searchInput').value = state.query;
      document.getElementById('sortSelect').value = state.sort;
      
      updateActiveCategoryButton();
      fetchProducts();
    }

    function updateURL() {
      const params = new URLSearchParams();
      Object.entries(state).forEach(([key, value]) => {
          if (value && !(key === 'page' && value === 1)) params.set(key, value);
      });
      const newUrl = `${window.location.pathname}?${params.toString()}`.replace(/\?$/, '');
      if (window.location.href !== newUrl) history.pushState(state, '', newUrl);
    }

    async function fetchData(params = {}) {
        fetchController.abort();
        fetchController = new AbortController();
        const url = new URL(CONFIG.API_URL);
        Object.entries(params).forEach(([key, value]) => { if (value) url.searchParams.append(key, value); });
        try {
            const response = await fetch(url, { signal: fetchController.signal });
            if (!response.ok) throw new Error(`HTTP Lỗi ${response.status}`);
            const json = await response.json();
            if (!json.success) throw new Error(json.message || 'API trả về lỗi');
            return json;
        } catch (error) {
            if (error.name !== 'AbortError') {
              console.error("Fetch Lỗi:", error);
              document.getElementById('productsList').innerHTML = '';
              const box = document.getElementById('loadingBox');
              box.innerText = "Lỗi khi tải dữ liệu.";
              box.style.display = 'block';
            }
            return null;
        }
    }
    
    async function fetchCategories() {
      const json = await fetchData({ action: 'getCategories' });
      if(json) renderCategories(json.data);
    }
    
    /**
     * Fetch products from the API. When append = true, fetched products will
     * be appended to the existing list instead of replacing it. This is
     * necessary to support infinite scrolling.
     * @param {boolean} append
     */
    async function fetchProducts(append = false) {
      // Show skeleton loader only when not appending (initial load or filter change)
      if (!append) {
        showSkeletonLoader();
      }
      const json = await fetchData({ ...state, action: 'getProducts', pageSize: CONFIG.PAGE_SIZE });
      if (json) renderProducts(json.data, json.meta, append);
    }
    
    function renderProducts(products, meta, append = false) {
      const listEl = document.getElementById('productsList');
      const loadingEl = document.getElementById('loadingBox');
      // Hide "no results" and skeleton loader message
      loadingEl.style.display = 'none';

      // Update global page count and reset fetching flag
      if (meta && typeof meta.totalPages === 'number') {
        totalPagesGlobal = meta.totalPages;
      }
      // When not appending, clear the list and pagination
      if (!append) {
        listEl.innerHTML = '';
        document.getElementById('paginationBar').innerHTML = "";
      }

      // Handle empty result set
      if (!products || products.length === 0) {
        if (!append) {
          loadingEl.innerText = "Không tìm thấy sản phẩm nào.";
          loadingEl.style.display = 'block';
        }
        return;
      }

      const fragment = document.createDocumentFragment();
      products.forEach(p => {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.onclick = () => showProductDetail(p.product_name);

        const hasDiscount = p.list_price > p.selling_price;
        const discountPercent = hasDiscount ? Math.round(((p.list_price - p.selling_price) / p.list_price) * 100) : 0;
        // Extract thumbnail: support multiple image URLs separated by commas (image_url field). Use the first image as thumbnail.
        let thumb;
        if (p.thumbnail && p.thumbnail.trim() !== '') {
          thumb = p.thumbnail.trim();
        } else if (p.image_url && p.image_url.trim() !== '') {
          const parts = p.image_url.split(',').map(s => s.trim()).filter(Boolean);
          thumb = parts.length > 0 ? parts[0] : '';
        } else {
          thumb = '';
        }
        if (!thumb) {
          thumb = 'https://dummyimage.com/250x180/f7931e/ffffff&text=No+Image';
        }
        card.innerHTML = `
          ${hasDiscount ? `<div class="discount-badge">-${discountPercent}%</div>` : ''}
          <div class="img-container">
            <img data-src="${thumb}" src="https://dummyimage.com/250x180/eeeeee/cccccc&text=Loading" alt="${p.product_name}" width="250" height="180" />
          </div>
          <div class="info">
            <h3 class="product-name">${p.product_name}</h3>
            <div class="product-price">
              ${formatPrice(p.selling_price)}
              ${hasDiscount ? `<span class="price-original">${formatPrice(p.list_price)}</span>` : ''}
            </div>
          </div>
        `;
        fragment.appendChild(card);
      });
      listEl.appendChild(fragment);
      // Initialise lazy loading after images are inserted into the DOM.
      initLazyLoading();

      // Add cart buttons to newly loaded products
      if (window.LotusCart) {
        setTimeout(() => {
          window.LotusCart.setupProductButtons();
        }, 100);
      }

      // Load featured and new products on homepage after products are loaded
      if (window.location.pathname === '/' || window.location.href.includes('blogspot.com/')) {
        // Wait for products to be loaded first
        if (allProducts && allProducts.length > 0) {
          loadNewProducts();
          loadFeaturedProducts();
        } else {
          // Retry after products are loaded
          setTimeout(() => {
            if (allProducts && allProducts.length > 0) {
              loadNewProducts();
              loadFeaturedProducts();
            }
          }, 1000);
        }
      }

      // Setup hero button scroll behavior
      setupHeroButton();

      // Setup header search
      setupHeaderSearch();

      // Setup account dropdown
      setupAccountDropdown();
      // Reset fetching flag for infinite scroll
      isFetchingMore = false;
      // Re-attach or update infinite scroll observer after first render
      if (!append) {
        initInfiniteScroll();
      }
    }

    function renderPagination(totalPages) {
      const pagBar = document.getElementById('paginationBar');
      if (totalPages <= 1) { pagBar.innerHTML = ""; return; }
      pagBar.innerHTML = Array.from({length: totalPages}, (_, i) => 
        `<button data-page="${i + 1}" class="${i + 1 === state.page ? 'active' : ''}">${i + 1}</button>`
      ).join('');
    }

    /**
     * Initialise lazy loading for images using IntersectionObserver. Images
     * inserted by renderProducts() have a data-src attribute pointing to
     * the actual thumbnail. This function observes them and swaps
     * src with data-src when they are near the viewport. If
     * IntersectionObserver is not supported, it falls back to eager loading.
     */
    function initLazyLoading() {
      const imgs = document.querySelectorAll('img[data-src]');
      if (!imgs.length) return;
      // Fallback for browsers without IntersectionObserver support
      if (!('IntersectionObserver' in window)) {
        imgs.forEach(img => { img.src = img.dataset.src; });
        return;
      }
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            observer.unobserve(img);
          }
        });
      }, { rootMargin: '100px' });
      imgs.forEach(img => observer.observe(img));
    }

    /**
     * Initialise infinite scrolling. Observes the sentinel element and
     * automatically loads the next page of products when the sentinel
     * enters the viewport. If total pages have been exhausted or an
     * existing observer is already attached, the observer will be
     * disconnected and re-created when necessary.
     */
    function initInfiniteScroll() {
      const sentinel = document.getElementById('infiniteSentinel');
      if (!sentinel) return;
      // Disconnect any existing observer to avoid duplicate callbacks
      if (infiniteObserver) {
        infiniteObserver.disconnect();
      }
      if (!('IntersectionObserver' in window)) return;
      infiniteObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !isFetchingMore && state.page < totalPagesGlobal) {
            isFetchingMore = true;
            state.page += 1;
            fetchProducts(true);
          }
        });
      }, { rootMargin: '200px' });
      infiniteObserver.observe(sentinel);
    }
    
    function showSkeletonLoader() {
      const listEl = document.getElementById('productsList');
      listEl.innerHTML = Array(CONFIG.PAGE_SIZE).fill(`
        <div class="skeleton-card">
          <div class="skeleton-img"></div>
          <div class="skeleton-info">
            <div class="skeleton-text"></div>
            <div class="skeleton-text short"></div>
          </div>
        </div>`
      ).join('');
      document.getElementById('loadingBox').style.display = 'none';
    }
    
    function onSearch() {
      state.query = document.getElementById('searchInput').value.trim();
      state.page = 1;
      updateURL();
      fetchProducts();
    }
    
    function onSort(event) {
      state.sort = event.target.value;
      state.page = 1;
      updateURL();
      fetchProducts();
    }

    function goToPage(event) {
      const button = event.target.closest('button');
      if (!button) return;
      const page = parseInt(button.dataset.page, 10);
      if (page !== state.page) {
        state.page = page;
        updateURL();
        fetchProducts();
        document.getElementById('main-content').scrollIntoView();
      }
    }
    
    async function showProductDetail(product_name) {
      const json = await fetchData({ action: 'getProductDetail', product_name });
      if (json) openProductModal(json.data);
    }
    
    function openProductModal(product) {
        const modalContainer = document.getElementById('productModal');
        const tpl = document.getElementById('modalTemplate').content.cloneNode(true);
        const elements = {
            name: tpl.querySelector('#modalName'),
            breadcrumbs: tpl.querySelector('#modalBreadcrumbs'),
            variantsContainer: tpl.querySelector('#modalVariants'),
            detailsGrid: tpl.querySelector('#modalDetailsGrid'),
            promo: tpl.querySelector('#modalPromo'),
            desc: tpl.querySelector('#modalDesc'),
            img: tpl.querySelector('#modalImg'),
            thumbnails: tpl.querySelector('#modalThumbnails')
        };

        elements.name.innerText = product.product_name;
        
        let breadcrumbs = '';
        if (product.category.parent) breadcrumbs += `${product.category.parent.name} / `;
        if (product.category.name) breadcrumbs += product.category.name;
        elements.breadcrumbs.innerText = breadcrumbs;
        
        elements.variantsContainer.innerHTML = product.variants.map((v, index) => 
            `<button class="variant-btn ${index === 0 ? 'active' : ''}" data-index="${index}">${v.name_product}</button>`
        ).join('');
        
        const updateVariantDetails = (index) => {
            const variant = product.variants[index];
            // Update main image to the first image of the variant, or placeholder
            elements.img.src = (variant.images && variant.images.length > 0 ? variant.images[0] : 'https://dummyimage.com/550x250/cccccc/ffffff&text=No+Image');
            // Build thumbnails if available
            if (elements.thumbnails) {
              if (variant.images && variant.images.length > 0) {
                const thumbsHtml = variant.images.map((url, idx) =>
                  `<img class="modal-thumbnail ${idx === 0 ? 'active' : ''}" data-index="${idx}" src="${url}" alt="Thumbnail ${idx + 1}" loading="lazy" />`
                ).join('');
                elements.thumbnails.innerHTML = thumbsHtml;
                // Add click events to thumbnails to update main image
                elements.thumbnails.querySelectorAll('.modal-thumbnail').forEach(th => {
                  th.addEventListener('click', () => {
                    const activeThumb = elements.thumbnails.querySelector('.modal-thumbnail.active');
                    if (activeThumb) activeThumb.classList.remove('active');
                    th.classList.add('active');
                    elements.img.src = th.src;
                  });
                });
              } else {
                elements.thumbnails.innerHTML = '';
              }
            }
            
            let detailsHTML = `<div class="detail-item"><strong>Giá cái:</strong> ${formatPrice(variant.price_piece_TSG)}</div>`;
            if(variant.price_box_TSG) detailsHTML += `<div class="detail-item"><strong>Giá hộp:</strong> ${formatPrice(variant.price_box_TSG)}</div>`;
            if(variant.price_pack_TSG) detailsHTML += `<div class="detail-item"><strong>Giá lố:</strong> ${formatPrice(variant.price_pack_TSG)}</div>`;
            if(variant.price_case_TSG) detailsHTML += `<div class="detail-item"><strong>Giá thùng:</strong> ${formatPrice(variant.price_case_TSG)}</div>`;
            if(variant.weight) detailsHTML += `<div class="detail-item"><strong>Trọng lượng:</strong> ${variant.weight} g</div>`;
            if(variant.volume) detailsHTML += `<div class="detail-item"><strong>Dung tích:</strong> ${variant.volume}</div>`;
            if(variant.height) detailsHTML += `<div class="detail-item"><strong>Chiều cao:</strong> ${variant.height} mm</div>`;
            if(variant.diameter) detailsHTML += `<div class="detail-item"><strong>Đường kính:</strong> ${variant.diameter} mm</div>`;
            elements.detailsGrid.innerHTML = detailsHTML;
            
            if (variant.promotions && variant.promotions.length > 0) {
                elements.promo.innerHTML = '<strong>✨ Khuyến mãi:</strong><br/>' + variant.promotions.map(p => p.rules.map(r => `&bull; ${r}`).join('<br/>')).join('<br/>');
                elements.promo.style.display = 'block';
            } else {
                elements.promo.style.display = 'none';
            }
        };

        elements.variantsContainer.addEventListener('click', (e) => {
            const btn = e.target.closest('.variant-btn');
            if (btn) {
                elements.variantsContainer.querySelector('.active').classList.remove('active');
                btn.classList.add('active');
                updateVariantDetails(btn.dataset.index);
            }
        });

        elements.desc.innerText = product.description || 'Chưa có mô tả cho sản phẩm này.';
        
        modalContainer.innerHTML = '';
        modalContainer.appendChild(tpl);
        updateVariantDetails(0); 
        modalContainer.classList.add('show');
    }

    function closeModal() {
      const modalContainer = document.getElementById('productModal');
      modalContainer.classList.remove('show');
      setTimeout(() => { modalContainer.innerHTML = ''; }, 300);
    }

    function formatPrice(val) {
      if (typeof val !== 'number' || isNaN(val)) return "Liên hệ";
      return val.toLocaleString("vi-VN") + "₫";
    }

    /**
     * Handle newsletter form submission. For now this function simply shows
     * a success message and resets the form. You can integrate this with
     * a real mailing list service by sending an AJAX request here.
     * @param {Event} event
     */
    function subscribeNewsletter(event) {
      event.preventDefault();
      const emailInput = document.getElementById('newsletterEmail');
      const successMsg = document.getElementById('newsletterSuccessMsg');
      if (!emailInput) return;
      const email = emailInput.value.trim();
      if (!email) return;
      // TODO: integrate with your newsletter API endpoint here.
      // For now just show a thank-you message and clear the input.
      emailInput.value = '';
      if (successMsg) successMsg.style.display = 'block';
      setTimeout(() => {
        if (successMsg) successMsg.style.display = 'none';
      }, 5000);
    }

    document.addEventListener('DOMContentLoaded', initApp);

    /**
     * Load new products for homepage
     */
    function loadNewProducts() {
      const newGrid = document.getElementById('newProductsGrid');
      if (!newGrid) return;

      // Check if products are loaded
      if (!allProducts || allProducts.length === 0) {
        console.log('⚠️ No products loaded yet for new products, retrying...');
        setTimeout(loadNewProducts, 500);
        return;
      }

      console.log('🆕 Loading new products from', allProducts.length, 'total products');

      // Filter products by is_new = TRUE
      const newProducts = allProducts.filter(product => {
        const isNew = product.is_new === true || product.is_new === 'TRUE' || product.is_new === 'true';
        return isNew;
      }).slice(0, 4); // Limit to 4 products for new section

      console.log('✨ Found', newProducts.length, 'new products');

      if (newProducts.length === 0) {
        newGrid.innerHTML = '<p style="text-align: center; color: #6c757d; grid-column: 1/-1;">Hiện tại chưa có sản phẩm mới</p>';
        return;
      }

      newGrid.innerHTML = newProducts.map(product => {
        const firstVariant = product.variants[0];
        const price = firstVariant.price;
        const image = firstVariant.images[0] || '/images/placeholder.jpg';

        console.log('🆕 New Product:', product.product_name, 'is_new:', product.is_new);

        return `
          <div class="featured-product-card product-card" data-sku="${product.sku}">
            <div class="featured-product-image">
              <img src="${image}" alt="${product.product_name}" loading="lazy"/>
              <div class="featured-product-badge new-badge">Mới</div>
            </div>
            <div class="featured-product-info info">
              <h3 class="featured-product-title product-name">${product.product_name}</h3>
              <div class="featured-product-price product-price">${formatPrice(price)}</div>
              <p class="featured-product-description">${product.description || 'Sản phẩm thủy tinh mới với thiết kế hiện đại và chất lượng cao.'}</p>
            </div>
          </div>
        `;
      }).join('');

      // Add cart buttons to new products
      setTimeout(() => {
        if (window.LotusCart) {
          window.LotusCart.setupProductButtons();
        }
      }, 100);
    }

    /**
     * Load featured products for homepage
     */
    function loadFeaturedProducts() {
      const featuredGrid = document.getElementById('featuredProductsGrid');
      if (!featuredGrid) return;

      // Check if products are loaded
      if (!allProducts || allProducts.length === 0) {
        console.log('⚠️ No products loaded yet, retrying...');
        setTimeout(loadFeaturedProducts, 500);
        return;
      }

      console.log('🔍 Loading featured products from', allProducts.length, 'total products');

      // Filter products by is_featured = TRUE only
      const featuredProducts = allProducts.filter(product => {
        const isFeatured = product.is_featured === true || product.is_featured === 'TRUE' || product.is_featured === 'true';
        return isFeatured;
      }).slice(0, 6); // Limit to 6 products

      console.log('✨ Found', featuredProducts.length, 'featured products');

      // If no featured products, fallback to first 6 products (excluding new products)
      const productsToShow = featuredProducts.length > 0 ? featuredProducts : allProducts.slice(0, 6);

      featuredGrid.innerHTML = productsToShow.map(product => {
        const firstVariant = product.variants[0];
        const price = firstVariant.price;
        const image = firstVariant.images[0] || '/images/placeholder.jpg';

        // Featured products always get "Nổi bật" badge
        const badgeText = 'Nổi bật';

        console.log('📦 Featured Product:', product.product_name, 'is_featured:', product.is_featured, 'badge:', badgeText);

        return `
          <div class="featured-product-card product-card" data-sku="${product.sku}">
            <div class="featured-product-image">
              <img src="${image}" alt="${product.name}" loading="lazy"/>
              <div class="featured-product-badge">${badgeText}</div>
            </div>
            <div class="featured-product-info info">
              <h3 class="featured-product-title product-name">${product.name}</h3>
              <div class="featured-product-price product-price">${formatPrice(price)}</div>
              <p class="featured-product-description">${product.description || 'Sản phẩm thủy tinh cao cấp với thiết kế tinh tế và chất lượng vượt trội.'}</p>
            </div>
          </div>
        `;
      }).join('');

      // Add cart buttons to featured products
      setTimeout(() => {
        if (window.LotusCart) {
          window.LotusCart.setupProductButtons();

          // Force add buttons to featured products if they don't have them
          const featuredCards = document.querySelectorAll('.featured-product-card');
          featuredCards.forEach(card => {
            if (!card.querySelector('.add-to-cart-btn')) {
              const sku = window.LotusCart.extractSKUFromCard(card);
              const name = window.LotusCart.extractNameFromCard(card);
              const price = window.LotusCart.extractPriceFromCard(card);
              const image = window.LotusCart.extractImageFromCard(card);

              if (sku && name && price) {
                window.LotusCart.addCartButtonToCard(card, { sku, name, price, image });
              }
            }
          });
        }
      }, 200);
    }

    /**
     * Setup hero button scroll behavior
     */
    function setupHeroButton() {
      const heroButton = document.getElementById('heroExplore');
      if (heroButton) {
        heroButton.addEventListener('click', function(e) {
          e.preventDefault();
          const productsSection = document.getElementById('products');
          if (productsSection) {
            productsSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      }
    }

    /**
     * Setup header search functionality
     */
    function setupHeaderSearch() {
      const searchHandle = document.getElementById('site-search-handle');
      const searchAction = document.querySelector('.header-action_search');
      const searchInput = document.getElementById('inputSearchAuto');
      const searchForm = document.querySelector('.searchform');
      const searchResults = document.getElementById('ajaxSearchResults');

      if (!searchHandle || !searchAction) return;

      // Toggle search dropdown
      searchHandle.addEventListener('click', function(e) {
        e.preventDefault();
        searchAction.classList.toggle('active');

        if (searchAction.classList.contains('active')) {
          setTimeout(() => {
            if (searchInput) searchInput.focus();
          }, 100);
        }
      });

      // Close search when clicking outside
      document.addEventListener('click', function(e) {
        if (!searchAction.contains(e.target)) {
          searchAction.classList.remove('active');
        }
      });

      // Handle search form submission
      if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
          e.preventDefault();
          const query = searchInput.value.trim();
          if (query) {
            performSearch(query);
          }
        });
      }

      // Handle search input changes for live search
      if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
          const query = this.value.trim();
          clearTimeout(searchTimeout);

          if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
              performSearch(query, true);
            }, 300);
          } else {
            if (searchResults) {
              searchResults.style.display = 'none';
            }
          }
        });
      }
    }

    /**
     * Perform product search
     */
    function performSearch(query, isLiveSearch = false) {
      const searchResults = document.getElementById('ajaxSearchResults');
      const resultsContent = searchResults?.querySelector('.resultsContent');

      if (!searchResults || !resultsContent) return;

      // Filter products based on query
      const filteredProducts = allProducts.filter(product => {
        const searchText = query.toLowerCase();
        return product.product_name.toLowerCase().includes(searchText) ||
               product.sku.toLowerCase().includes(searchText) ||
               (product.description && product.description.toLowerCase().includes(searchText));
      }).slice(0, 5); // Limit to 5 results for dropdown

      if (filteredProducts.length > 0) {
        resultsContent.innerHTML = filteredProducts.map(product => {
          const firstVariant = product.variants[0];
          const price = firstVariant.price;
          const image = firstVariant.images[0] || '/images/placeholder.jpg';

          return `
            <div class="search-result-item" onclick="showProductDetail('${product.product_name}')">
              <img src="${image}" alt="${product.product_name}" loading="lazy"/>
              <div class="search-result-info">
                <h4>${product.product_name}</h4>
                <p class="search-result-price">${formatPrice(price)}</p>
              </div>
            </div>
          `;
        }).join('');

        searchResults.style.display = 'block';
      } else {
        resultsContent.innerHTML = '<div class="search-no-results">Không tìm thấy sản phẩm nào</div>';
        searchResults.style.display = 'block';
      }

      // If not live search, redirect to products page with search
      if (!isLiveSearch) {
        setTimeout(() => {
          window.location.href = `/p/san-pham.html?search=${encodeURIComponent(query)}`;
        }, 500);
      }
    }

    /**
     * Setup account dropdown functionality
     */
    function setupAccountDropdown() {
      const accountHandle = document.getElementById('site-account-handle');
      const accountAction = document.querySelector('.header-action_account');
      const loginForm = document.getElementById('customer_login');

      if (!accountHandle || !accountAction) return;

      // Toggle account dropdown
      accountHandle.addEventListener('click', function(e) {
        e.preventDefault();
        accountAction.classList.toggle('active');
      });

      // Close account dropdown when clicking outside
      document.addEventListener('click', function(e) {
        if (!accountAction.contains(e.target)) {
          accountAction.classList.remove('active');
        }
      });

      // Handle login form submission
      if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
          e.preventDefault();
          const email = document.getElementById('login-customer-email').value;
          const password = document.getElementById('login-customer-password').value;

          // Simple validation
          if (!email || !password) {
            alert('Vui lòng nhập đầy đủ email và mật khẩu');
            return;
          }

          // For demo purposes - in real implementation, this would call an API
          alert('Chức năng đăng nhập sẽ được tích hợp trong phiên bản tiếp theo');
          accountAction.classList.remove('active');
        });
      }

      // Handle register and recover buttons
      const showRegister = document.getElementById('show-register');
      const showRecover = document.getElementById('show-recover');

      if (showRegister) {
        showRegister.addEventListener('click', function(e) {
          e.preventDefault();
          alert('Chức năng đăng ký sẽ được tích hợp trong phiên bản tiếp theo');
        });
      }

      if (showRecover) {
        showRecover.addEventListener('click', function(e) {
          e.preventDefault();
          alert('Chức năng khôi phục mật khẩu sẽ được tích hợp trong phiên bản tiếp theo');
        });
      }
    }
  //]]>
  </script>

  <!-- ============================================================================
       LOTUS GLASS - COMPLETE SHOPPING CART JAVASCRIPT
       ============================================================================ -->
  <script type="text/javascript">
  //<![CDATA[

  // ============================================================================
  // GLOBAL CONFIGURATION
  // ============================================================================

  const LOTUS_CONFIG = {
    // Google Apps Script API URL
    API_BASE_URL: 'https://script.google.com/macros/s/AKfycbzKMKloNLg4AzzqCc4CPPXhCALge38LprQKeM242qDicVf8fflpRslAGKWbFb8uOlGBYA/exec',

    // Currency settings
    CURRENCY: 'VND',
    CURRENCY_SYMBOL: '₫',

    // Cart settings
    MAX_QUANTITY: 999,
    MIN_QUANTITY: 1,

    // Local storage keys
    STORAGE_KEYS: {
      CART: 'lotus_cart_items',
      CUSTOMER: 'lotus_customer_info',
      LAST_VISIT: 'lotus_last_visit'
    },

    // Animation durations
    ANIMATION: {
      NOTIFICATION: 3000,
      CART_SLIDE: 400,
      BUTTON_FEEDBACK: 200
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const LotusUtils = {
    /**
     * Format price to Vietnamese currency
     */
    formatPrice(amount) {
      if (!amount || isNaN(amount)) return '0₫';
      return new Intl.NumberFormat('vi-VN').format(amount) + '₫';
    },

    /**
     * Generate unique ID
     */
    generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * Debounce function calls
     */
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    /**
     * Sanitize HTML to prevent XSS
     */
    sanitizeHTML(str) {
      const temp = document.createElement('div');
      temp.textContent = str;
      return temp.innerHTML;
    },

    /**
     * Local storage with error handling
     */
    storage: {
      get(key, defaultValue = null) {
        try {
          const item = localStorage.getItem(key);
          return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
          console.warn('Storage get error:', error);
          return defaultValue;
        }
      },

      set(key, value) {
        try {
          localStorage.setItem(key, JSON.stringify(value));
          return true;
        } catch (error) {
          console.warn('Storage set error:', error);
          return false;
        }
      },

      remove(key) {
        try {
          localStorage.removeItem(key);
          return true;
        } catch (error) {
          console.warn('Storage remove error:', error);
          return false;
        }
      }
    }
  };


  // ============================================================================
  // MAIN CART SYSTEM
  // ============================================================================

  const LotusCart = {
    // Internal state
    items: [],
    isOpen: false,
    isLoading: false,

    // DOM elements cache
    elements: {},

    /**
     * Initialize cart system
     */
    init() {
      this.cacheElements();
      this.loadCartFromStorage();
      this.bindEvents();
      this.updateUI();
      this.setupProductButtons();

      console.log('🛒 Lotus Cart initialized');
    },

    /**
     * Cache DOM elements for performance
     */
    cacheElements() {
      this.elements = {
        badge: document.getElementById('cart-badge'),
        count: document.getElementById('cart-count'),
        sidebar: document.getElementById('cart-sidebar'),
        overlay: document.getElementById('cart-overlay'),
        items: document.getElementById('cart-items'),
        empty: document.getElementById('cart-empty'),
        loading: document.getElementById('cart-loading'),
        total: document.getElementById('cart-total'),
        subtotal: document.getElementById('cart-subtotal'),
        checkoutBtn: document.getElementById('proceed-checkout'),
        notification: document.getElementById('cart-notification')
      };
    },

    /**
     * Bind event listeners
     */
    bindEvents() {
      // Close cart when clicking overlay
      if (this.elements.overlay) {
        this.elements.overlay.addEventListener('click', () => this.closeCart());
      }

      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen) {
          this.closeCart();
        }
      });

      // Auto-save cart periodically
      setInterval(() => this.saveCartToStorage(), 30000);
    },

    /**
     * Setup add to cart buttons on product cards
     */
    setupProductButtons() {
      // Auto-detect product cards and add buttons (including featured product cards)
      const productCards = document.querySelectorAll('.product-card, .featured-product-card');

      productCards.forEach(card => {
        if (card.querySelector('.add-to-cart-btn')) return; // Already has button

        const sku = this.extractSKUFromCard(card);
        const name = this.extractNameFromCard(card);
        const price = this.extractPriceFromCard(card);
        const image = this.extractImageFromCard(card);

        if (sku && name && price) {
          this.addCartButtonToCard(card, { sku, name, price, image });
        }
      });

      // Setup mutation observer to watch for new product cards
      this.setupProductObserver();

      // Bind existing add to cart buttons
      document.addEventListener('click', (e) => {
        const btn = e.target.closest('.add-to-cart-btn');
        if (btn) {
          e.preventDefault();
          this.handleAddToCartClick(btn);
        }
      });
    },

    /**
     * Setup observer to watch for new product cards
     */
    setupProductObserver() {
      // Watch both products list and featured products grid
      const containers = [
        document.getElementById('productsList'),
        document.querySelector('.products-list'),
        document.getElementById('featuredProductsGrid'),
        document.querySelector('.featured-products-grid')
      ].filter(Boolean);

      if (containers.length === 0) return;

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1 && (node.classList.contains('product-card') || node.classList.contains('featured-product-card'))) {
              // New product card added
              setTimeout(() => {
                if (!node.querySelector('.add-to-cart-btn')) {
                  const sku = this.extractSKUFromCard(node);
                  const name = this.extractNameFromCard(node);
                  const price = this.extractPriceFromCard(node);
                  const image = this.extractImageFromCard(node);

                  if (sku && name && price) {
                    this.addCartButtonToCard(node, { sku, name, price, image });
                  }
                }
              }, 50);
            }
          });
        });
      });

      // Observe all containers
      containers.forEach(container => {
        observer.observe(container, { childList: true, subtree: true });
      });
    },

    /**
     * Extract product data from card elements
     */
    extractSKUFromCard(card) {
      // Try multiple methods to get SKU
      return card.dataset.sku ||
             card.querySelector('[data-sku]')?.dataset.sku ||
             card.dataset.productSku ||
             this.generateSKUFromName(card);
    },

    extractNameFromCard(card) {
      const nameEl = card.querySelector('.product-name, .featured-product-title, h3, .title, .name');
      return nameEl ? nameEl.textContent.trim() : '';
    },

    extractPriceFromCard(card) {
      const priceEl = card.querySelector('.product-price, .featured-product-price, .price');
      if (!priceEl) return 0;

      // Get the first price (selling price, not original price)
      const priceText = priceEl.childNodes[0]?.textContent || priceEl.textContent;
      const cleanPrice = priceText.replace(/[^\d]/g, '');
      return parseInt(cleanPrice) || 0;
    },

    extractImageFromCard(card) {
      const img = card.querySelector('img');
      return img ? img.src : '';
    },

    generateSKUFromName(card) {
      const name = this.extractNameFromCard(card);
      if (!name) return 'LG' + Date.now().toString().slice(-6);
      return 'LG' + name.replace(/\s+/g, '').substring(0, 8).toUpperCase() + Date.now().toString().slice(-4);
    },

    /**
     * Add cart button to product card
     */
    addCartButtonToCard(card, product) {
      const button = document.createElement('button');
      button.className = 'add-to-cart-btn';
      button.dataset.sku = product.sku;
      button.innerHTML = `
        <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
          <path d="M19 7H16V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm4 10a1 1 0 0 1-2 0v-3a1 1 0 0 1 2 0v3z"/>
        </svg>
        Thêm vào giỏ
      `;

      // Add some margin to the button
      button.style.marginTop = '12px';

      // Find best place to insert button - check for featured product info first
      const infoSection = card.querySelector('.featured-product-info, .info, .product-info, .product-details');
      if (infoSection) {
        infoSection.appendChild(button);
      } else {
        // If no info section, create one
        const infoDiv = document.createElement('div');
        infoDiv.className = card.classList.contains('featured-product-card') ? 'featured-product-info' : 'info';
        infoDiv.style.padding = '12px';
        infoDiv.appendChild(button);
        card.appendChild(infoDiv);
      }
    },

    /**
     * Handle add to cart button click
     */
    handleAddToCartClick(btn) {
      const card = btn.closest('.product-card');
      if (!card) return;

      const sku = btn.dataset.sku || this.extractSKUFromCard(card);
      const name = this.extractNameFromCard(card);
      const price = this.extractPriceFromCard(card);
      const image = this.extractImageFromCard(card);

      this.addToCart(sku, name, '', price, image);
    },


    /**
     * Add item to cart
     */
    addToCart(sku, name, variant = '', price = 0, image = '', quantity = 1) {
      if (!sku || !name) {
        this.showNotification('Thông tin sản phẩm không hợp lệ', 'error');
        return false;
      }

      // Check if item already exists
      const existingItem = this.items.find(item => item.sku === sku);

      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        this.items.push({
          sku,
          name: LotusUtils.sanitizeHTML(name),
          variant: LotusUtils.sanitizeHTML(variant),
          price: Number(price),
          image,
          quantity: Number(quantity),
          addedAt: Date.now()
        });
      }

      this.updateUI();
      this.saveCartToStorage();
      this.showNotification(`Đã thêm "${name}" vào giỏ hàng`, 'success');
      this.animateCartBadge();

      return true;
    },

    /**
     * Remove item from cart
     */
    removeItem(sku) {
      const index = this.items.findIndex(item => item.sku === sku);
      if (index > -1) {
        const item = this.items[index];
        this.items.splice(index, 1);
        this.updateUI();
        this.saveCartToStorage();
        this.showNotification(`Đã xóa "${item.name}" khỏi giỏ hàng`, 'info');
      }
    },

    /**
     * Update item quantity
     */
    updateQuantity(sku, change) {
      const item = this.items.find(item => item.sku === sku);
      if (!item) return;

      const newQuantity = item.quantity + change;

      if (newQuantity <= 0) {
        this.removeItem(sku);
      } else if (newQuantity <= LOTUS_CONFIG.MAX_QUANTITY) {
        item.quantity = newQuantity;
        this.updateUI();
        this.saveCartToStorage();
      }
    },

    /**
     * Set specific quantity
     */
    setQuantity(sku, quantity) {
      const item = this.items.find(item => item.sku === sku);
      if (!item) return;

      const qty = Math.max(LOTUS_CONFIG.MIN_QUANTITY, Math.min(LOTUS_CONFIG.MAX_QUANTITY, Number(quantity)));

      if (qty !== item.quantity) {
        item.quantity = qty;
        this.updateUI();
        this.saveCartToStorage();
      }
    },

    /**
     * Clear entire cart
     */
    clearCart() {
      this.items = [];
      this.updateUI();
      this.saveCartToStorage();
      this.showNotification('Đã xóa tất cả sản phẩm khỏi giỏ hàng', 'info');
    },

    /**
     * Get cart totals
     */
    getTotals() {
      const subtotal = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const itemCount = this.items.reduce((sum, item) => sum + item.quantity, 0);

      return {
        subtotal,
        itemCount,
        total: subtotal // Can add shipping, tax, etc. later
      };
    },

    /**
     * Open cart sidebar
     */
    openCart() {
      if (this.isOpen) return;

      this.isOpen = true;
      this.elements.sidebar?.classList.add('active');
      this.elements.overlay?.classList.add('active');
      document.body.style.overflow = 'hidden';

      // Focus management for accessibility
      setTimeout(() => {
        const closeBtn = this.elements.sidebar?.querySelector('.cart-close');
        closeBtn?.focus();
      }, 100);
    },

    /**
     * Close cart sidebar
     */
    closeCart() {
      if (!this.isOpen) return;

      this.isOpen = false;
      this.elements.sidebar?.classList.remove('active');
      this.elements.overlay?.classList.remove('active');
      document.body.style.overflow = '';

      // Return focus to cart badge
      this.elements.badge?.focus();
    },

    /**
     * Proceed to checkout
     */
    proceedToCheckout() {
      if (this.items.length === 0) {
        this.showNotification('Giỏ hàng trống. Vui lòng thêm sản phẩm trước khi thanh toán.', 'warning');
        return;
      }

      // Close cart and open checkout modal
      this.closeCart();

      // Check if checkout system is available
      if (window.LotusCheckout) {
        window.LotusCheckout.open();
      } else {
        this.showNotification('Hệ thống thanh toán đang được cập nhật. Vui lòng thử lại sau.', 'info');
      }
    },


    /**
     * Update cart UI
     */
    updateUI() {
      this.updateCartBadge();
      this.updateCartItems();
      this.updateCartTotals();
      this.updateCheckoutButton();
    },

    /**
     * Update cart badge
     */
    updateCartBadge() {
      const { itemCount } = this.getTotals();

      if (this.elements.count) {
        this.elements.count.textContent = itemCount;
        this.elements.count.style.display = itemCount > 0 ? 'flex' : 'none';
      }
    },

    /**
     * Update cart items display
     */
    updateCartItems() {
      if (!this.elements.items) return;

      if (this.items.length === 0) {
        this.elements.empty.style.display = 'block';
        this.elements.items.innerHTML = '';
        this.elements.items.appendChild(this.elements.empty);
        return;
      }

      this.elements.empty.style.display = 'none';

      const itemsHTML = this.items.map(item => this.createCartItemHTML(item)).join('');
      this.elements.items.innerHTML = itemsHTML;
    },

    /**
     * Create HTML for cart item
     */
    createCartItemHTML(item) {
      return `
        <div class="cart-item" data-sku="${item.sku}">
          <div class="item-image">
            <img src="${item.image || &apos;/images/placeholder.jpg&apos;}" alt="${item.name}" loading="lazy"/>
          </div>

          <div class="item-details">
            <h4 class="item-name">${item.name}</h4>
            ${item.variant ? `<p class="item-variant">${item.variant}</p>` : ''}
            <p class="item-price">${LotusUtils.formatPrice(item.price)}</p>
          </div>

          <div class="item-controls">
            <!-- Quantity Controls -->
            <div class="quantity-controls">
              <button class="qty-btn qty-decrease" onclick="LotusCart.updateQuantity(&apos;${item.sku}&apos;, -1)" aria-label="Giảm số lượng">
                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                  <path d="M19 13H5v-2h14v2z"/>
                </svg>
              </button>
              <input type="number" class="qty-input" value="${item.quantity}" min="1" max="999"
                     onchange="LotusCart.setQuantity(&apos;${item.sku}&apos;, this.value)" aria-label="Số lượng"/>
              <button class="qty-btn qty-increase" onclick="LotusCart.updateQuantity(&apos;${item.sku}&apos;, 1)" aria-label="Tăng số lượng">
                <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
              </button>
            </div>

            <!-- Item Total -->
            <div class="item-total">${LotusUtils.formatPrice(item.price * item.quantity)}</div>

            <!-- Remove Button -->
            <button class="remove-item" onclick="LotusCart.removeItem(&apos;${item.sku}&apos;)" aria-label="Xóa sản phẩm">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
            </button>
          </div>
        </div>
      `;
    },

    /**
     * Update cart totals
     */
    updateCartTotals() {
      const { subtotal, total } = this.getTotals();

      if (this.elements.subtotal) {
        this.elements.subtotal.textContent = LotusUtils.formatPrice(subtotal);
      }

      if (this.elements.total) {
        this.elements.total.textContent = LotusUtils.formatPrice(total);
      }
    },

    /**
     * Update checkout button state
     */
    updateCheckoutButton() {
      if (!this.elements.checkoutBtn) return;

      const hasItems = this.items.length > 0;
      this.elements.checkoutBtn.disabled = !hasItems;
      this.elements.checkoutBtn.style.opacity = hasItems ? '1' : '0.5';
    },

    /**
     * Animate cart badge when item added
     */
    animateCartBadge() {
      if (!this.elements.badge) return;

      this.elements.badge.style.transform = 'scale(1.2)';
      setTimeout(() => {
        this.elements.badge.style.transform = 'scale(1)';
      }, LOTUS_CONFIG.ANIMATION.BUTTON_FEEDBACK);
    },

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
      if (!this.elements.notification) return;

      const notification = this.elements.notification;
      const content = notification.querySelector('.notification-content');

      if (content) {
        content.innerHTML = `
          <svg class="notification-icon" viewBox="0 0 24 24" fill="currentColor">
            ${this.getNotificationIcon(type)}
          </svg>
          <span>${message}</span>
        `;
      }

      notification.className = `cart-notification ${type} show`;

      setTimeout(() => {
        notification.classList.remove('show');
      }, LOTUS_CONFIG.ANIMATION.NOTIFICATION);
    },

    /**
     * Get notification icon based on type
     */
    getNotificationIcon(type) {
      const icons = {
        success: '<path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>',
        error: '<path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>',
        warning: '<path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>',
        info: '<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>'
      };
      return icons[type] || icons.info;
    },


    /**
     * Save cart to localStorage
     */
    saveCartToStorage() {
      LotusUtils.storage.set(LOTUS_CONFIG.STORAGE_KEYS.CART, {
        items: this.items,
        lastUpdated: Date.now()
      });
    },

    /**
     * Load cart from localStorage
     */
    loadCartFromStorage() {
      const cartData = LotusUtils.storage.get(LOTUS_CONFIG.STORAGE_KEYS.CART);

      if (cartData && cartData.items) {
        // Filter out expired items (older than 30 days)
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        this.items = cartData.items.filter(item =>
          item.addedAt && item.addedAt > thirtyDaysAgo
        );
      }
    },

    /**
     * Get cart data for checkout
     */
    getCartData() {
      return {
        items: this.items,
        totals: this.getTotals(),
        timestamp: Date.now()
      };
    },

    /**
     * Validate cart items
     */
    validateCart() {
      // Remove invalid items
      this.items = this.items.filter(item =>
        item.sku && item.name && item.price >= 0 && item.quantity > 0
      );

      // Fix quantity bounds
      this.items.forEach(item => {
        item.quantity = Math.max(LOTUS_CONFIG.MIN_QUANTITY,
                                Math.min(LOTUS_CONFIG.MAX_QUANTITY, item.quantity));
      });

      this.updateUI();
      this.saveCartToStorage();
    }
  };

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  // Initialize cart when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other scripts to load
    setTimeout(() => {
      LotusCart.init();
    }, 100);
  });

  // Initialize cart if DOM is already loaded
  if (document.readyState === 'loading') {
    // DOM is still loading
  } else {
    // DOM is already loaded
    setTimeout(() => {
      LotusCart.init();
    }, 100);
  }

  // Expose cart to global scope for debugging
  window.LotusCart = LotusCart;
  window.LotusUtils = LotusUtils;

  // ============================================================================
  // LEGACY SUPPORT & COMPATIBILITY
  // ============================================================================

  // Support for manual cart operations
  window.addToCart = function(sku, name, variant, price, image, quantity) {
    return LotusCart.addToCart(sku, name, variant, price, image, quantity);
  };

  window.openCart = function() {
    return LotusCart.openCart();
  };

  window.closeCart = function() {
    return LotusCart.closeCart();
  };

  // Auto-setup for existing product cards
  const setupExistingProducts = () => {
    // Look for product cards with data attributes
    document.querySelectorAll('[data-product-sku]').forEach(card => {
      const sku = card.dataset.productSku;
      const name = card.dataset.productName || '';
      const price = parseInt(card.dataset.productPrice) || 0;
      const image = card.dataset.productImage || '';

      if (sku && name && !card.querySelector('.add-to-cart-btn')) {
        LotusCart.addCartButtonToCard(card, { sku, name, price, image });
      }
    });
  };

  // Run setup after a delay to ensure all content is loaded
  setTimeout(setupExistingProducts, 500);

  console.log('🛒 Lotus Cart System Loaded Successfully');

  //]]>
  </script>

  <!-- ============================================================================
       LOTUS GLASS - COMPLETE CHECKOUT SYSTEM JAVASCRIPT
       ============================================================================ -->
  <script type="text/javascript">
  //<![CDATA[

  // ============================================================================
  // CHECKOUT CONFIGURATION
  // ============================================================================

  const CHECKOUT_CONFIG = {
    // Validation settings
    PHONE_REGEX: /^(\+84|0)[1-9]\d{8}$/,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    TAX_ID_REGEX: /^\d{10,13}$/,

    // Form settings
    MAX_NOTES_LENGTH: 500,
    AUTO_SAVE_INTERVAL: 30000, // 30 seconds

    // Payment settings
    COD_FEE_RATE: 0.02, // 2% COD fee
    COD_MIN_FEE: 15000, // Minimum 15k VND
    COD_MAX_FEE: 50000, // Maximum 50k VND
    FREE_SHIPPING_THRESHOLD: 500000, // 500k VND

    // API endpoints
    ENDPOINTS: {
      CREATE_ORDER: 'create_order',
      VALIDATE_CART: 'validate_cart',
      CALCULATE_SHIPPING: 'calculate_shipping',
      APPLY_PROMOTION: 'apply_promotion',
      VNPAY_CREATE: 'vnpay_create_payment',
      ORDER_STATUS: 'get_order_status'
    },

    // Error messages
    ERRORS: {
      REQUIRED_FIELD: 'Trường này là bắt buộc',
      INVALID_PHONE: 'Số điện thoại không hợp lệ (10-11 số, bắt đầu bằng 0)',
      INVALID_EMAIL: 'Địa chỉ email không hợp lệ',
      INVALID_TAX_ID: 'Mã số thuế phải có 10-13 chữ số',
      FORM_VALIDATION: 'Vui lòng kiểm tra và sửa các lỗi trong form',
      ORDER_CREATION: 'Có lỗi xảy ra khi tạo đơn hàng. Vui lòng thử lại.',
      NETWORK_ERROR: 'Không thể kết nối tới server. Vui lòng kiểm tra kết nối mạng.',
      EMPTY_CART: 'Giỏ hàng trống. Vui lòng thêm sản phẩm trước khi thanh toán.'
    }
  };

  // ============================================================================
  // MAIN CHECKOUT SYSTEM
  // ============================================================================

  const LotusCheckout = {
    // Internal state
    currentStep: 1,
    isOpen: false,
    isProcessing: false,
    formData: {},
    orderData: null,

    // DOM elements cache
    elements: {},

    /**
     * Initialize checkout system
     */
    init() {
      this.cacheElements();
      this.bindEvents();
      this.loadSavedData();

      console.log('💳 Lotus Checkout initialized');
    },

    /**
     * Cache DOM elements for performance
     */
    cacheElements() {
      this.elements = {
        modal: document.getElementById('checkout-modal'),
        container: document.querySelector('.checkout-container'),
        closeBtn: document.getElementById('checkout-close'),
        steps: document.querySelectorAll('.step'),
        stepContents: document.querySelectorAll('.checkout-step'),
        backBtn: document.getElementById('checkout-back'),
        continueBtn: document.getElementById('checkout-continue'),
        loading: document.getElementById('checkout-loading'),

        // Form elements
        customerName: document.getElementById('customer-name'),
        customerPhone: document.getElementById('customer-phone'),
        customerEmail: document.getElementById('customer-email'),
        shippingAddress: document.getElementById('shipping-address'),
        shippingWard: document.getElementById('shipping-ward'),
        shippingDistrict: document.getElementById('shipping-district'),
        shippingCity: document.getElementById('shipping-city'),
        shippingPostal: document.getElementById('shipping-postal'),
        orderNotes: document.getElementById('order-notes'),

        // Payment elements
        paymentMethods: document.querySelectorAll('.payment-method'),

        // Summary elements
        checkoutItems: document.getElementById('checkout-items'),
        checkoutSubtotal: document.getElementById('checkout-subtotal'),
        checkoutShipping: document.getElementById('checkout-shipping'),
        checkoutCodFee: document.getElementById('checkout-cod-fee'),
        checkoutTotal: document.getElementById('checkout-total'),

        // Result modals
        successModal: document.getElementById('checkout-success-modal'),
        errorModal: document.getElementById('checkout-error-modal'),
        errorMessage: document.getElementById('checkout-error-message')
      };
    },

    /**
     * Bind event listeners
     */
    bindEvents() {
      // Close modal events
      if (this.elements.closeBtn) {
        this.elements.closeBtn.addEventListener('click', () => this.close());
      }

      // Navigation events
      if (this.elements.backBtn) {
        this.elements.backBtn.addEventListener('click', () => this.previousStep());
      }

      if (this.elements.continueBtn) {
        this.elements.continueBtn.addEventListener('click', () => this.nextStep());
      }

      // Payment method selection
      this.elements.paymentMethods.forEach(method => {
        method.addEventListener('click', () => this.selectPaymentMethod(method.dataset.method));
      });

      // Form validation events
      this.bindFormValidation();

      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen) {
          this.close();
        }
      });

      // Auto-save form data
      setInterval(() => this.saveFormData(), CHECKOUT_CONFIG.AUTO_SAVE_INTERVAL);
    },


    /**
     * Bind form validation events
     */
    bindFormValidation() {
      const inputs = [
        this.elements.customerName,
        this.elements.customerPhone,
        this.elements.customerEmail,
        this.elements.shippingAddress,
        this.elements.shippingWard,
        this.elements.shippingDistrict,
        this.elements.shippingCity
      ].filter(Boolean);

      inputs.forEach(input => {
        input.addEventListener('blur', () => this.validateField(input));
        input.addEventListener('input', () => this.clearFieldError(input));
      });
    },

    /**
     * Open checkout modal
     */
    open() {
      // Check if cart has items
      if (!window.LotusCart || LotusCart.items.length === 0) {
        this.showError(CHECKOUT_CONFIG.ERRORS.EMPTY_CART);
        return;
      }

      this.isOpen = true;
      this.currentStep = 1;
      this.updateStepDisplay();
      this.updateOrderSummary();
      this.elements.modal.classList.add('active');
      document.body.style.overflow = 'hidden';

      // Focus management
      setTimeout(() => {
        const firstInput = this.elements.customerName;
        if (firstInput) firstInput.focus();
      }, 100);
    },

    /**
     * Close checkout modal
     */
    close() {
      if (!this.isOpen) return;

      this.isOpen = false;
      this.elements.modal.classList.remove('active');
      document.body.style.overflow = '';

      // Reset form if not processing
      if (!this.isProcessing) {
        this.resetForm();
      }
    },

    /**
     * Navigate to next step
     */
    nextStep() {
      if (this.isProcessing) return;

      // Validate current step
      if (!this.validateCurrentStep()) {
        return;
      }

      if (this.currentStep < 3) {
        this.currentStep++;
        this.updateStepDisplay();
      } else {
        // Final step - submit order
        this.submitOrder();
      }
    },

    /**
     * Navigate to previous step
     */
    previousStep() {
      if (this.isProcessing) return;

      if (this.currentStep > 1) {
        this.currentStep--;
        this.updateStepDisplay();
      }
    },

    /**
     * Update step display
     */
    updateStepDisplay() {
      // Update step indicators
      this.elements.steps.forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed');

        if (stepNumber < this.currentStep) {
          step.classList.add('completed');
        } else if (stepNumber === this.currentStep) {
          step.classList.add('active');
        }
      });

      // Update step content
      this.elements.stepContents.forEach((content, index) => {
        content.classList.remove('active');
        if (index + 1 === this.currentStep) {
          content.classList.add('active');
        }
      });

      // Update navigation buttons
      this.elements.backBtn.style.display = this.currentStep > 1 ? 'flex' : 'none';

      if (this.currentStep === 3) {
        this.elements.continueBtn.innerHTML = `
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
          </svg>
          Đặt hàng
        `;
      } else {
        this.elements.continueBtn.innerHTML = `
          Tiếp tục
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M4 11h12.17l-5.59-5.59L12 4l8 8-8 8-1.41-1.41L16.17 13H4v-2z"/>
          </svg>
        `;
      }

      // Update order confirmation if on step 3
      if (this.currentStep === 3) {
        this.updateOrderConfirmation();
      }
    },

    /**
     * Validate current step
     */
    validateCurrentStep() {
      switch (this.currentStep) {
        case 1:
          return this.validateCustomerInfo();
        case 2:
          return this.validatePaymentMethod();
        case 3:
          return true; // Confirmation step
        default:
          return false;
      }
    },

    /**
     * Validate customer information
     */
    validateCustomerInfo() {
      let isValid = true;

      // Required fields
      const requiredFields = [
        { element: this.elements.customerName, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD },
        { element: this.elements.customerPhone, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD },
        { element: this.elements.shippingAddress, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD },
        { element: this.elements.shippingWard, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD },
        { element: this.elements.shippingDistrict, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD },
        { element: this.elements.shippingCity, message: CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD }
      ];

      requiredFields.forEach(field => {
        if (!field.element.value.trim()) {
          this.showFieldError(field.element, field.message);
          isValid = false;
        }
      });

      // Phone validation
      if (this.elements.customerPhone.value && !CHECKOUT_CONFIG.PHONE_REGEX.test(this.elements.customerPhone.value)) {
        this.showFieldError(this.elements.customerPhone, CHECKOUT_CONFIG.ERRORS.INVALID_PHONE);
        isValid = false;
      }

      // Email validation (if provided)
      if (this.elements.customerEmail.value && !CHECKOUT_CONFIG.EMAIL_REGEX.test(this.elements.customerEmail.value)) {
        this.showFieldError(this.elements.customerEmail, CHECKOUT_CONFIG.ERRORS.INVALID_EMAIL);
        isValid = false;
      }

      return isValid;
    },

    /**
     * Validate payment method selection
     */
    validatePaymentMethod() {
      const selectedMethod = document.querySelector('.payment-method.selected');
      if (!selectedMethod) {
        this.showError('Vui lòng chọn phương thức thanh toán');
        return false;
      }
      return true;
    },


    /**
     * Show field validation error
     */
    showFieldError(field, message) {
      field.classList.add('error');
      const errorElement = field.parentNode.querySelector('.form-error');
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
      }
    },

    /**
     * Clear field validation error
     */
    clearFieldError(field) {
      field.classList.remove('error');
      const errorElement = field.parentNode.querySelector('.form-error');
      if (errorElement) {
        errorElement.classList.remove('show');
      }
    },

    /**
     * Validate individual field
     */
    validateField(field) {
      this.clearFieldError(field);

      if (field.hasAttribute('required') && !field.value.trim()) {
        this.showFieldError(field, CHECKOUT_CONFIG.ERRORS.REQUIRED_FIELD);
        return false;
      }

      if (field.type === 'tel' && field.value && !CHECKOUT_CONFIG.PHONE_REGEX.test(field.value)) {
        this.showFieldError(field, CHECKOUT_CONFIG.ERRORS.INVALID_PHONE);
        return false;
      }

      if (field.type === 'email' && field.value && !CHECKOUT_CONFIG.EMAIL_REGEX.test(field.value)) {
        this.showFieldError(field, CHECKOUT_CONFIG.ERRORS.INVALID_EMAIL);
        return false;
      }

      field.classList.add('success');
      return true;
    },

    /**
     * Select payment method
     */
    selectPaymentMethod(method) {
      // Remove previous selection
      this.elements.paymentMethods.forEach(pm => pm.classList.remove('selected'));

      // Select new method
      const selectedMethod = document.querySelector(`[data-method="${method}"]`);
      if (selectedMethod) {
        selectedMethod.classList.add('selected');
        this.updateOrderSummary(); // Update totals based on payment method
      }
    },

    /**
     * Update order summary
     */
    updateOrderSummary() {
      if (!window.LotusCart) return;

      const cartData = LotusCart.getCartData();
      const { items, totals } = cartData;

      // Update items display
      this.elements.checkoutItems.innerHTML = items.map(item => `
        <div class="summary-item">
          <div class="item-info">
            <div class="item-name">${item.name}</div>
            <div class="item-details">${item.variant ? item.variant + ' - ' : ''}${item.quantity}x ${LotusUtils.formatPrice(item.price)}</div>
          </div>
          <div class="item-price">${LotusUtils.formatPrice(item.price * item.quantity)}</div>
        </div>
      `).join('');

      // Calculate totals
      const subtotal = totals.subtotal;
      const shipping = this.calculateShipping(subtotal);
      const codFee = this.calculateCODFee(subtotal);
      const total = subtotal + shipping + codFee;

      // Update totals display
      this.elements.checkoutSubtotal.textContent = LotusUtils.formatPrice(subtotal);
      this.elements.checkoutShipping.textContent = LotusUtils.formatPrice(shipping);
      this.elements.checkoutCodFee.textContent = LotusUtils.formatPrice(codFee);
      this.elements.checkoutTotal.textContent = LotusUtils.formatPrice(total);
    },

    /**
     * Calculate shipping fee
     */
    calculateShipping(subtotal) {
      // Free shipping for orders over threshold
      if (subtotal >= CHECKOUT_CONFIG.FREE_SHIPPING_THRESHOLD) {
        return 0;
      }

      // Basic shipping calculation - can be enhanced with location-based pricing
      return 30000; // 30k VND standard shipping
    },

    /**
     * Calculate COD fee
     */
    calculateCODFee(subtotal) {
      const selectedMethod = document.querySelector('.payment-method.selected');
      if (!selectedMethod || selectedMethod.dataset.method !== 'cod') {
        return 0;
      }

      const fee = Math.round(subtotal * CHECKOUT_CONFIG.COD_FEE_RATE);
      return Math.max(CHECKOUT_CONFIG.COD_MIN_FEE, Math.min(CHECKOUT_CONFIG.COD_MAX_FEE, fee));
    },

    /**
     * Update order confirmation display
     */
    updateOrderConfirmation() {
      const formData = this.collectFormData();
      const cartData = LotusCart.getCartData();
      const selectedMethod = document.querySelector('.payment-method.selected');

      const confirmationHTML = `
        <div class="confirmation-section">
          <h4>Thông tin khách hàng</h4>
          <p><strong>Họ tên:</strong> ${formData.customerName}</p>
          <p><strong>Điện thoại:</strong> ${formData.customerPhone}</p>
          ${formData.customerEmail ? `<p><strong>Email:</strong> ${formData.customerEmail}</p>` : ''}
        </div>

        <div class="confirmation-section">
          <h4>Địa chỉ giao hàng</h4>
          <p>${formData.shippingAddress}</p>
          <p>${formData.shippingWard}, ${formData.shippingDistrict}</p>
          <p>${formData.shippingCity}</p>
        </div>

        <div class="confirmation-section">
          <h4>Phương thức thanh toán</h4>
          <p>${this.getPaymentMethodName(selectedMethod?.dataset.method)}</p>
        </div>

        ${formData.orderNotes ? `
        <div class="confirmation-section">
          <h4>Ghi chú</h4>
          <p>${formData.orderNotes}</p>
        </div>
        ` : ''}
      `;

      document.getElementById('order-confirmation').innerHTML = confirmationHTML;
    },

    /**
     * Get payment method display name
     */
    getPaymentMethodName(method) {
      const names = {
        vnpay: 'VNPay - Thanh toán online',
        bank_transfer: 'Chuyển khoản ngân hàng',
        cod: 'Thanh toán khi nhận hàng (COD)'
      };
      return names[method] || 'Chưa chọn';
    },

    /**
     * Collect form data
     */
    collectFormData() {
      return {
        customerName: this.elements.customerName?.value.trim() || '',
        customerPhone: this.elements.customerPhone?.value.trim() || '',
        customerEmail: this.elements.customerEmail?.value.trim() || '',
        shippingAddress: this.elements.shippingAddress?.value.trim() || '',
        shippingWard: this.elements.shippingWard?.value.trim() || '',
        shippingDistrict: this.elements.shippingDistrict?.value.trim() || '',
        shippingCity: this.elements.shippingCity?.value || '',
        shippingPostal: this.elements.shippingPostal?.value.trim() || '',
        orderNotes: this.elements.orderNotes?.value.trim() || '',
        paymentMethod: document.querySelector('.payment-method.selected')?.dataset.method || ''
      };
    },


    /**
     * Submit order
     */
    async submitOrder() {
      if (this.isProcessing) return;

      this.isProcessing = true;
      this.showLoading(true);

      try {
        const formData = this.collectFormData();
        const cartData = LotusCart.getCartData();

        // Prepare order data
        const orderData = {
          customer: {
            name: formData.customerName,
            phone: formData.customerPhone,
            email: formData.customerEmail
          },
          shipping: {
            address: formData.shippingAddress,
            ward: formData.shippingWard,
            district: formData.shippingDistrict,
            city: formData.shippingCity,
            postal: formData.shippingPostal
          },
          items: cartData.items,
          payment: {
            method: formData.paymentMethod,
            subtotal: cartData.totals.subtotal,
            shipping: this.calculateShipping(cartData.totals.subtotal),
            codFee: this.calculateCODFee(cartData.totals.subtotal),
            total: cartData.totals.subtotal + this.calculateShipping(cartData.totals.subtotal) + this.calculateCODFee(cartData.totals.subtotal)
          },
          notes: formData.orderNotes,
          timestamp: Date.now()
        };

        // Submit to backend
        const response = await this.submitToBackend(orderData);

        if (response.success) {
          this.orderData = response.order;
          this.handleOrderSuccess(response);
        } else {
          throw new Error(response.message || 'Order submission failed');
        }

      } catch (error) {
        console.error('Order submission error:', error);
        this.handleOrderError(error.message);
      } finally {
        this.isProcessing = false;
        this.showLoading(false);
      }
    },

    /**
     * Submit order to backend
     */
    async submitToBackend(orderData) {
      const url = LOTUS_CONFIG.API_BASE_URL;

      const formData = new FormData();
      formData.append('action', CHECKOUT_CONFIG.ENDPOINTS.CREATE_ORDER);
      formData.append('orderData', JSON.stringify(orderData));

      const response = await fetch(url, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    },

    /**
     * Handle successful order submission
     */
    handleOrderSuccess(response) {
      // Clear cart
      if (window.LotusCart) {
        LotusCart.clearCart();
      }

      // Close checkout modal
      this.close();

      // Handle payment method specific actions
      if (response.order.payment.method === 'vnpay' && response.paymentUrl) {
        // Redirect to VNPay
        window.location.href = response.paymentUrl;
      } else {
        // Show success modal
        this.showSuccessModal();
      }

      // Save order data for reference
      this.saveOrderData(response.order);
    },

    /**
     * Handle order submission error
     */
    handleOrderError(message) {
      this.showErrorModal(message || CHECKOUT_CONFIG.ERRORS.ORDER_CREATION);
    },

    /**
     * Show loading state
     */
    showLoading(show) {
      this.elements.loading.classList.toggle('show', show);
      this.elements.continueBtn.disabled = show;
      this.elements.backBtn.disabled = show;
    },

    /**
     * Show success modal
     */
    showSuccessModal() {
      this.elements.successModal.classList.add('show');
    },

    /**
     * Close success modal
     */
    closeSuccessModal() {
      this.elements.successModal.classList.remove('show');
    },

    /**
     * Show error modal
     */
    showErrorModal(message) {
      this.elements.errorMessage.textContent = message;
      this.elements.errorModal.classList.add('show');
    },

    /**
     * Close error modal
     */
    closeErrorModal() {
      this.elements.errorModal.classList.remove('show');
    },

    /**
     * Show general error message
     */
    showError(message) {
      // You can implement a toast notification here
      alert(message); // Simple fallback
    },

    /**
     * Save form data to localStorage
     */
    saveFormData() {
      if (!this.isOpen) return;

      const formData = this.collectFormData();
      LotusUtils.storage.set('lotus_checkout_form', formData);
    },

    /**
     * Load saved form data
     */
    loadSavedData() {
      const savedData = LotusUtils.storage.get('lotus_checkout_form');
      if (!savedData) return;

      // Populate form fields
      Object.keys(savedData).forEach(key => {
        const element = this.elements[key];
        if (element && savedData[key]) {
          element.value = savedData[key];
        }
      });
    },

    /**
     * Save order data for reference
     */
    saveOrderData(orderData) {
      const orders = LotusUtils.storage.get('lotus_orders', []);
      orders.unshift(orderData);

      // Keep only last 10 orders
      if (orders.length > 10) {
        orders.splice(10);
      }

      LotusUtils.storage.set('lotus_orders', orders);
    },

    /**
     * Reset form
     */
    resetForm() {
      // Clear form fields
      const inputs = this.elements.container.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.value = '';
        input.classList.remove('error', 'success');
      });

      // Clear errors
      const errors = this.elements.container.querySelectorAll('.form-error');
      errors.forEach(error => error.classList.remove('show'));

      // Reset payment method selection
      this.elements.paymentMethods.forEach(method => method.classList.remove('selected'));

      // Reset step
      this.currentStep = 1;
      this.updateStepDisplay();

      // Clear saved data
      LotusUtils.storage.remove('lotus_checkout_form');
    }
  };


  // ============================================================================
  // CHECKOUT INITIALIZATION
  // ============================================================================

  // Initialize checkout when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    // Wait for cart system to load first
    setTimeout(() => {
      LotusCheckout.init();
    }, 200);
  });

  // Initialize checkout if DOM is already loaded
  if (document.readyState === 'loading') {
    // DOM is still loading
  } else {
    // DOM is already loaded
    setTimeout(() => {
      LotusCheckout.init();
    }, 200);
  }

  // Expose checkout to global scope
  window.LotusCheckout = LotusCheckout;

  // ============================================================================
  // INTEGRATION WITH CART SYSTEM
  // ============================================================================

  // Update cart's proceedToCheckout method to use new checkout system
  if (window.LotusCart) {
    LotusCart.proceedToCheckout = function() {
      if (this.items.length === 0) {
        this.showNotification('Giỏ hàng trống. Vui lòng thêm sản phẩm trước khi thanh toán.', 'warning');
        return;
      }

      // Close cart and open checkout
      this.closeCart();

      setTimeout(() => {
        if (window.LotusCheckout) {
          LotusCheckout.open();
        }
      }, 300);
    };
  }

  // ============================================================================
  // LEGACY SUPPORT & COMPATIBILITY
  // ============================================================================

  // Support for manual checkout operations
  window.openCheckout = function() {
    return LotusCheckout.open();
  };

  window.closeCheckout = function() {
    return LotusCheckout.close();
  };

  // ============================================================================
  // PAYMENT GATEWAY INTEGRATION
  // ============================================================================

  // VNPay return handler
  window.handleVNPayReturn = function(params) {
    // This will be called when user returns from VNPay
    const urlParams = new URLSearchParams(window.location.search);
    const responseCode = urlParams.get('vnp_ResponseCode');

    if (responseCode === '00') {
      // Payment successful
      LotusCheckout.showSuccessModal();
    } else {
      // Payment failed
      LotusCheckout.showErrorModal('Thanh toán không thành công. Vui lòng thử lại.');
    }
  };

  // Check for VNPay return on page load
  if (window.location.search.includes('vnp_ResponseCode')) {
    setTimeout(() => {
      handleVNPayReturn();
    }, 1000);
  }

  console.log('💳 Lotus Checkout System Loaded Successfully');

  //]]>
  </script>
    <div id="cart-sidebar" class="cart-sidebar" role="dialog" aria-labelledby="cart-title" aria-hidden="true">
    <!-- Cart Header -->
    <div class="cart-header">
      <h3 id="cart-title">Giỏ hàng của bạn</h3>
      <button id="cart-close" class="cart-close" onclick="LotusCart.closeCart()" aria-label="Đóng giỏ hàng">
        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
    </div>
    
    <!-- Cart Items Container -->
    <div id="cart-items" class="cart-items">
      <!-- Empty State -->
      <div id="cart-empty" class="cart-empty">
        <svg class="cart-empty-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
        </svg>
        <h4>Giỏ hàng trống</h4>
        <p>Hãy thêm sản phẩm để tiếp tục mua sắm</p>
      </div>
      
      <!-- Loading State -->
      <div id="cart-loading" class="cart-loading" style="display: none;">
        <div class="cart-spinner"></div>
        <span>Đang tải...</span>
      </div>
      
      <!-- Cart Items will be dynamically inserted here -->
    </div>
    
    <!-- Cart Footer -->
    <div class="cart-footer">
      <!-- Cart Totals -->
      <div class="cart-totals">
        <div class="subtotal">
          <span>Tạm tính:</span>
          <span id="cart-subtotal" class="subtotal-amount">0₫</span>
        </div>
        <div class="shipping-note">
          💡 Phí vận chuyển sẽ được tính khi thanh toán
        </div>
      </div>
      
      <!-- Cart Actions -->
      <div class="cart-actions">
        <button id="continue-shopping" class="cart-btn cart-btn-secondary" onclick="LotusCart.closeCart()">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
          </svg>
          Tiếp tục mua sắm
        </button>
        <button id="proceed-checkout" class="cart-btn cart-btn-primary" onclick="LotusCart.proceedToCheckout()" disabled="disabled">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
          </svg>
          Thanh toán
        </button>
      </div>
    </div>
  </div>

  <!-- Cart Overlay -->
  <div id="cart-overlay" class="cart-overlay" onclick="LotusCart.closeCart()" aria-hidden="true"></div>

  <!-- Cart Notification -->
  <div id="cart-notification" class="cart-notification">
    <div class="notification-content">
      <svg class="notification-icon" viewBox="0 0 24 24" fill="currentColor">
        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
      </svg>
      <div class="notification-text">
        <div id="notification-message">Đã thêm sản phẩm vào giỏ hàng</div>
      </div>
    </div>
  </div>

  <!-- ============================================================================
       LOTUS GLASS - CHECKOUT MODAL
       ============================================================================ -->
  <div id="checkout-modal" class="checkout-modal" role="dialog" aria-labelledby="checkout-title" aria-hidden="true">
    <div class="checkout-container">
      <!-- Checkout Header -->
      <div class="checkout-header">
        <h2 id="checkout-title" class="checkout-title">Thanh toán đơn hàng</h2>
        <button id="checkout-close" class="checkout-close" onclick="LotusCheckout.close()" aria-label="Đóng thanh toán">
          <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>

      <!-- Checkout Steps -->
      <div class="checkout-steps">
        <div class="step active" data-step="1">
          <div class="step-number">1</div>
          <span>Thông tin</span>
        </div>
        <div class="step" data-step="2">
          <div class="step-number">2</div>
          <span>Thanh toán</span>
        </div>
        <div class="step" data-step="3">
          <div class="step-number">3</div>
          <span>Xác nhận</span>
        </div>
      </div>

      <!-- Checkout Content -->
      <div class="checkout-content">
        <!-- Checkout Form -->
        <div class="checkout-form">
          <!-- Step 1: Customer Information -->
          <div id="checkout-step-1" class="checkout-step active">
            <!-- Customer Info Section -->
            <div class="form-section">
              <h3 class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                Thông tin khách hàng
              </h3>

              <div class="form-row">
                <div class="form-group">
                  <label for="customer-name" class="form-label required">Họ và tên</label>
                  <input type="text" id="customer-name" class="form-input" placeholder="Nhập họ và tên" required="required"/>
                  <div class="form-error">Vui lòng nhập họ và tên</div>
                </div>
                <div class="form-group">
                  <label for="customer-phone" class="form-label required">Số điện thoại</label>
                  <input type="tel" id="customer-phone" class="form-input" placeholder="0123456789" required="required"/>
                  <div class="form-error">Số điện thoại không hợp lệ</div>
                </div>
              </div>

              <div class="form-group">
                <label for="customer-email" class="form-label">Email</label>
                <input type="email" id="customer-email" class="form-input" placeholder="<EMAIL>"/>
                <div class="form-error">Email không hợp lệ</div>
              </div>
            </div>

            <!-- Shipping Address Section -->
            <div class="form-section">
              <h3 class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
                Địa chỉ giao hàng
              </h3>

              <div class="form-group">
                <label for="shipping-address" class="form-label required">Địa chỉ</label>
                <input type="text" id="shipping-address" class="form-input" placeholder="Số nhà, tên đường" required="required"/>
                <div class="form-error">Vui lòng nhập địa chỉ</div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="shipping-ward" class="form-label required">Phường/Xã</label>
                  <input type="text" id="shipping-ward" class="form-input" placeholder="Phường/Xã" required="required"/>
                  <div class="form-error">Vui lòng nhập phường/xã</div>
                </div>
                <div class="form-group">
                  <label for="shipping-district" class="form-label required">Quận/Huyện</label>
                  <input type="text" id="shipping-district" class="form-input" placeholder="Quận/Huyện" required="required"/>
                  <div class="form-error">Vui lòng nhập quận/huyện</div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="shipping-city" class="form-label required">Tỉnh/Thành phố</label>
                  <select id="shipping-city" class="form-input form-select" required="required">
                    <option value="">Chọn tỉnh/thành phố</option>
                    <option value="hanoi">Hà Nội</option>
                    <option value="hcm">TP. Hồ Chí Minh</option>
                    <option value="danang">Đà Nẵng</option>
                    <option value="haiphong">Hải Phòng</option>
                    <option value="cantho">Cần Thơ</option>
                  </select>
                  <div class="form-error">Vui lòng chọn tỉnh/thành phố</div>
                </div>
                <div class="form-group">
                  <label for="shipping-postal" class="form-label">Mã bưu điện</label>
                  <input type="text" id="shipping-postal" class="form-input" placeholder="100000"/>
                </div>
              </div>
            </div>

            <!-- Order Notes Section -->
            <div class="form-section">
              <h3 class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,10H19.5L14,4.5V10M5,3H15L21,9V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3M9,12H16V14H9V12M9,16H13V18H9V16Z"/>
                </svg>
                Ghi chú đơn hàng
              </h3>

              <div class="form-group">
                <label for="order-notes" class="form-label">Ghi chú (tùy chọn)</label>
                <textarea id="order-notes" class="form-input form-textarea" placeholder="Ghi chú về đơn hàng, yêu cầu đặc biệt..."></textarea>
              </div>
            </div>
          </div>

          <!-- Step 2: Payment Method -->
          <div id="checkout-step-2" class="checkout-step">
            <div class="form-section">
              <h3 class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20,8H4V6H20M20,18H4V12H20M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,5.11 21.11,4 20,4Z"/>
                </svg>
                Phương thức thanh toán
              </h3>

              <div class="payment-methods">
                <!-- VNPay Payment -->
                <div class="payment-method" data-method="vnpay">
                  <div class="payment-method-header">
                    <div class="payment-radio"></div>
                    <div class="payment-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20,8H4V6H20M20,18H4V12H20M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,5.11 21.11,4 20,4Z"/>
                      </svg>
                    </div>
                    <div class="payment-info">
                      <h4>VNPay</h4>
                      <p>Thanh toán online qua thẻ ATM, Visa, MasterCard</p>
                    </div>
                  </div>
                  <div class="payment-details">
                    <p>Bạn sẽ được chuyển đến cổng thanh toán VNPay để hoàn tất giao dịch.</p>
                  </div>
                </div>

                <!-- Bank Transfer -->
                <div class="payment-method" data-method="bank_transfer">
                  <div class="payment-method-header">
                    <div class="payment-radio"></div>
                    <div class="payment-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M5,6H23V18H5V6M14,9A3,3 0 0,1 17,12A3,3 0 0,1 14,15A3,3 0 0,1 11,12A3,3 0 0,1 14,9M9,8A2,2 0 0,1 7,10V14A2,2 0 0,1 9,16H19A2,2 0 0,1 21,14V10A2,2 0 0,1 19,8H9Z"/>
                      </svg>
                    </div>
                    <div class="payment-info">
                      <h4>Chuyển khoản ngân hàng</h4>
                      <p>Chuyển khoản trực tiếp vào tài khoản ngân hàng</p>
                    </div>
                  </div>
                  <div class="payment-details">
                    <p><strong>Ngân hàng:</strong> Vietcombank</p>
                    <p><strong>Số tài khoản:</strong> **********</p>
                    <p><strong>Chủ tài khoản:</strong> LOTUS GLASS VIETNAM</p>
                    <p><strong>Nội dung:</strong> Thanh toán đơn hàng [Mã đơn hàng]</p>
                  </div>
                </div>

                <!-- Cash on Delivery -->
                <div class="payment-method" data-method="cod">
                  <div class="payment-method-header">
                    <div class="payment-radio"></div>
                    <div class="payment-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M7,22A2,2 0 0,1 5,20V4A2,2 0 0,1 7,2H17A2,2 0 0,1 19,4V20A2,2 0 0,1 17,22H7Z"/>
                      </svg>
                    </div>
                    <div class="payment-info">
                      <h4>Thanh toán khi nhận hàng (COD)</h4>
                      <p>Thanh toán bằng tiền mặt khi nhận hàng</p>
                    </div>
                  </div>
                  <div class="payment-details">
                    <p>Phí COD: 2% giá trị đơn hàng (tối thiểu 15,000₫, tối đa 50,000₫)</p>
                    <p>Bạn có thể kiểm tra hàng trước khi thanh toán.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 3: Order Confirmation -->
          <div id="checkout-step-3" class="checkout-step">
            <div class="form-section">
              <h3 class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                </svg>
                Xác nhận đơn hàng
              </h3>

              <div id="order-confirmation">
                <!-- Order confirmation content will be populated by JavaScript -->
              </div>
            </div>
          </div>

          <!-- Loading State -->
          <div id="checkout-loading" class="checkout-loading">
            <div class="checkout-spinner"></div>
            <span>Đang xử lý đơn hàng...</span>
          </div>
        </div>

        <!-- Order Summary Sidebar -->
        <div class="checkout-summary">
          <div class="order-summary">
            <h3 class="summary-title">Đơn hàng của bạn</h3>

            <div id="checkout-items" class="summary-items">
              <!-- Order items will be populated by JavaScript -->
            </div>

            <div class="summary-totals">
              <div class="total-row">
                <span class="total-label">Tạm tính:</span>
                <span id="checkout-subtotal" class="total-value">0₫</span>
              </div>
              <div class="total-row">
                <span class="total-label">Phí vận chuyển:</span>
                <span id="checkout-shipping" class="total-value">0₫</span>
              </div>
              <div class="total-row">
                <span class="total-label">Phí COD:</span>
                <span id="checkout-cod-fee" class="total-value">0₫</span>
              </div>
              <div class="total-row">
                <span class="total-label">Tổng cộng:</span>
                <span id="checkout-total" class="total-value">0₫</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Checkout Actions -->
      <div class="checkout-actions">
        <button id="checkout-back" class="checkout-btn checkout-btn-secondary" style="display: none;">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
          </svg>
          Quay lại
        </button>
        <button id="checkout-continue" class="checkout-btn checkout-btn-primary">
          Tiếp tục
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M4 11h12.17l-5.59-5.59L12 4l8 8-8 8-1.41-1.41L16.17 13H4v-2z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div id="checkout-success-modal" class="result-modal">
    <div class="result-content">
      <div class="result-icon success">
        <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
          <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
        </svg>
      </div>
      <h3 class="result-title">Đặt hàng thành công!</h3>
      <p class="result-message">Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.</p>
      <button class="checkout-btn checkout-btn-primary" onclick="LotusCheckout.closeSuccessModal()">
        Tiếp tục mua sắm
      </button>
    </div>
  </div>

  <!-- Error Modal -->
  <div id="checkout-error-modal" class="result-modal">
    <div class="result-content">
      <div class="result-icon error">
        <svg viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </div>
      <h3 class="result-title">Có lỗi xảy ra!</h3>
      <p id="checkout-error-message" class="result-message">Không thể xử lý đơn hàng. Vui lòng thử lại sau.</p>
      <button class="checkout-btn checkout-btn-primary" onclick="LotusCheckout.closeErrorModal()">
        Thử lại
      </button>
    </div>
  </div>

  <!-- ========================================
      CART ITEM TEMPLATE
      Template này sẽ được clone bằng JavaScript
      ======================================== -->
  <template id="cart-item-template" style="display: none;">
    <div class="cart-item" data-sku="">
      <div class="item-image">
        <img src="" alt="" loading="lazy"/>
      </div>
      
      <div class="item-details">
        <h4 class="item-name"></h4>
        <p class="item-variant"></p>
        <p class="item-price"></p>
      </div>
      
      <div class="item-controls">
        <!-- Quantity Controls -->
        <div class="quantity-controls">
          <button class="qty-btn qty-decrease" onclick="LotusCart.updateQuantity(this.closest(&apos;.cart-item&apos;).dataset.sku, -1)" aria-label="Giảm số lượng">
            <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
              <path d="M19 13H5v-2h14v2z"/>
            </svg>
          </button>
          <input type="number" class="qty-input" min="1" max="999" onchange="LotusCart.setQuantity(this.closest(&apos;.cart-item&apos;).dataset.sku, this.value)" aria-label="Số lượng"/>
          <button class="qty-btn qty-increase" onclick="LotusCart.updateQuantity(this.closest(&apos;.cart-item&apos;).dataset.sku, 1)" aria-label="Tăng số lượng">
            <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
          </button>
        </div>
        
        <!-- Item Total -->
        <div class="item-total"></div>
        
        <!-- Remove Button -->
        <button class="remove-item" onclick="LotusCart.removeItem(this.closest(&apos;.cart-item&apos;).dataset.sku)" aria-label="Xóa sản phẩm">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </button>
      </div>
    </div>
  </template>
</body>
</html>

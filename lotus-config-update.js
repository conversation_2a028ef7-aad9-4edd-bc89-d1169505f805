/**
 * 🌸 LOTUS GLASS - CẤU HÌNH API MỚI CHO CODE-GAS.GS
 * File này chứa cấu hình cập nhật để tích hợp với code-gas.gs v5.0
 */

// ===================================================================================
// CẤU HÌNH API MỚI - THAY THẾ TRONG BLOGTHEMEN.XML
// ===================================================================================

const LOTUS_CONFIG_V5 = {
  // API URL - CẬP NHẬT URL MỚI CỦA BẠN TẠI ĐÂY
  API_URL: "https://script.google.com/macros/s/AKfycbzKMKloNLg4AzzqCc4CPPXhCALge38LprQKeM242qDicVf8fflpRslAGKWbFb8uOlGBYA/exec",
  
  // Cấu hình Cache phía client
  CACHE: {
    ENABLED: true,
    PREFIX: 'LG_CLIENT_V5_',
    DEFAULT_TTL_MS: 300000, // 5 phút
    CATEGORIES_TTL_MS: 600000, // 10 phút cho categories
    PRODUCTS_TTL_MS: 180000 // 3 phút cho products
  },
  
  // Cấu hình phân trang
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
  },
  
  // Cấu hình retry cho API calls
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY_MS: 1000
  },
  
  // Cấu hình debug
  DEBUG: {
    ENABLED: true,
    LOG_API_CALLS: true,
    LOG_CACHE_OPERATIONS: true
  }
};

// ===================================================================================
// CLIENT-SIDE CACHE MANAGER - TƯƠNG THÍCH VỚI SERVER CACHE
// ===================================================================================

const ClientCacheManager = {
  /**
   * Lấy dữ liệu từ localStorage cache
   */
  get: function(key) {
    if (!LOTUS_CONFIG_V5.CACHE.ENABLED) return null;
    
    try {
      const cacheKey = LOTUS_CONFIG_V5.CACHE.PREFIX + key;
      const cached = localStorage.getItem(cacheKey);
      
      if (!cached) return null;
      
      const data = JSON.parse(cached);
      const now = Date.now();
      
      // Kiểm tra TTL
      if (data.expiry && now > data.expiry) {
        localStorage.removeItem(cacheKey);
        return null;
      }
      
      if (LOTUS_CONFIG_V5.DEBUG.LOG_CACHE_OPERATIONS) {
        console.log(`📦 Cache HIT: ${key}`);
      }
      
      return data.value;
    } catch (e) {
      console.warn('⚠️ Cache get error:', e);
      return null;
    }
  },
  
  /**
   * Lưu dữ liệu vào localStorage cache
   */
  set: function(key, value, ttlMs = LOTUS_CONFIG_V5.CACHE.DEFAULT_TTL_MS) {
    if (!LOTUS_CONFIG_V5.CACHE.ENABLED) return;
    
    try {
      const cacheKey = LOTUS_CONFIG_V5.CACHE.PREFIX + key;
      const data = {
        value: value,
        expiry: Date.now() + ttlMs,
        created: Date.now()
      };
      
      localStorage.setItem(cacheKey, JSON.stringify(data));
      
      if (LOTUS_CONFIG_V5.DEBUG.LOG_CACHE_OPERATIONS) {
        console.log(`💾 Cache SET: ${key} (TTL: ${ttlMs}ms)`);
      }
    } catch (e) {
      console.warn('⚠️ Cache set error:', e);
    }
  },
  
  /**
   * Xóa một key khỏi cache
   */
  remove: function(key) {
    if (!LOTUS_CONFIG_V5.CACHE.ENABLED) return;
    
    const cacheKey = LOTUS_CONFIG_V5.CACHE.PREFIX + key;
    localStorage.removeItem(cacheKey);
    
    if (LOTUS_CONFIG_V5.DEBUG.LOG_CACHE_OPERATIONS) {
      console.log(`🗑️ Cache REMOVE: ${key}`);
    }
  },
  
  /**
   * Xóa tất cả cache của Lotus Glass
   */
  clearAll: function() {
    if (!LOTUS_CONFIG_V5.CACHE.ENABLED) return;
    
    const keys = Object.keys(localStorage);
    const lotusKeys = keys.filter(key => key.startsWith(LOTUS_CONFIG_V5.CACHE.PREFIX));
    
    lotusKeys.forEach(key => localStorage.removeItem(key));
    
    console.log(`🧹 Cleared ${lotusKeys.length} cache entries`);
  },
  
  /**
   * Lấy thông tin cache stats
   */
  getStats: function() {
    const keys = Object.keys(localStorage);
    const lotusKeys = keys.filter(key => key.startsWith(LOTUS_CONFIG_V5.CACHE.PREFIX));
    
    let totalSize = 0;
    let validEntries = 0;
    let expiredEntries = 0;
    const now = Date.now();
    
    lotusKeys.forEach(key => {
      try {
        const value = localStorage.getItem(key);
        totalSize += value.length;
        
        const data = JSON.parse(value);
        if (data.expiry && now > data.expiry) {
          expiredEntries++;
        } else {
          validEntries++;
        }
      } catch (e) {
        // Invalid cache entry
      }
    });
    
    return {
      totalEntries: lotusKeys.length,
      validEntries,
      expiredEntries,
      totalSizeBytes: totalSize,
      totalSizeKB: Math.round(totalSize / 1024)
    };
  }
};

// ===================================================================================
// ENHANCED API CLIENT - TƯƠNG THÍCH VỚI CODE-GAS.GS V5.0
// ===================================================================================

const LotusAPIClient = {
  /**
   * Thực hiện API call với retry và cache
   */
  async call(action, params = {}, options = {}) {
    const {
      useCache = true,
      cacheKey = null,
      cacheTTL = LOTUS_CONFIG_V5.CACHE.DEFAULT_TTL_MS,
      retryAttempts = LOTUS_CONFIG_V5.RETRY.MAX_ATTEMPTS
    } = options;
    
    // Tạo cache key nếu không được cung cấp
    const finalCacheKey = cacheKey || `${action}_${JSON.stringify(params)}`;
    
    // Kiểm tra cache trước
    if (useCache) {
      const cached = ClientCacheManager.get(finalCacheKey);
      if (cached) {
        return cached;
      }
    }
    
    // Thực hiện API call với retry
    let lastError = null;
    
    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        if (LOTUS_CONFIG_V5.DEBUG.LOG_API_CALLS) {
          console.log(`🌐 API Call: ${action} (Attempt ${attempt}/${retryAttempts})`);
        }
        
        const requestData = {
          action: action,
          ...params
        };
        
        const response = await fetch(LOTUS_CONFIG_V5.API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Kiểm tra response format từ code-gas.gs
        if (data.success === false) {
          throw new Error(data.message || 'API returned error');
        }
        
        // Lưu vào cache nếu thành công
        if (useCache && data.success) {
          ClientCacheManager.set(finalCacheKey, data, cacheTTL);
        }
        
        if (LOTUS_CONFIG_V5.DEBUG.LOG_API_CALLS) {
          console.log(`✅ API Success: ${action}`);
        }
        
        return data;
        
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ API Call failed (Attempt ${attempt}/${retryAttempts}):`, error.message);
        
        // Đợi trước khi retry (trừ lần cuối)
        if (attempt < retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, LOTUS_CONFIG_V5.RETRY.DELAY_MS * attempt));
        }
      }
    }
    
    // Tất cả attempts đều thất bại
    console.error(`🚨 API Call failed after ${retryAttempts} attempts:`, lastError);
    throw lastError;
  },
  
  /**
   * Lấy danh sách categories với cache
   */
  async getCategories() {
    return await this.call('getCategories', {}, {
      cacheKey: 'categories_tree',
      cacheTTL: LOTUS_CONFIG_V5.CACHE.CATEGORIES_TTL_MS
    });
  },
  
  /**
   * Lấy danh sách products với phân trang và lọc
   */
  async getProducts(params = {}) {
    const {
      page = 1,
      pageSize = LOTUS_CONFIG_V5.PAGINATION.DEFAULT_PAGE_SIZE,
      category = '',
      sort = '',
      query = '',
      featured = false,
      new: isNew = false
    } = params;
    
    return await this.call('getProducts', {
      page,
      pageSize,
      category,
      sort,
      query,
      featured,
      new: isNew
    }, {
      cacheKey: `products_p${page}_s${pageSize}_cat${category}_sort${sort}_q${query}_feat${featured}_new${isNew}`,
      cacheTTL: LOTUS_CONFIG_V5.CACHE.PRODUCTS_TTL_MS
    });
  },
  
  /**
   * Lấy chi tiết sản phẩm
   */
  async getProductDetail(productName) {
    return await this.call('getProductDetail', {
      product_name: productName
    }, {
      cacheKey: `product_detail_${productName.replace(/\s/g, '_')}`,
      cacheTTL: LOTUS_CONFIG_V5.CACHE.PRODUCTS_TTL_MS
    });
  },
  
  /**
   * DỰ BÁO TỒN KHO - TÍNH NĂNG MỚI
   */
  async predictInventory(daysToPredict = 30) {
    return await this.call('predictInventory', {
      daysToPredict
    }, {
      useCache: false // Không cache dự báo để có dữ liệu real-time
    });
  },
  
  /**
   * Health check API
   */
  async ping() {
    return await this.call('ping', {}, {
      useCache: false
    });
  },
  
  /**
   * Xóa cache server
   */
  async clearServerCache() {
    return await this.call('clearCache', {}, {
      useCache: false
    });
  }
};

// Export cho sử dụng global
window.LOTUS_CONFIG_V5 = LOTUS_CONFIG_V5;
window.ClientCacheManager = ClientCacheManager;
window.LotusAPIClient = LotusAPIClient;

console.log('🌸 Lotus Glass API Client V5.0 loaded successfully!');

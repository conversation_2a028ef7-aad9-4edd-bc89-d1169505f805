<!-- ============================================================================
     LOTUS GLASS - COMPLETE CART HTML STRUCTURE
     File: lotus-cart-html.html
     
     PART 1: Cart <PERSON>ge (thêm vào navigation)
     PART 2: Cart <PERSON> (thêm vào cuối body, trước </body>)
     ============================================================================ -->

<!-- ========================================
     PART 1: CART BADGE FOR NAVIGATION
     ======================================== -->

<!-- Thay thế <nav class="custom-nav"> existing bằng code này: -->
<nav class="custom-nav">
  <a href="/" class="nav-link">Trang chủ</a>
  <a href="/p/san-pham.html" class="nav-link">Sản phẩm</a>
  <a href="/p/gioi-thieu.html" class="nav-link">Giới thiệu</a>
  <a href="/p/tin-tuc.html" class="nav-link">Tin tức</a>
  
  <!-- Shopping Cart Badge -->
  <div id="cart-badge" class="cart-badge" onclick="LotusCart.openCart()" role="button" aria-label="Mở giỏ hàng">
    <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
      <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
    </svg>
    <span id="cart-count" class="cart-count" aria-live="polite">0</span>
  </div>
</nav>

<!-- ========================================
     PART 2: COMPLETE CART SIDEBAR
     Thêm vào cuối file, trước </body>
     ======================================== -->

<!-- Shopping Cart Sidebar -->
<div id="cart-sidebar" class="cart-sidebar" role="dialog" aria-labelledby="cart-title" aria-hidden="true">
  <!-- Cart Header -->
  <div class="cart-header">
    <h3 id="cart-title">Giỏ hàng của bạn</h3>
    <button id="cart-close" class="cart-close" onclick="LotusCart.closeCart()" aria-label="Đóng giỏ hàng">
      <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
      </svg>
    </button>
  </div>
  
  <!-- Cart Items Container -->
  <div id="cart-items" class="cart-items">
    <!-- Empty State -->
    <div id="cart-empty" class="cart-empty">
      <svg class="cart-empty-icon" viewBox="0 0 24 24" fill="currentColor">
        <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
      </svg>
      <h4>Giỏ hàng trống</h4>
      <p>Hãy thêm sản phẩm để tiếp tục mua sắm</p>
    </div>
    
    <!-- Loading State -->
    <div id="cart-loading" class="cart-loading" style="display: none;">
      <div class="cart-spinner"></div>
      <span>Đang tải...</span>
    </div>
    
    <!-- Cart Items will be dynamically inserted here -->
  </div>
  
  <!-- Cart Footer -->
  <div class="cart-footer">
    <!-- Cart Totals -->
    <div class="cart-totals">
      <div class="subtotal">
        <span>Tạm tính:</span>
        <span id="cart-subtotal" class="subtotal-amount">0₫</span>
      </div>
      <div class="shipping-note">
        💡 Phí vận chuyển sẽ được tính khi thanh toán
      </div>
    </div>
    
    <!-- Cart Actions -->
    <div class="cart-actions">
      <button id="continue-shopping" class="cart-btn cart-btn-secondary" onclick="LotusCart.closeCart()">
        <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        Tiếp tục mua sắm
      </button>
      <button id="proceed-checkout" class="cart-btn cart-btn-primary" onclick="LotusCart.proceedToCheckout()" disabled>
        <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
          <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
        </svg>
        Thanh toán
      </button>
    </div>
  </div>
</div>

<!-- Cart Overlay -->
<div id="cart-overlay" class="cart-overlay" onclick="LotusCart.closeCart()" aria-hidden="true"></div>

<!-- Cart Notification -->
<div id="cart-notification" class="cart-notification">
  <div class="notification-content">
    <svg class="notification-icon" viewBox="0 0 24 24" fill="currentColor">
      <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
    </svg>
    <div class="notification-text">
      <div id="notification-message">Đã thêm sản phẩm vào giỏ hàng</div>
    </div>
  </div>
</div>

<!-- ========================================
     CART ITEM TEMPLATE
     Template này sẽ được clone bằng JavaScript
     ======================================== -->
<template id="cart-item-template" style="display: none;">
  <div class="cart-item" data-sku="">
    <div class="item-image">
      <img src="" alt="" loading="lazy">
    </div>
    
    <div class="item-details">
      <h4 class="item-name"></h4>
      <p class="item-variant"></p>
      <p class="item-price"></p>
    </div>
    
    <div class="item-controls">
      <!-- Quantity Controls -->
      <div class="quantity-controls">
        <button class="qty-btn qty-decrease" onclick="LotusCart.updateQuantity(this.closest('.cart-item').dataset.sku, -1)" aria-label="Giảm số lượng">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M19 13H5v-2h14v2z"/>
          </svg>
        </button>
        <input type="number" class="qty-input" min="1" max="999" onchange="LotusCart.setQuantity(this.closest('.cart-item').dataset.sku, this.value)" aria-label="Số lượng">
        <button class="qty-btn qty-increase" onclick="LotusCart.updateQuantity(this.closest('.cart-item').dataset.sku, 1)" aria-label="Tăng số lượng">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        </button>
      </div>
      
      <!-- Item Total -->
      <div class="item-total"></div>
      
      <!-- Remove Button -->
      <button class="remove-item" onclick="LotusCart.removeItem(this.closest('.cart-item').dataset.sku)" aria-label="Xóa sản phẩm">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<!-- ========================================
     PRODUCT CARD ADD TO CART BUTTON
     Thêm vào mỗi product card trong product listing
     ======================================== -->

<!-- Example: Cách thêm Add to Cart button vào product card -->
<!--
<div class="product-card" data-sku="VTC099BNTRON3.8LHT">
  <div class="product-image">
    <img src="product-image.jpg" alt="Product Name">
  </div>
  <div class="product-info">
    <h3 class="product-name">Bình Ngâm Thủy Tinh</h3>
    <p class="product-variant">3.8 lít SVQT</p>
    <p class="product-price">380,000₫</p>
    
    <!-- Add to Cart Button -->
    <button class="add-to-cart-btn" 
            onclick="LotusCart.addToCart(
              'VTC099BNTRON3.8LHT',
              'Bình Ngâm Thủy Tinh', 
              '3.8 lít SVQT',
              380000,
              'product-image.jpg'
            )"
            data-sku="VTC099BNTRON3.8LHT">
      <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
        <path d="M19 7H16V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm4 10a1 1 0 0 1-2 0v-3a1 1 0 0 1 2 0v3z"/>
      </svg>
      Thêm vào giỏ
    </button>
  </div>
</div>
-->

<!-- ========================================
     CHECKOUT MODAL (PHASE 1 EXTENSION)
     Sẽ được implement trong Step 3
     ======================================== -->

<div id="checkout-modal" class="checkout-modal" style="display: none;">
  <!-- Checkout form sẽ được implement sau -->
</div>
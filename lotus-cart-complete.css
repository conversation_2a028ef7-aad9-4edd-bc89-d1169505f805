/* ============================================================================
   LOTUS GLASS - COMPLETE SHOPPING CART STYLES
   File: lotus-cart-complete.css
   
   Thêm vào cuối section <b:skin><![CDATA[ ... ]]></b:skin>
   ============================================================================ */

/* === ROOT VARIABLES === */
:root {
  --cart-primary: #f37021;
  --cart-primary-dark: #d85c15;
  --cart-secondary: #6c757d;
  --cart-success: #28a745;
  --cart-warning: #ffc107;
  --cart-danger: #dc3545;
  --cart-light: #f8f9fa;
  --cart-dark: #343a40;
  --cart-border: #dee2e6;
  --cart-shadow: 0 2px 10px rgba(0,0,0,0.1);
  --cart-shadow-lg: 0 4px 20px rgba(0,0,0,0.15);
  --cart-radius: 8px;
  --cart-transition: all 0.3s ease;
}

/* === CART BADGE === */
.cart-badge {
  position: relative;
  cursor: pointer;
  padding: 8px 12px;
  color: var(--cart-primary);
  transition: var(--cart-transition);
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: var(--cart-radius);
  user-select: none;
}

.cart-badge:hover {
  color: var(--cart-primary-dark);
  background: rgba(243, 112, 33, 0.1);
  transform: translateY(-1px);
}

.cart-badge svg {
  width: 24px;
  height: 24px;
  transition: var(--cart-transition);
}

.cart-badge:hover svg {
  transform: scale(1.1);
}

.cart-count {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--cart-danger);
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 700;
  transform: scale(0);
  transition: var(--cart-transition);
  border: 2px solid white;
  box-shadow: var(--cart-shadow);
}

.cart-count.has-items {
  transform: scale(1);
  animation: cartBounce 0.6s ease;
}

@keyframes cartBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

/* === CART SIDEBAR === */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -450px;
  width: 450px;
  max-width: 100vw;
  height: 100vh;
  background: white;
  box-shadow: var(--cart-shadow-lg);
  z-index: 1000;
  transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.cart-sidebar.active {
  right: 0;
}

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.6);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: var(--cart-transition);
  backdrop-filter: blur(2px);
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

body.cart-open {
  overflow: hidden;
}

/* === CART HEADER === */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--cart-border);
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.cart-header h3 {
  margin: 0;
  color: var(--cart-dark);
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-header h3::before {
  content: '🛒';
  font-size: 18px;
}

.cart-close {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: var(--cart-secondary);
  padding: 8px;
  line-height: 1;
  border-radius: var(--cart-radius);
  transition: var(--cart-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.cart-close:hover {
  color: var(--cart-dark);
  background: var(--cart-light);
  transform: rotate(90deg);
}

/* === CART ITEMS === */
.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: var(--cart-border) transparent;
}

.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: transparent;
}

.cart-items::-webkit-scrollbar-thumb {
  background: var(--cart-border);
  border-radius: 3px;
}

.cart-empty {
  text-align: center;
  padding: 60px 24px;
  color: var(--cart-secondary);
}

.cart-empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  opacity: 0.3;
  fill: currentColor;
}

.cart-empty h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-dark);
}

.cart-empty p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.cart-item {
  display: flex;
  gap: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid var(--cart-light);
  transition: var(--cart-transition);
  position: relative;
}

.cart-item:hover {
  background: rgba(243, 112, 33, 0.02);
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 70px;
  height: 70px;
  flex-shrink: 0;
  border-radius: var(--cart-radius);
  overflow: hidden;
  background: var(--cart-light);
  position: relative;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--cart-transition);
}

.item-image:hover img {
  transform: scale(1.05);
}

.item-image::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: var(--cart-radius);
}

.item-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-name {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.4;
  color: var(--cart-dark);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-variant {
  margin: 0;
  font-size: 13px;
  color: var(--cart-secondary);
  font-weight: 500;
}

.item-price {
  margin: 4px 0 0 0;
  font-size: 15px;
  color: var(--cart-primary);
  font-weight: 600;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
  flex-shrink: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--cart-light);
  border-radius: var(--cart-radius);
  padding: 4px;
  border: 1px solid var(--cart-border);
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-primary);
  transition: var(--cart-transition);
  user-select: none;
}

.qty-btn:hover:not(:disabled) {
  background: var(--cart-primary);
  color: white;
  transform: scale(1.1);
}

.qty-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.qty-input {
  width: 45px;
  height: 32px;
  text-align: center;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-dark);
  outline: none;
}

.item-total {
  font-size: 15px;
  font-weight: 700;
  color: var(--cart-primary);
  text-align: right;
}

.remove-item {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--cart-secondary);
  padding: 6px;
  border-radius: var(--cart-radius);
  transition: var(--cart-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.remove-item:hover {
  color: var(--cart-danger);
  background: rgba(220, 53, 69, 0.1);
  transform: scale(1.1);
}

.remove-item svg {
  width: 16px;
  height: 16px;
}

/* === CART FOOTER === */
.cart-footer {
  border-top: 1px solid var(--cart-border);
  padding: 24px;
  background: white;
  position: sticky;
  bottom: 0;
}

.cart-totals {
  margin-bottom: 20px;
}

.subtotal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-dark);
  margin-bottom: 8px;
}

.subtotal-amount {
  color: var(--cart-primary);
  font-size: 20px;
  font-weight: 700;
}

.shipping-note {
  font-size: 12px;
  color: var(--cart-secondary);
  font-style: italic;
  text-align: center;
  padding: 8px 12px;
  background: rgba(243, 112, 33, 0.05);
  border-radius: var(--cart-radius);
  border: 1px dashed rgba(243, 112, 33, 0.2);
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cart-btn {
  padding: 14px 20px;
  border: none;
  border-radius: var(--cart-radius);
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--cart-transition);
  text-align: center;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.cart-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  transition: all 0.6s ease;
  transform: translate(-50%, -50%);
}

.cart-btn:hover::before {
  width: 300px;
  height: 300px;
}

.cart-btn-primary {
  background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
  color: white;
  box-shadow: var(--cart-shadow);
}

.cart-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--cart-shadow-lg);
}

.cart-btn-primary:disabled {
  background: var(--cart-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cart-btn-secondary {
  background: white;
  color: var(--cart-primary);
  border: 2px solid var(--cart-primary);
}

.cart-btn-secondary:hover {
  background: var(--cart-primary);
  color: white;
  transform: translateY(-1px);
}

/* === ADD TO CART BUTTONS === */
.add-to-cart-btn {
  background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
  color: white;
  border: none;
  padding: 12px 18px;
  border-radius: var(--cart-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--cart-transition);
  margin-top: 12px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.add-to-cart-btn:hover::before {
  left: 100%;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--cart-shadow-lg);
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

.add-to-cart-btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

.add-to-cart-btn.success {
  background: var(--cart-success);
  transform: scale(0.95);
}

/* === CART NOTIFICATION === */
.cart-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
  color: white;
  padding: 16px 24px;
  border-radius: var(--cart-radius);
  box-shadow: var(--cart-shadow-lg);
  z-index: 1001;
  transform: translateX(120%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 350px;
  min-width: 280px;
}

.cart-notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  animation: notificationPulse 0.6s ease;
}

@keyframes notificationPulse {
  0% { transform: scale(0.5) rotate(-45deg); opacity: 0; }
  50% { transform: scale(1.2) rotate(0deg); opacity: 1; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.notification-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

/* === MOBILE RESPONSIVE === */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
    right: -100vw;
  }
  
  .cart-sidebar.active {
    right: 0;
  }
  
  .cart-header {
    padding: 16px 20px;
  }
  
  .cart-header h3 {
    font-size: 18px;
  }
  
  .cart-item {
    padding: 16px 20px;
    gap: 12px;
  }
  
  .item-image {
    width: 60px;
    height: 60px;
  }
  
  .item-name {
    font-size: 14px;
  }
  
  .cart-footer {
    padding: 20px;
  }
  
  .cart-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    transform: translateY(-120%);
  }
  
  .cart-notification.show {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .cart-badge {
    padding: 6px 8px;
  }
  
  .cart-badge svg {
    width: 20px;
    height: 20px;
  }
  
  .cart-count {
    width: 18px;
    height: 18px;
    font-size: 10px;
    top: -1px;
    right: -1px;
  }
  
  .cart-item {
    padding: 12px 16px;
  }
  
  .item-image {
    width: 50px;
    height: 50px;
  }
  
  .cart-header {
    padding: 12px 16px;
  }
  
  .cart-footer {
    padding: 16px;
  }
}

/* === LOADING STATES === */
.cart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--cart-secondary);
}

.cart-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--cart-light);
  border-top-color: var(--cart-primary);
  border-radius: 50%;
  animation: cartSpin 1s linear infinite;
  margin-right: 12px;
}

@keyframes cartSpin {
  to { transform: rotate(360deg); }
}

/* === ANIMATIONS === */
.cart-item-enter {
  animation: cartItemSlideIn 0.3s ease;
}

@keyframes cartItemSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.cart-item-exit {
  animation: cartItemSlideOut 0.3s ease forwards;
}

@keyframes cartItemSlideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

/* === ACCESSIBILITY === */
.cart-badge:focus,
.cart-btn:focus,
.add-to-cart-btn:focus,
.qty-btn:focus,
.cart-close:focus {
  outline: 2px solid var(--cart-primary);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"undefined"},{"function":"__c","vtp_value":"google.com.vn"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":12,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":10},{"function":"__ccd_ga_first","priority":11,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":23},{"function":"__set_product_settings","priority":10,"vtp_instanceDestinationId":"G-9XY5DSYD8S","vtp_foreignTldMacroResult":["macro",2],"vtp_isChinaVipRegionMacroResult":["macro",3],"tag_id":22},{"function":"__ccd_ga_regscope","priority":9,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":21},{"function":"__ccd_em_download","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":20},{"function":"__ccd_em_form","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":19},{"function":"__ccd_em_outbound_click","priority":6,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":18},{"function":"__ccd_em_page_view","priority":5,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":17},{"function":"__ccd_em_scroll","priority":4,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":16},{"function":"__ccd_em_site_search","priority":3,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":15},{"function":"__ccd_em_video","priority":2,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":14},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":13},{"function":"__gct","vtp_trackingId":"G-9XY5DSYD8S","vtp_sessionDuration":0,"tag_id":1},{"function":"__zone","vtp_childContainers":["list",["map","publicId","UA-261015249-1"]],"vtp_enableConfiguration":false,"tag_id":3},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-9XY5DSYD8S","tag_id":12}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",12,13]],[["if",1],["add",0,14,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AJ"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AN"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AO"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AX"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AY"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"q",[46,"w"],[36,[1,[15,"w"],[21,[2,[2,[15,"w"],"toLowerCase",[7]],"match",[7,[15,"p"]]],[45]]]]],[50,"r",[46,"w"],[52,"x",[2,[17,[15,"w"],"pathname"],"split",[7,"."]]],[52,"y",[39,[18,[17,[15,"x"],"length"],1],[16,[15,"x"],[37,[17,[15,"x"],"length"],1]],""]],[36,[16,[2,[15,"y"],"split",[7,"/"]],0]]],[50,"s",[46,"w"],[36,[39,[12,[2,[17,[15,"w"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"w"],"pathname"],[0,"/",[17,[15,"w"],"pathname"]]]]],[50,"t",[46,"w"],[41,"x"],[3,"x",""],[22,[1,[15,"w"],[17,[15,"w"],"href"]],[46,[53,[41,"y"],[3,"y",[2,[17,[15,"w"],"href"],"indexOf",[7,"#"]]],[3,"x",[39,[23,[15,"y"],0],[17,[15,"w"],"href"],[2,[17,[15,"w"],"href"],"substring",[7,0,[15,"y"]]]]]]]],[36,[15,"x"]]],[50,"v",[46,"w"],[52,"x",[8]],[43,[15,"x"],[15,"i"],true],[43,[15,"x"],[15,"e"],true],[43,[15,"w"],"eventMetadata",[15,"x"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"u",["l",[8,"checkValidation",true]]],[22,[28,[15,"u"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"w","x"],["x"],[52,"y",[8,"eventId",[16,[15,"w"],"gtm.uniqueEventId"],"deferrable",true]],[52,"z",[16,[15,"w"],"gtm.elementUrl"]],[52,"aA",["n",[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[52,"aB",["r",[15,"aA"]]],[22,[28,["q",[15,"aB"]]],[46,[53,[36]]]],[52,"aC",[8,"link_id",[16,[15,"w"],"gtm.elementId"],"link_url",["t",[15,"aA"]],"link_text",[16,[15,"w"],"gtm.elementText"],"file_name",["s",[15,"aA"]],"file_extension",[15,"aB"]]],["v",[15,"y"]],["o",["m"],[15,"g"],[15,"aC"],[15,"y"]]],[15,"u"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"aA"],[52,"aB",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aB"],"event_usage",[7,8]],[43,[15,"aA"],[15,"m"],[15,"aB"]]],[50,"u",[46,"aA","aB"],[52,"aC",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aC"],[15,"l"],true],[43,[15,"aC"],[15,"g"],true],[22,[16,[15,"aB"],"gtm.formCanceled"],[46,[53,[43,[15,"aC"],[15,"n"],true]]]],[43,[15,"aA"],[15,"m"],[15,"aC"]]],[50,"v",[46,"aA","aB","aC"],[52,"aD",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[20,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aD"],"length"],0],[46,[53,["s",[15,"aD"],[15,"aA"],[15,"aB"],[15,"aC"]]]]],[52,"aE",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[21,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aE"],"length"],0],[46,[53,[43,[15,"aC"],"deferrable",true],["s",[15,"aE"],[15,"aA"],[15,"aB"],[15,"aC"]]]]]],[52,"b",["require","internal.isFeatureEnabled"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmFormActivity"]],[52,"g","speculative"],[52,"h","ae_block_form"],[52,"i","form_submit"],[52,"j","form_start"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m","eventMetadata"],[52,"n","form_event_canceled"],[52,"o",[17,[15,"a"],"instanceDestinationId"]],[22,["d",[15,"o"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"e"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"k"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"aA","aB"],[22,[15,"aB"],[46,["aB"]]],[52,"aC",[16,[15,"aA"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"aC"]],[46,[36]]],[43,[15,"w"],[15,"aC"],true],[52,"aD",[8,"form_id",[15,"aC"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aA"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aA"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aA"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aA"],"gtm.interactedFormFieldPosition"]]],[52,"aE",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aE"]],["u",[15,"aE"],[15,"aA"]],["v",[15,"j"],[15,"aD"],[15,"aE"]]]],[52,"y",["b",[17,[15,"c"],"DH"]]],[52,"z",[51,"",[7,"aA","aB"],["x",[15,"aA"],[44]],[52,"aC",[8,"form_id",[16,[15,"aA"],"gtm.elementId"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"aA"],"gtm.formSubmitElementText"],[16,[15,"aA"],"gtm.formSubmitButtonText"]]]],[43,[15,"aC"],"event_callback",[15,"aB"]],[52,"aD",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aD"]],["u",[15,"aD"],[15,"aA"]],["v",[15,"i"],[15,"aC"],[15,"aD"]]]],[22,[15,"y"],[46,[53,[52,"aA",["require","internal.addDataLayerEventListener"]],[52,"aB",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aC",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aD",["aC"]],[22,[28,[15,"aD"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formInteract",[15,"x"],[15,"aD"]],[52,"aE",["aB",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aE"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formSubmit",[15,"z"],[15,"aE"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"r",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"s",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",[17,[15,"x"],"hostname"]],[52,"z",[2,[15,"y"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"z"],[16,[15,"z"],0]],[46,[3,"y",[2,[15,"y"],"substring",[7,[17,[16,[15,"z"],0],"length"]]]]]],[36,[15,"y"]]],[50,"t",[46,"x"],[22,[28,[15,"x"]],[46,[36,false]]],[52,"y",[2,[17,[15,"x"],"hostname"],"toLowerCase",[7]]],[22,[28,[15,"y"]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[2,["s",["p",["o"]]],"toLowerCase",[7]]],[41,"aA"],[3,"aA",[37,[17,[15,"y"],"length"],[17,[15,"z"],"length"]]],[22,[1,[18,[15,"aA"],0],[29,[2,[15,"z"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aA"],[3,"aA",[37,[15,"aA"],1]]],[3,"z",[0,".",[15,"z"]]]]]],[22,[1,[19,[15,"aA"],0],[12,[2,[15,"y"],"indexOf",[7,[15,"z"],[15,"aA"]]],[15,"aA"]]],[46,[53,[36,false]]]],[36,true]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"i"],true],[43,[15,"y"],[15,"e"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmOutboundClickActivity"]],[52,"e","speculative"],[52,"f","ae_block_outbound_click"],[52,"g","click"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.getRemoteConfigParameter"]],[52,"o",["require","getUrl"]],[52,"p",["require","parseUrl"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"u",["n",[15,"j"],"cross_domain_conditions"]],[52,"v",["l",[8,"affiliateDomains",[15,"u"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"x","y"],[52,"z",["p",[16,[15,"x"],"gtm.elementUrl"]]],[22,[28,["t",[15,"z"]]],[46,[53,["y"],[36]]]],[52,"aA",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_classes",[16,[15,"x"],"gtm.elementClasses"],"link_url",["r",[15,"z"]],"link_domain",["s",[15,"z"]],"outbound",true]],[43,[15,"aA"],"event_callback",[15,"y"]],[52,"aB",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"],"deferrable",true]],["w",[15,"aB"]],["q",["m"],[15,"g"],[15,"aA"],[15,"aB"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"P"],true],[43,[15,"s"],[17,[15,"f"],"BV"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"w"],"deferrable",true]]]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"p",[46,"q"],[52,"r",[8]],[43,[15,"r"],[15,"i"],true],[43,[15,"r"],[15,"e"],true],[43,[15,"q"],"eventMetadata",[15,"r"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"o"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"q","r"],["r"],[52,"s",[8,"eventId",[16,[15,"q"],"gtm.uniqueEventId"],"deferrable",true]],[52,"t",[8,"percent_scrolled",[16,[15,"q"],"gtm.scrollThreshold"]]],["p",[15,"s"]],["n",["m"],[15,"g"],[15,"t"],[15,"s"]]],[15,"o"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[15,"k"],true],[43,[15,"t"],[15,"e"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",["require","internal.getDestinationIds"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.videoStatus"]],[41,"v"],[22,[20,[15,"u"],"start"],[46,[53,[3,"v",[15,"g"]]]],[46,[22,[20,[15,"u"],"progress"],[46,[53,[3,"v",[15,"h"]]]],[46,[22,[20,[15,"u"],"complete"],[46,[53,[3,"v",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"w",[8,"video_current_time",[16,[15,"s"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"s"],"gtm.videoDuration"],"video_percent",[16,[15,"s"],"gtm.videoPercent"],"video_provider",[16,[15,"s"],"gtm.videoProvider"],"video_title",[16,[15,"s"],"gtm.videoTitle"],"video_url",[16,[15,"s"],"gtm.videoUrl"],"visible",[16,[15,"s"],"gtm.videoVisible"]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"],"deferrable",true]],["r",[15,"x"]],["p",["o"],[15,"v"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DW"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"X"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"CC"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CK"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"Y"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CC"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CD"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","allow_interest_groups"],[52,"z","auid"],[52,"aA","aw_remarketing"],[52,"aB","aw_remarketing_only"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_cookie_prefix"],[52,"aK","conversion_id"],[52,"aL","conversion_linker"],[52,"aM","conversion_api"],[52,"aN","cookie_deprecation"],[52,"aO","cookie_expires"],[52,"aP","cookie_prefix"],[52,"aQ","cookie_update"],[52,"aR","country"],[52,"aS","currency"],[52,"aT","customer_buyer_stage"],[52,"aU","customer_lifetime_value"],[52,"aV","customer_loyalty"],[52,"aW","customer_ltv_bucket"],[52,"aX","debug_mode"],[52,"aY","developer_id"],[52,"aZ","shipping"],[52,"bA","engagement_time_msec"],[52,"bB","estimated_delivery_date"],[52,"bC","event_developer_id_string"],[52,"bD","event"],[52,"bE","event_timeout"],[52,"bF","first_party_collection"],[52,"bG","match_id"],[52,"bH","gdpr_applies"],[52,"bI","google_analysis_params"],[52,"bJ","_google_ng"],[52,"bK","gpp_sid"],[52,"bL","gpp_string"],[52,"bM","gsa_experiment_id"],[52,"bN","gtag_event_feature_usage"],[52,"bO","iframe_state"],[52,"bP","ignore_referrer"],[52,"bQ","is_passthrough"],[52,"bR","_lps"],[52,"bS","language"],[52,"bT","merchant_feed_label"],[52,"bU","merchant_feed_language"],[52,"bV","merchant_id"],[52,"bW","new_customer"],[52,"bX","page_hostname"],[52,"bY","page_path"],[52,"bZ","page_referrer"],[52,"cA","page_title"],[52,"cB","_platinum_request_status"],[52,"cC","quantity"],[52,"cD","restricted_data_processing"],[52,"cE","screen_resolution"],[52,"cF","send_page_view"],[52,"cG","server_container_url"],[52,"cH","session_duration"],[52,"cI","session_engaged_time"],[52,"cJ","session_id"],[52,"cK","_shared_user_id"],[52,"cL","delivery_postal_code"],[52,"cM","topmost_url"],[52,"cN","transaction_id"],[52,"cO","transport_url"],[52,"cP","update"],[52,"cQ","_user_agent_architecture"],[52,"cR","_user_agent_bitness"],[52,"cS","_user_agent_full_version_list"],[52,"cT","_user_agent_mobile"],[52,"cU","_user_agent_model"],[52,"cV","_user_agent_platform"],[52,"cW","_user_agent_platform_version"],[52,"cX","_user_agent_wow64"],[52,"cY","user_data"],[52,"cZ","user_data_auto_latency"],[52,"dA","user_data_auto_meta"],[52,"dB","user_data_auto_multi"],[52,"dC","user_data_auto_selectors"],[52,"dD","user_data_auto_status"],[52,"dE","user_data_mode"],[52,"dF","user_id"],[52,"dG","user_properties"],[52,"dH","us_privacy_string"],[52,"dI","value"],[52,"dJ","_fpm_parameters"],[52,"dK","_host_name"],[52,"dL","_in_page_command"],[52,"dM","non_personalized_ads"],[52,"dN","conversion_label"],[52,"dO","page_location"],[52,"dP","global_developer_id_string"],[52,"dQ","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BK",[15,"y"],"BP",[15,"z"],"BR",[15,"aA"],"BS",[15,"aB"],"BT",[15,"aC"],"BU",[15,"aD"],"BV",[15,"aE"],"BW",[15,"aF"],"BX",[15,"aG"],"BY",[15,"aH"],"CG",[15,"aI"],"CL",[15,"aJ"],"CM",[15,"aK"],"JS",[15,"dN"],"CN",[15,"aL"],"CP",[15,"aM"],"CQ",[15,"aN"],"CS",[15,"aO"],"CW",[15,"aP"],"CX",[15,"aQ"],"CY",[15,"aR"],"CZ",[15,"aS"],"DA",[15,"aT"],"DB",[15,"aU"],"DC",[15,"aV"],"DD",[15,"aW"],"DH",[15,"aX"],"DI",[15,"aY"],"DU",[15,"aZ"],"DW",[15,"bA"],"EA",[15,"bB"],"EE",[15,"bC"],"EG",[15,"bD"],"EI",[15,"bE"],"EN",[15,"bF"],"EY",[15,"bG"],"FI",[15,"bH"],"JU",[15,"dP"],"FM",[15,"bI"],"FN",[15,"bJ"],"FQ",[15,"bK"],"FR",[15,"bL"],"FT",[15,"bM"],"FU",[15,"bN"],"FW",[15,"bO"],"FX",[15,"bP"],"GC",[15,"bQ"],"GD",[15,"bR"],"GE",[15,"bS"],"GL",[15,"bT"],"GM",[15,"bU"],"GN",[15,"bV"],"GR",[15,"bW"],"GU",[15,"bX"],"JT",[15,"dO"],"GV",[15,"bY"],"GW",[15,"bZ"],"GX",[15,"cA"],"HF",[15,"cB"],"HH",[15,"cC"],"HL",[15,"cD"],"HP",[15,"cE"],"HS",[15,"cF"],"HU",[15,"cG"],"HV",[15,"cH"],"HX",[15,"cI"],"HY",[15,"cJ"],"IA",[15,"cK"],"IB",[15,"cL"],"JV",[15,"dQ"],"IG",[15,"cM"],"IJ",[15,"cN"],"IK",[15,"cO"],"IM",[15,"cP"],"IP",[15,"cQ"],"IQ",[15,"cR"],"IR",[15,"cS"],"IS",[15,"cT"],"IT",[15,"cU"],"IU",[15,"cV"],"IV",[15,"cW"],"IW",[15,"cX"],"IX",[15,"cY"],"IY",[15,"cZ"],"IZ",[15,"dA"],"JA",[15,"dB"],"JB",[15,"dC"],"JC",[15,"dD"],"JD",[15,"dE"],"JF",[15,"dF"],"JG",[15,"dG"],"JI",[15,"dH"],"JJ",[15,"dI"],"JL",[15,"dJ"],"JM",[15,"dK"],"JN",[15,"dL"],"JQ",[15,"dM"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",42],[52,"h",43],[52,"i",44],[52,"j",45],[52,"k",46],[52,"l",47],[52,"m",56],[52,"n",68],[52,"o",113],[52,"p",129],[52,"q",142],[52,"r",156],[52,"s",168],[52,"t",174],[52,"u",178],[52,"v",188],[52,"w",212],[52,"x",226],[36,[8,"DP",[15,"s"],"W",[15,"b"],"X",[15,"c"],"Y",[15,"d"],"Z",[15,"e"],"AF",[15,"f"],"AH",[15,"g"],"AI",[15,"h"],"AJ",[15,"i"],"AK",[15,"j"],"AL",[15,"k"],"AM",[15,"l"],"EA",[15,"v"],"AR",[15,"m"],"DT",[15,"t"],"DW",[15,"u"],"AW",[15,"n"],"BX",[15,"o"],"CK",[15,"p"],"CX",[15,"q"],"EP",[15,"w"],"DH",[15,"r"],"EZ",[15,"x"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"H",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"P",[15,"h"],"U",[15,"i"],"V",[15,"j"],"AD",[15,"k"],"AF",[15,"l"],"AG",[15,"m"],"AJ",[15,"n"],"AL",[15,"o"],"AN",[15,"p"],"AO",[15,"q"],"AQ",[15,"r"],"AR",[15,"s"],"AS",[15,"t"],"AT",[15,"u"],"AW",[15,"v"],"AX",[15,"w"],"AY",[15,"x"],"AZ",[15,"y"],"BA",[15,"z"],"BC",[15,"aA"],"BD",[15,"aB"],"BI",[15,"aC"],"BL",[15,"aD"],"BM",[15,"aE"],"BO",[15,"aF"],"BT",[15,"aG"],"BV",[15,"aH"],"BY",[15,"aI"],"BZ",[15,"aJ"],"CA",[15,"aK"],"CB",[15,"aL"],"CC",[15,"aM"],"CD",[15,"aN"],"CE",[15,"aO"],"CF",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"BA"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"GW"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"GW"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"BV"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"AH"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"m","n","o"],[50,"t",[46,"v"],[52,"w",[16,[15,"i"],[15,"v"]]],[22,[28,[15,"w"]],[46,[36]]],[53,[41,"x"],[3,"x",0],[63,[7,"x"],[23,[15,"x"],[17,[15,"w"],"length"]],[33,[15,"x"],[3,"x",[0,[15,"x"],1]]],[46,[53,[52,"y",[16,[15,"w"],[15,"x"]]],["q",[15,"p"],[17,[15,"y"],"name"],[17,[15,"y"],"value"]]]]]]],[50,"u",[46,"v"],[22,[30,[28,[15,"r"]],[21,[17,[15,"r"],"length"],2]],[46,[53,[36,false]]]],[41,"w"],[3,"w",[16,[15,"v"],[15,"s"]]],[22,[20,[15,"w"],[44]],[46,[53,[3,"w",[16,[15,"v"],[15,"r"]]]]]],[36,[28,[28,[15,"w"]]]]],[22,[28,[15,"n"]],[46,[36]]],[52,"p",[30,[17,[15,"m"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"q",["h",[15,"f"],[15,"o"]]],[52,"r",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"o"]]],["$0"]]],[52,"s",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"o"]]],["$0"]]],[53,[41,"v"],[3,"v",0],[63,[7,"v"],[23,[15,"v"],[17,[15,"n"],"length"]],[33,[15,"v"],[3,"v",[0,[15,"v"],1]]],[46,[53,[52,"w",[16,[15,"n"],[15,"v"]]],[22,[30,[17,[15,"w"],"disallowAllRegions"],["u",[17,[15,"w"],"disallowedRegions"]]],[46,[53,["t",[17,[15,"w"],"redactFieldGroup"]]]]]]]]]],[50,"k",[46,"m"],[52,"n",[8]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[53,[6]]]],[43,[15,"n"],[15,"q"],true]]]]],[36,[15,"n"]]],[50,"l",[46,"m"],[22,[28,[17,[15,"m"],"settingsTable"]],[46,[36,[7]]]],[52,"n",[8]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[17,[15,"m"],"settingsTable"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[17,[15,"m"],"settingsTable"],[15,"o"]]],[52,"q",[17,[15,"p"],"redactFieldGroup"]],[22,[28,[16,[15,"i"],[15,"q"]]],[46,[6]]],[43,[15,"n"],[15,"q"],[8,"redactFieldGroup",[15,"q"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"r",[16,[15,"n"],[15,"q"]]],[22,[17,[15,"p"],"disallowAllRegions"],[46,[53,[43,[15,"r"],"disallowAllRegions",true],[6]]]],[43,[15,"r"],"disallowedRegions",["k",[17,[15,"p"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"n"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[52,"i",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"j"],"B",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_download":{"2":true,"5":true}
,
"__ccd_em_form":{"2":true,"5":true}
,
"__ccd_em_outbound_click":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_em_scroll":{"2":true,"5":true}
,
"__ccd_em_site_search":{"2":true,"5":true}
,
"__ccd_em_video":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"1","10":"G-9XY5DSYD8S|GT-K825BWS","11":true,"14":"57s0","15":"2","16":"ChEI8OKhxAYQ47yi8OGBmrn8ARIlAKjB7+6ORaQaidKUGxpSDty6l507k5Sny09Kt9G+q1KuHYC57hoCKwE=","17":"","19":"dataLayer","2":true,"20":"","21":"www.googletagmanager.com","22":"eyIwIjoiVk4iLCIxIjoiVk4tU0ciLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udm4iLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"VN","31":"VN-SG","32":true,"34":"G-9XY5DSYD8S","35":"G","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BLmFt6UXBhRmCroatpW1SXiUGX8nlIzsjWuo/35QAO+zaS+otiG5QcR9nM1Cps71ya2tmVIsN5veaAal7MHFLEs=\",\"version\":0},\"id\":\"aec78412-6373-47d5-ac96-9fcee93fb999\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BC/FqS2VfJxJt+KUoa5szFzBglEsbyx+I9x123cX99SEO7P1N7hO6AIp93nTAdi/z2DFSAto+EqKKdcuaTb9W0s=\",\"version\":0},\"id\":\"a8322124-3ea2-4d88-b25b-86e2f0112cae\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BKfFh+mfP+VYN5VmB9shcyG0A1lRYz8Xzw3WGLlsKlBmFEaKsavgS+aJLQV57OOtxcD75yF5XPI4JCpAEVT6aZE=\",\"version\":0},\"id\":\"69d58b45-d2bb-4a7f-9952-57e6e8373ee3\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BGKg2rrDYEGZYBnoJcCvLOBw40XwX02uo+UmyosodkDpDhfJRS/gnmzpZxgdB0K64JD4BNvJP8lOXmDgfjDJnr0=\",\"version\":0},\"id\":\"1cfcadd3-649d-4616-a730-b7cbb203d3b2\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BIj0YjU0Id8OOxdy8oAkpsYU3WUMzeTX3IB3zolk/AGHi8e4L1Wndgs+eEljcMtqAzqNrV2PUboMi62U86LWEtA=\",\"version\":0},\"id\":\"12ffea68-4f40-48ea-9714-010853b2215c\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","5":"G-9XY5DSYD8S","6":"104972449","8":"res_ts:1727385715115522,srv_cl:787922924,ds:live,cv:1","9":"G-9XY5DSYD8S"}
,"permissions":{
"__c":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},da=ca(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ia={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ia?g=ia:g=da;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=fa&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ia,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=fa?da.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(fa&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var qa;a:{var ra={a:!0},ta={};try{ta.__proto__=ra;qa=ta.a;break a}catch(a){}qa=!1}pa=qa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ua=pa,va=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(ua)ua(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.yq=b.prototype},l=function(a){var b=typeof ia.Symbol!="undefined"&&ia.Symbol.iterator&&a[ia.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},xa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ya=function(a){return a instanceof Array?a:xa(l(a))},Aa=function(a){return za(a,a)},za=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ba=fa&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ba},"es6");
var Ca=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Da=this||self,Ea=function(a,b){function c(){}c.prototype=b.prototype;a.yq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.yr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Fa=function(a,b){this.type=a;this.data=b};var Ga=function(){this.map={};this.C={}};Ga.prototype.get=function(a){return this.map["dust."+a]};Ga.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ga.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ga.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ha=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ga.prototype.ya=function(){return Ha(this,1)};Ga.prototype.yc=function(){return Ha(this,2)};Ga.prototype.ac=function(){return Ha(this,3)};var Ia=function(){};Ia.prototype.reset=function(){};var Ja=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.Cb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ga};Ja.prototype.add=function(a,b){Ka(this,a,b,!1)};Ja.prototype.oh=function(a,b){Ka(this,a,b,!0)};var Ka=function(a,b,c,d){if(!a.Cb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ja.prototype;k.set=function(a,b){this.Cb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ja(this.P,this);this.C&&a.Mb(this.C);a.Tc(this.H);a.Pd(this.N);return a};k.Hd=function(){return this.P};k.Mb=function(a){this.C=a};k.Wl=function(){return this.C};k.Tc=function(a){this.H=a};k.Yi=function(){return this.H};k.Ta=function(){this.Cb=!0};k.Pd=function(a){this.N=a};k.sb=function(){return this.N};var La=function(){this.value={};this.prefix="gtm."};La.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};La.prototype.get=function(a){return this.value[this.prefix+String(a)]};La.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Ma(){try{return Map?new Map:new La}catch(a){return new La}};var Na=function(){this.values=[]};Na.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Na.prototype.has=function(a){return this.values.indexOf(a)>-1};var Oa=function(a,b){this.ia=a;this.parent=b;this.P=this.H=void 0;this.Cb=!1;this.N=function(d,e,f){return d.apply(e,f)};this.C=Ma();var c;try{c=Set?new Set:new Na}catch(d){c=new Na}this.R=c};Oa.prototype.add=function(a,b){Pa(this,a,b,!1)};Oa.prototype.oh=function(a,b){Pa(this,a,b,!0)};var Pa=function(a,b,c,d){a.Cb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=Oa.prototype;
k.set=function(a,b){this.Cb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Oa(this.ia,this);this.H&&a.Mb(this.H);a.Tc(this.N);a.Pd(this.P);return a};k.Hd=function(){return this.ia};k.Mb=function(a){this.H=a};k.Wl=function(){return this.H};
k.Tc=function(a){this.N=a};k.Yi=function(){return this.N};k.Ta=function(){this.Cb=!0};k.Pd=function(a){this.P=a};k.sb=function(){return this.P};var Qa=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.im=a;this.Ol=c===void 0?!1:c;this.debugInfo=[];this.C=b};va(Qa,Error);var Ra=function(a){return a instanceof Qa?a:new Qa(a,void 0,!0)};var Sa=[],Ta={};function Ua(a){return Sa[a]===void 0?!1:Sa[a]};var Xa=Ma();function Ya(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Za(a,e.value),c instanceof Fa);e=d.next());return c}
function Za(a,b){try{if(Ua(16)){var c=b[0],d=b.slice(1),e=String(c),f=Xa.has(e)?Xa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ra(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=xa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ra(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(ya(m)))}catch(q){var p=a.Wl();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var $a=function(){this.H=new Ia;this.C=Ua(16)?new Oa(this.H):new Ja(this.H)};k=$a.prototype;k.Hd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Tc=function(a){this.C.Tc(a)};k.execute=function(a){return this.zj([a].concat(ya(Ca.apply(1,arguments))))};k.zj=function(){for(var a,b=l(Ca.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Za(this.C,c.value);return a};
k.Zn=function(a){var b=Ca.apply(1,arguments),c=this.C.rb();c.Pd(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Za(c,f.value);return d};k.Ta=function(){this.C.Ta()};var ab=function(){this.Fa=!1;this.aa=new Ga};k=ab.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Fa||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Fa||this.aa.remove(a)};k.ya=function(){return this.aa.ya()};k.yc=function(){return this.aa.yc()};k.ac=function(){return this.aa.ac()};k.Ta=function(){this.Fa=!0};k.Cb=function(){return this.Fa};function bb(){for(var a=cb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function db(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var cb,eb;function fb(a){cb=cb||db();eb=eb||bb();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(cb[m],cb[n],cb[p],cb[q])}return b.join("")}
function gb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=eb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}cb=cb||db();eb=eb||bb();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var hb={};function ib(a,b){hb[a]=hb[a]||[];hb[a][b]=!0}function jb(){hb.GTAG_EVENT_FEATURE_CHANNEL=kb}function lb(a){var b=hb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return fb(c.join("")).replace(/\.+$/,"")}function mb(){for(var a=[],b=hb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function nb(){}function ob(a){return typeof a==="function"}function pb(a){return typeof a==="string"}function qb(a){return typeof a==="number"&&!isNaN(a)}function rb(a){return Array.isArray(a)?a:[a]}function sb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function tb(a,b){if(!qb(a)||!qb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function ub(a,b){for(var c=new vb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function wb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function xb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function yb(a){return Math.round(Number(a))||0}function zb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Ab(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Bb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Cb(){return new Date(Date.now())}function Db(){return Cb().getTime()}var vb=function(){this.prefix="gtm.";this.values={}};vb.prototype.set=function(a,b){this.values[this.prefix+a]=b};vb.prototype.get=function(a){return this.values[this.prefix+a]};vb.prototype.contains=function(a){return this.get(a)!==void 0};
function Fb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Gb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Hb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Ib(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Jb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Kb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Lb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Mb=/^\w{1,9}$/;function Nb(a,b){a=a||{};b=b||",";var c=[];wb(a,function(d,e){Mb.test(d)&&e&&c.push(d)});return c.join(b)}function Ob(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Pb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Qb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Rb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Sb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ya(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Tb=globalThis.trustedTypes,Ub;function Wb(){var a=null;if(!Tb)return a;try{var b=function(c){return c};a=Tb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Xb(){Ub===void 0&&(Ub=Wb());return Ub};var Yb=function(a){this.C=a};Yb.prototype.toString=function(){return this.C+""};function Zb(a){var b=a,c=Xb(),d=c?c.createScriptURL(b):b;return new Yb(d)}function $b(a){if(a instanceof Yb)return a.C;throw Error("");};var ac=Aa([""]),bc=za(["\x00"],["\\0"]),cc=za(["\n"],["\\n"]),ec=za(["\x00"],["\\u0000"]);function fc(a){return a.toString().indexOf("`")===-1}fc(function(a){return a(ac)})||fc(function(a){return a(bc)})||fc(function(a){return a(cc)})||fc(function(a){return a(ec)});var hc=function(a){this.C=a};hc.prototype.toString=function(){return this.C};var ic=function(a){this.Np=a};function jc(a){return new ic(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var kc=[jc("data"),jc("http"),jc("https"),jc("mailto"),jc("ftp"),new ic(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function lc(a){var b;b=b===void 0?kc:b;if(a instanceof hc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ic&&d.Np(a))return new hc(a)}}var mc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function nc(a){var b;if(a instanceof hc)if(a instanceof hc)b=a.C;else throw Error("");else b=mc.test(a)?a:void 0;return b};function oc(a,b){var c=nc(b);c!==void 0&&(a.action=c)};function pc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var qc=function(a){this.C=a};qc.prototype.toString=function(){return this.C+""};var sc=function(){this.C=rc[0].toLowerCase()};sc.prototype.toString=function(){return this.C};function tc(a,b){var c=[new sc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof sc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var uc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function vc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,wc=window.history,A=document,xc=navigator;function yc(){var a;try{a=xc.serviceWorker}catch(b){return}return a}var zc=A.currentScript,Ac=zc&&zc.src;function Bc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Cc(a){return(xc.userAgent||"").indexOf(a)!==-1}function Dc(){return Cc("Firefox")||Cc("FxiOS")}function Ec(){return(Cc("GSA")||Cc("GoogleApp"))&&(Cc("iPhone")||Cc("iPad"))}function Fc(){return Cc("Edg/")||Cc("EdgA/")||Cc("EdgiOS/")}
var Gc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Hc={height:1,onload:1,src:1,style:1,width:1};function Ic(a,b,c){b&&wb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Jc(a,b,c,d,e){var f=A.createElement("script");Ic(f,d,Gc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Zb(vc(a));f.src=$b(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Kc(){if(Ac){var a=Ac.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Lc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Ic(g,c,Hc);d&&wb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Mc(a,b,c,d){return Nc(a,b,c,d)}function Oc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Pc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Qc(a){x.setTimeout(a,0)}function Rc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Sc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Tc(a){var b=A.createElement("div"),c=b,d,e=vc("A<div>"+a+"</div>"),f=Xb(),g=f?f.createHTML(e):e;d=new qc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof qc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Uc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Vc(a,b,c){var d;try{d=xc.sendBeacon&&xc.sendBeacon(a)}catch(e){ib("TAGGING",15)}d?b==null||b():Nc(a,b,c)}function Wc(a,b){try{return xc.sendBeacon(a,b)}catch(c){ib("TAGGING",15)}return!1}var Xc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Yc(a,b,c,d,e){if(Zc()){var f=ma(Object,"assign").call(Object,{},Xc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Eh)return e==null||e(),
!1;if(b){var h=Wc(a,b);h?d==null||d():e==null||e();return h}$c(a,d,e);return!0}function Zc(){return typeof x.fetch==="function"}function ad(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function bd(){var a=x.performance;if(a&&ob(a.now))return a.now()}
function cd(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function dd(){return x.performance||void 0}function ed(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Nc=function(a,b,c,d){var e=new Image(1,1);Ic(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},$c=Vc;function fd(a,b){return this.evaluate(a)&&this.evaluate(b)}function gd(a,b){return this.evaluate(a)===this.evaluate(b)}function hd(a,b){return this.evaluate(a)||this.evaluate(b)}function id(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function jd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function kd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof ab&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var ld=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,md=function(a){if(a==null)return String(a);var b=ld.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},nd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},od=function(a){if(!a||md(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!nd(a,"constructor")&&!nd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
nd(a,b)},pd=function(a,b){var c=b||(md(a)=="array"?[]:{}),d;for(d in a)if(nd(a,d)){var e=a[d];md(e)=="array"?(md(c[d])!="array"&&(c[d]=[]),c[d]=pd(e,c[d])):od(e)?(od(c[d])||(c[d]={}),c[d]=pd(e,c[d])):c[d]=e}return c};function qd(a){if(a==void 0||Array.isArray(a)||od(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function rd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var sd=function(a){a=a===void 0?[]:a;this.aa=new Ga;this.values=[];this.Fa=!1;for(var b in a)a.hasOwnProperty(b)&&(rd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=sd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof sd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Fa)if(a==="length"){if(!rd(b))throw Ra(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else rd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():rd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.ya=function(){for(var a=this.aa.ya(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.yc=function(){for(var a=this.aa.yc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){rd(a)?delete this.values[Number(a)]:this.Fa||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ya(Ca.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Ca.apply(2,arguments);return b===void 0&&c.length===0?new sd(this.values.splice(a)):new sd(this.values.splice.apply(this.values,[a,b||0].concat(ya(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ya(Ca.apply(0,arguments)))};k.has=function(a){return rd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ta=function(){this.Fa=!0;Object.freeze(this.values)};k.Cb=function(){return this.Fa};
function td(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var ud=function(a,b){this.functionName=a;this.Fd=b;this.aa=new Ga;this.Fa=!1};k=ud.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new sd(this.ya())};k.invoke=function(a){return this.Fd.call.apply(this.Fd,[new vd(this,a)].concat(ya(Ca.apply(1,arguments))))};k.apply=function(a,b){return this.Fd.apply(new vd(this,a),b)};k.Kb=function(a){var b=Ca.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ya(b)))}catch(c){}};
k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Fa||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Fa||this.aa.remove(a)};k.ya=function(){return this.aa.ya()};k.yc=function(){return this.aa.yc()};k.ac=function(){return this.aa.ac()};k.Ta=function(){this.Fa=!0};k.Cb=function(){return this.Fa};var wd=function(a,b){ud.call(this,a,b)};va(wd,ud);var xd=function(a,b){ud.call(this,a,b)};va(xd,ud);var vd=function(a,b){this.Fd=a;this.K=b};
vd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Za(b,a):a};vd.prototype.getName=function(){return this.Fd.getName()};vd.prototype.Hd=function(){return this.K.Hd()};var yd=function(){this.map=new Map};yd.prototype.set=function(a,b){this.map.set(a,b)};yd.prototype.get=function(a){return this.map.get(a)};var zd=function(){this.keys=[];this.values=[]};zd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};zd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Ad(){try{return Map?new yd:new zd}catch(a){return new zd}};var Bd=function(a){if(a instanceof Bd)return a;if(qd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Bd.prototype.getValue=function(){return this.value};Bd.prototype.toString=function(){return String(this.value)};var Dd=function(a){this.promise=a;this.Fa=!1;this.aa=new Ga;this.aa.set("then",Cd(this));this.aa.set("catch",Cd(this,!0));this.aa.set("finally",Cd(this,!1,!0))};k=Dd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Fa||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Fa||this.aa.remove(a)};k.ya=function(){return this.aa.ya()};k.yc=function(){return this.aa.yc()};k.ac=function(){return this.aa.ac()};
var Cd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new wd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof wd||(d=void 0);e instanceof wd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Bd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Dd(h)})};Dd.prototype.Ta=function(){this.Fa=!0};Dd.prototype.Cb=function(){return this.Fa};function C(a,b,c){var d=Ad(),e=function(g,h){for(var m=g.ya(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof sd){var m=[];d.set(g,m);for(var n=g.ya(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Dd)return g.promise.then(function(u){return C(u,b,1)},function(u){return Promise.reject(C(u,b,1))});if(g instanceof ab){var q={};d.set(g,q);e(g,q);return q}if(g instanceof wd){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Ed(arguments[v],b,c);var w=new Ja(b?b.Hd():new Ia);b&&w.Pd(b.sb());return f(Ua(16)?g.apply(w,u):g.invoke.apply(g,[w].concat(ya(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof Bd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Ed(a,b,c){var d=Ad(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||xb(g)){var m=new sd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(od(g)){var p=new ab;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new wd("",function(){for(var u=Ca.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=C(this.evaluate(u[w]),b,c);return f(this.K.Yi()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new Bd(g)};return f(a)};var Fd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof sd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new sd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new sd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new sd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ya(Ca.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ra(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ra(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ra(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ra(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=td(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new sd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=td(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ya(Ca.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ya(Ca.apply(1,arguments)))}};var Gd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Hd=new Fa("break"),Id=new Fa("continue");function Jd(a,b){return this.evaluate(a)+this.evaluate(b)}function Kd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Ld(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof sd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ra(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=C(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ra(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Gd.hasOwnProperty(e)){var m=2;m=1;var n=C(f,void 0,m);return Ed(d[e].apply(d,n),this.K)}throw Ra(Error("TypeError: "+e+" is not a function"));}if(d instanceof sd){if(d.has(e)){var p=d.get(String(e));if(p instanceof wd){var q=td(f);return Ua(16)?p.apply(this.K,q):p.invoke.apply(p,[this.K].concat(ya(q)))}throw Ra(Error("TypeError: "+e+" is not a function"));
}if(Fd.supportedMethods.indexOf(e)>=0){var r=td(f);return Fd[e].call.apply(Fd[e],[d,this.K].concat(ya(r)))}}if(d instanceof wd||d instanceof ab||d instanceof Dd){if(d.has(e)){var t=d.get(e);if(t instanceof wd){var u=td(f);return Ua(16)?t.apply(this.K,u):t.invoke.apply(t,[this.K].concat(ya(u)))}throw Ra(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof wd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Bd&&e==="toString")return d.toString();
throw Ra(Error("TypeError: Object has no '"+e+"' property."));}function Md(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Nd(){var a=Ca.apply(0,arguments),b=this.K.rb(),c=Ya(b,a);if(c instanceof Fa)return c}function Od(){return Hd}
function Pd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Fa)return d}}function Rd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.oh(c,d)}}}function Sd(){return Id}function Td(a,b){return new Fa(a,this.evaluate(b))}
function Ud(a,b){var c=Ca.apply(2,arguments),d;d=new sd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ya(c));this.K.add(a,this.evaluate(g))}function Vd(a,b){return this.evaluate(a)/this.evaluate(b)}function Wd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Bd,f=d instanceof Bd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Xd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function Yd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ya(f,d);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}}}function Zd(a,b,c){if(typeof b==="string")return Yd(a,function(){return b.length},function(f){return f},c);if(b instanceof ab||b instanceof Dd||b instanceof sd||b instanceof wd){var d=b.ya(),e=d.length;return Yd(a,function(){return e},function(f){return d[f]},c)}}
function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){g.set(d,h);return g},e,f)}function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return de(function(h){g.set(d,h);return g},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return de(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return de(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function de(a,b,c){if(typeof b==="string")return Yd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof sd)return Yd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ra(Error("The value is not iterable."));}
function ge(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof sd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Za(m,b);){var n=Ya(m,h);if(n instanceof Fa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Za(p,c);m=p}}
function he(a,b){var c=Ca.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof sd))throw Error("Error: non-List value given for Fn argument names.");return new wd(a,function(){return function(){var f=Ca.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Pd(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new sd(h));var r=Ya(g,c);if(r instanceof Fa)return r.type===
"return"?r.data:r}}())}function ie(a){var b=this.evaluate(a),c=this.K;if(je&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ke(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ra(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof ab||d instanceof Dd||d instanceof sd||d instanceof wd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:rd(e)&&(c=d[e]);else if(d instanceof Bd)return;return c}function le(a,b){return this.evaluate(a)>this.evaluate(b)}function me(a,b){return this.evaluate(a)>=this.evaluate(b)}
function ne(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Bd&&(c=c.getValue());d instanceof Bd&&(d=d.getValue());return c===d}function oe(a,b){return!ne.call(this,a,b)}function pe(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ya(this.K,d);if(e instanceof Fa)return e}var je=!1;
function qe(a,b){return this.evaluate(a)<this.evaluate(b)}function re(a,b){return this.evaluate(a)<=this.evaluate(b)}function se(){for(var a=new sd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function te(){for(var a=new ab,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ue(a,b){return this.evaluate(a)%this.evaluate(b)}
function ve(a,b){return this.evaluate(a)*this.evaluate(b)}function we(a){return-this.evaluate(a)}function xe(a){return!this.evaluate(a)}function ye(a,b){return!Wd.call(this,a,b)}function ze(){return null}function Ae(a,b){return this.evaluate(a)||this.evaluate(b)}function Be(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ce(a){return this.evaluate(a)}function De(){return Ca.apply(0,arguments)}function Ee(a){return new Fa("return",this.evaluate(a))}
function Fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ra(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof wd||d instanceof sd||d instanceof ab)&&d.set(String(e),f);return f}function Ge(a,b){return this.evaluate(a)-this.evaluate(b)}
function He(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Fa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Fa&&(g.type==="return"||g.type==="continue")))return g}
function Ie(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Je(a){var b=this.evaluate(a);return b instanceof wd?"function":typeof b}function Ke(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Le(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ya(this.K,e);if(f instanceof Fa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ya(this.K,e);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Me(a){return~Number(this.evaluate(a))}function Ne(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Pe(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Re(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Se(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Te(){}
function Ue(a,b,c){try{var d=this.evaluate(b);if(d instanceof Fa)return d}catch(h){if(!(h instanceof Qa&&h.Ol))throw h;var e=this.K.rb();a!==""&&(h instanceof Qa&&(h=h.im),e.add(a,new Bd(h)));var f=this.evaluate(c),g=Ya(e,f);if(g instanceof Fa)return g}}function Ve(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Qa&&f.Ol))throw f;c=f}var e=this.evaluate(b);if(e instanceof Fa)return e;if(c)throw c;if(d instanceof Fa)return d};var Ye=function(){this.C=new $a;Xe(this)};Ye.prototype.execute=function(a){return this.C.zj(a)};var Xe=function(a){var b=function(c,d){var e=new xd(String(c),d);e.Ta();var f=String(c);a.C.C.set(f,e);Xa.set(f,e)};b("map",te);b("and",fd);b("contains",id);b("equals",gd);b("or",hd);b("startsWith",jd);b("variable",kd)};Ye.prototype.Mb=function(a){this.C.Mb(a)};var $e=function(){this.H=!1;this.C=new $a;Ze(this);this.H=!0};$e.prototype.execute=function(a){return af(this.C.zj(a))};var bf=function(a,b,c){return af(a.C.Zn(b,c))};$e.prototype.Ta=function(){this.C.Ta()};
var Ze=function(a){var b=function(c,d){var e=String(c),f=new xd(e,d);f.Ta();a.C.C.set(e,f);Xa.set(e,f)};b(0,Jd);b(1,Kd);b(2,Ld);b(3,Md);b(56,Qe);b(57,Ne);b(58,Me);b(59,Se);b(60,Oe);b(61,Pe);b(62,Re);b(53,Nd);b(4,Od);b(5,Pd);b(68,Ue);b(52,Rd);b(6,Sd);b(49,Td);b(7,se);b(8,te);b(9,Pd);b(50,Ud);b(10,Vd);b(12,Wd);b(13,Xd);b(67,Ve);b(51,he);b(47,$d);b(54,ae);b(55,be);b(63,ge);b(64,ce);b(65,ee);b(66,fe);b(15,ie);b(16,ke);b(17,ke);b(18,le);b(19,me);b(20,ne);b(21,oe);b(22,pe);b(23,qe);b(24,re);b(25,ue);b(26,
ve);b(27,we);b(28,xe);b(29,ye);b(45,ze);b(30,Ae);b(32,Be);b(33,Be);b(34,Ce);b(35,Ce);b(46,De);b(36,Ee);b(43,Fe);b(37,Ge);b(38,He);b(39,Ie);b(40,Je);b(44,Te);b(41,Ke);b(42,Le)};$e.prototype.Hd=function(){return this.C.Hd()};$e.prototype.Mb=function(a){this.C.Mb(a)};$e.prototype.Tc=function(a){this.C.Tc(a)};
function af(a){if(a instanceof Fa||a instanceof wd||a instanceof sd||a instanceof ab||a instanceof Dd||a instanceof Bd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var cf=function(a){this.message=a};function df(a){a.Fr=!0;return a};var ef=df(function(a){return typeof a==="string"});function ff(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new cf("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function gf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var hf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function jf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+ff(e)+c}a<<=2;d||(a|=32);return c=""+ff(a|b)+c}
function kf(a,b){var c;var d=a.Sc,e=a.Bh;d===void 0?c="":(e||(e=0),c=""+jf(1,1)+ff(d<<2|e));var f=a.Nl,g=a.Io,h="4"+c+(f?""+jf(2,1)+ff(f):"")+(g?""+jf(12,1)+ff(g):""),m,n=a.Aj;m=n&&hf.test(n)?""+jf(3,2)+n:"";var p,q=a.wj;p=q?""+jf(4,1)+ff(q):"";var r;var t=a.ctid;if(t&&b){var u=jf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+ff(1+y.length)+(a.Zl||0)+y}}else r="";var z=a.xq,B=a.we,D=a.Oa,G=a.Jr,I=h+m+p+r+(z?""+jf(6,1)+ff(z):"")+(B?""+jf(7,3)+ff(B.length)+
B:"")+(D?""+jf(8,3)+ff(D.length)+D:"")+(G?""+jf(9,3)+ff(G.length)+G:""),M;var T=a.Pl;T=T===void 0?{}:T;for(var ea=[],Q=l(Object.keys(T)),W=Q.next();!W.done;W=Q.next()){var ja=W.value;ea[Number(ja)]=T[ja]}if(ea.length){var ka=jf(10,3),Y;if(ea.length===0)Y=ff(0);else{for(var X=[],ha=0,wa=!1,sa=0;sa<ea.length;sa++){wa=!0;var Va=sa%6;ea[sa]&&(ha|=1<<Va);Va===5&&(X.push(ff(ha)),ha=0,wa=!1)}wa&&X.push(ff(ha));Y=X.join("")}var Wa=Y;M=""+ka+ff(Wa.length)+Wa}else M="";var Eb=a.jm,Vb=a.mq;return I+M+(Eb?""+
jf(11,3)+ff(Eb.length)+Eb:"")+(Vb?""+jf(13,3)+ff(Vb.length)+Vb:"")};var lf=function(){function a(b){return{toString:function(){return b}}}return{Mm:a("consent"),Pj:a("convert_case_to"),Qj:a("convert_false_to"),Rj:a("convert_null_to"),Sj:a("convert_true_to"),Tj:a("convert_undefined_to"),Lq:a("debug_mode_metadata"),Ra:a("function"),yi:a("instance_name"),co:a("live_only"),eo:a("malware_disabled"),METADATA:a("metadata"),ho:a("original_activity_id"),gr:a("original_vendor_template_id"),er:a("once_on_load"),fo:a("once_per_event"),pl:a("once_per_load"),ir:a("priority_override"),
lr:a("respected_consent_types"),xl:a("setup_tags"),mh:a("tag_id"),Fl:a("teardown_tags")}}();var Hf;var If=[],Jf=[],Kf=[],Lf=[],Mf=[],Nf,Of,Pf;function Qf(a){Pf=Pf||a}
function Rf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)If.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Lf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Kf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Sf(p[r])}Jf.push(p)}}
function Sf(a){}var Tf,Uf=[],Vf=[];function Wf(a,b){var c={};c[lf.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Xf(a,b,c){try{return Of(Yf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Yf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Zf(a[e],b,c));return d},Zf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Zf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=If[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[lf.yi]);try{var m=Yf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=$f(m,{event:b,index:f,type:2,
name:h});Tf&&(d=Tf.Jo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Zf(a[n],b,c)]=Zf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Zf(a[q],b,c);Pf&&(p=p||Pf.Kp(r));d.push(r)}return Pf&&p?Pf.Oo(d):d.join("");case "escape":d=Zf(a[1],b,c);if(Pf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Pf.Lp(a))return Pf.aq(d);d=String(d);for(var t=2;t<a.length;t++)sf[a[t]]&&(d=sf[a[t]](d));return d;
case "tag":var u=a[1];if(!Lf[u])throw Error("Unable to resolve tag reference "+u+".");return{Tl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[lf.Ra]=a[1];var w=Xf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},$f=function(a,b){var c=a[lf.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Nf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Uf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Jb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=If[q];break;case 1:r=Lf[q];break;default:n="";break a}var t=r&&r[lf.yi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Vf.indexOf(c)===-1){Vf.push(c);
var y=Db();u=e(g);var z=Db()-y,B=Db();v=Hf(c,h,b);w=z-(Db()-B)}else if(e&&(u=e(g)),!e||f)v=Hf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),qd(u)?(Array.isArray(u)?Array.isArray(v):od(u)?od(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var ag=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};va(ag,Error);ag.prototype.getMessage=function(){return this.message};function bg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)bg(a[c],b[c])}};function cg(){return function(a,b){var c;var d=dg;a instanceof Qa?(a.C=d,c=a):c=new Qa(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function dg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)qb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function eg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=fg(a),f=0;f<Jf.length;f++){var g=Jf[f],h=gg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Lf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function gg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function fg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Xf(Kf[c],a));return b[c]}};function hg(a,b){b[lf.Pj]&&typeof a==="string"&&(a=b[lf.Pj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(lf.Rj)&&a===null&&(a=b[lf.Rj]);b.hasOwnProperty(lf.Tj)&&a===void 0&&(a=b[lf.Tj]);b.hasOwnProperty(lf.Sj)&&a===!0&&(a=b[lf.Sj]);b.hasOwnProperty(lf.Qj)&&a===!1&&(a=b[lf.Qj]);return a};var ig=function(){this.C={}},kg=function(a,b){var c=jg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ya(Ca.apply(0,arguments)))})};function lg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new ag(c,d,g);}}
function mg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ya(Ca.apply(1,arguments))));lg(e,b,d,g);lg(f,b,d,g)}}}};var qg=function(){var a=data.permissions||{},b=ng.ctid,c=this;this.H={};this.C=new ig;var d={},e={},f=mg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ya(Ca.apply(1,arguments)))):{}});wb(a,function(g,h){function m(p){var q=Ca.apply(1,arguments);if(!n[p])throw og(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ya(q)))}var n={};wb(h,function(p,q){var r=pg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Ll&&!e[p]&&(e[p]=r.Ll)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw og(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ya(t.slice(1))))}})},rg=function(a){return jg.H[a]||function(){}};
function pg(a,b){var c=Wf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=og;try{return $f(c)}catch(d){return{assert:function(e){throw new ag(e,{},"Permission "+e+" is unknown.");},T:function(){throw new ag(a,{},"Permission "+a+" is unknown.");}}}}function og(a,b,c){return new ag(a,b,c)};var sg=!1;var tg={};tg.Dm=zb('');tg.Xo=zb('');
var xg=function(a){var b={},c=0;wb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(ug.hasOwnProperty(e))b[ug[e]]=g;else if(vg.hasOwnProperty(e)){var h=vg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=wg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];wb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
ug={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},vg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},wg=["ca",
"c2","c3","c4","c5"];function yg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var zg=[];function Ag(a){switch(a){case 1:return 0;case 216:return 15;case 38:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 16;case 75:return 3;case 103:return 13;case 197:return 14;case 114:return 11;case 116:return 4;case 135:return 8;case 136:return 5}}function Bg(a,b){zg[a]=b;var c=Ag(a);c!==void 0&&(Sa[c]=b)}function E(a){Bg(a,!0)}
E(39);E(34);E(35);E(36);
E(56);E(145);E(153);E(144);E(120);
E(5);E(111);E(139);E(87);
E(92);E(159);E(132);
E(20);E(72);E(113);
E(154);E(116);Bg(23,!1),E(24);Ta[1]=yg('1',6E4);Ta[3]=yg('10',1);
Ta[2]=yg('',50);E(29);Cg(26,25);E(37);
E(9);E(91);
E(123);
E(158);E(71);E(136);E(127);E(27);E(69);E(135);
E(95);E(38);E(103);E(112);
E(63);
E(152);
E(101);E(122);E(121);
E(134);
E(22);E(19);
E(90);E(114);
E(59);E(164);E(175);

E(185);E(186);E(192);
E(200);E(210);E(213);function F(a){return!!zg[a]}function Cg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?E(b):E(a)};
var Dg=function(){this.events=[];this.C="";this.qa={};this.baseUrl="";this.N=0;this.P=this.H=!1;this.endpoint=0;F(89)&&(this.P=!0)};Dg.prototype.add=function(a){return this.R(a)?(this.events.push(a),this.C=a.H,this.qa=a.qa,this.baseUrl=a.baseUrl,this.N+=a.P,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ia=a.eventId,this.la=a.priorityId,!0):!1};Dg.prototype.R=function(a){return this.events.length?this.events.length>=20||a.P+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.Ea(a):!0};Dg.prototype.Ea=function(a){var b=this;if(!this.P)return this.C===a.H;var c=Object.keys(this.qa);return c.length===Object.keys(a.qa).length&&c.every(function(d){return a.qa.hasOwnProperty(d)&&String(b.qa[d])===String(a.qa[d])})};var Eg={},Fg=(Eg.uaa=!0,Eg.uab=!0,Eg.uafvl=!0,Eg.uamb=!0,Eg.uam=!0,Eg.uap=!0,Eg.uapv=!0,Eg.uaw=!0,Eg);
var Ig=function(a,b){var c=a.events;if(c.length===1)return Gg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)wb(c[f].Qd,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};wb(e,function(t,u){var v,w=-1,y=0;wb(u,function(z,B){y+=B;var D=(z.length+t.length+2)*(B-1);D>w&&(v=z,w=D)});y===c.length&&(g[t]=v)});Hg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={pj:void 0},p++){var q=[];n.pj={};wb(c[p].Qd,function(t){return function(u,
v){g[u]!==""+v&&(t.pj[u]=v)}}(n));c[p].C&&q.push(c[p].C);Hg(n.pj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Gg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Hg(a.Qd,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Hg=function(a,b){wb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Jg=function(a){var b=[];wb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Kg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.qa=a.qa;this.Qd=a.Qd;this.Vi=a.Vi;this.N=d;this.H=Jg(a.qa);this.C=Jg(a.Vi);this.P=this.C.length;if(e&&this.P>16384)throw Error("EVENT_TOO_LARGE");};
var Ng=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Lg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Mg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Jb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Mg=/^[a-z$_][\w-$]*$/i,Lg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Og=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Pg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Qg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Rg=new vb;function Sg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Rg.get(e);f||(f=new RegExp(b,d),Rg.set(e,f));return f.test(a)}catch(g){return!1}}function Tg(a,b){return String(a).indexOf(String(b))>=0}
function Ug(a,b){return String(a)===String(b)}function Vg(a,b){return Number(a)>=Number(b)}function Wg(a,b){return Number(a)<=Number(b)}function Xg(a,b){return Number(a)>Number(b)}function Yg(a,b){return Number(a)<Number(b)}function Zg(a,b){return Jb(String(a),String(b))};var fh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,gh={Fn:"function",PixieMap:"Object",List:"Array"};
function hh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=fh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof wd?n="Fn":m instanceof sd?n="List":m instanceof ab?n="PixieMap":m instanceof Dd?n="PixiePromise":m instanceof Bd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((gh[n]||n)+", which does not match required type ")+
((gh[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof wd?d.push("function"):g instanceof sd?d.push("Array"):g instanceof ab?d.push("Object"):g instanceof Dd?d.push("Promise"):g instanceof Bd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ih(a){return a instanceof ab}function jh(a){return ih(a)||a===null||kh(a)}
function lh(a){return a instanceof wd}function mh(a){return lh(a)||a===null||kh(a)}function nh(a){return a instanceof sd}function oh(a){return a instanceof Bd}function ph(a){return typeof a==="string"}function qh(a){return ph(a)||a===null||kh(a)}function rh(a){return typeof a==="boolean"}function sh(a){return rh(a)||kh(a)}function th(a){return rh(a)||a===null||kh(a)}function uh(a){return typeof a==="number"}function kh(a){return a===void 0};function vh(a){return""+a}
function wh(a,b){var c=[];return c};function xh(a,b){var c=new wd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ra(g);}});c.Ta();return c}
function yh(a,b){var c=new ab,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ob(e)?c.set(d,xh(a+"_"+d,e)):od(e)?c.set(d,yh(a+"_"+d,e)):(qb(e)||pb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ta();return c};function zh(a,b){if(!ph(a))throw H(this.getName(),["string"],arguments);if(!qh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new ab;return d=yh("AssertApiSubject",
c)};function Ah(a,b){if(!qh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof Dd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new ab;return d=yh("AssertThatSubject",c)};function Bh(a){return function(){for(var b=Ca.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(C(b[e],d));return Ed(a.apply(null,c))}}function Ch(){for(var a=Math,b=Dh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Bh(a[e].bind(a)))}return c};function Eh(a){return a!=null&&Jb(a,"__cvt_")};function Fh(a){var b;return b};function Gh(a){var b;return b};function Hh(a){try{return encodeURI(a)}catch(b){}};function Ih(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Jh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Kh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Jh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Jh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Mh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Kh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Lh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Lh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Mh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Sg(d(c[0]),d(c[1]),!1);case 5:return Ug(d(c[0]),d(c[1]));case 6:return Zg(d(c[0]),d(c[1]));case 7:return Pg(d(c[0]),d(c[1]));case 8:return Tg(d(c[0]),d(c[1]));case 9:return Yg(d(c[0]),d(c[1]));case 10:return Wg(d(c[0]),d(c[1]));case 11:return Xg(d(c[0]),d(c[1]));case 12:return Vg(d(c[0]),d(c[1]));case 13:return Qg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Nh(a){if(!qh(a))throw H(this.getName(),["string|undefined"],arguments);};function Oh(a,b){if(!uh(a)||!uh(b))throw H(this.getName(),["number","number"],arguments);return tb(a,b)};function Ph(){return(new Date).getTime()};function Qh(a){if(a===null)return"null";if(a instanceof sd)return"array";if(a instanceof wd)return"function";if(a instanceof Bd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Rh(a){function b(c){return function(d){try{return c(d)}catch(e){(sg||tg.Dm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Ed(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(C(c))}),publicName:"JSON"}};function Sh(a){return yb(C(a,this.K))};function Th(a){return Number(C(a,this.K))};function Uh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Vh(a,b,c){var d=null,e=!1;return e?d:null};var Dh="floor ceil round max min abs pow sqrt".split(" ");function Wh(){var a={};return{lp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Am:function(b,c){a[b]=c},reset:function(){a={}}}}function Xh(a,b){return function(){return wd.prototype.invoke.apply(a,[b].concat(ya(Ca.apply(0,arguments))))}}
function Yh(a,b){if(!ph(a))throw H(this.getName(),["string","any"],arguments);}
function Zh(a,b){if(!ph(a)||!ih(b))throw H(this.getName(),["string","PixieMap"],arguments);};var $h={};var ai=function(a){var b=new ab;if(a instanceof sd)for(var c=a.ya(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof wd)for(var f=a.ya(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
$h.keys=function(a){hh(this.getName(),arguments);if(a instanceof sd||a instanceof wd||typeof a==="string")a=ai(a);if(a instanceof ab||a instanceof Dd)return new sd(a.ya());return new sd};
$h.values=function(a){hh(this.getName(),arguments);if(a instanceof sd||a instanceof wd||typeof a==="string")a=ai(a);if(a instanceof ab||a instanceof Dd)return new sd(a.yc());return new sd};
$h.entries=function(a){hh(this.getName(),arguments);if(a instanceof sd||a instanceof wd||typeof a==="string")a=ai(a);if(a instanceof ab||a instanceof Dd)return new sd(a.ac().map(function(b){return new sd(b)}));return new sd};
$h.freeze=function(a){(a instanceof ab||a instanceof Dd||a instanceof sd||a instanceof wd)&&a.Ta();return a};$h.delete=function(a,b){if(a instanceof ab&&!a.Cb())return a.remove(b),!0;return!1};function J(a,b){var c=Ca.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.jq){try{d.Ml.apply(null,[b].concat(ya(c)))}catch(e){throw ib("TAGGING",21),e;}return}d.Ml.apply(null,[b].concat(ya(c)))};var bi=function(){this.H={};this.C={};this.N=!0;};bi.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};bi.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
bi.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:ob(b)?xh(a,b):yh(a,b)};function ci(a,b){var c=void 0;return c};function di(){var a={};
return a};var K={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",hc:"region",fa:"consent_updated",sg:"wait_for_update",Vm:"app_remove",Wm:"app_store_refund",Xm:"app_store_subscription_cancel",Ym:"app_store_subscription_convert",Zm:"app_store_subscription_renew",bn:"consent_update",Xj:"add_payment_info",Yj:"add_shipping_info",Td:"add_to_cart",Ud:"remove_from_cart",Zj:"view_cart",Vc:"begin_checkout",Vd:"select_item",kc:"view_item_list",Dc:"select_promotion",mc:"view_promotion",
wb:"purchase",Wd:"refund",Nb:"view_item",bk:"add_to_wishlist",dn:"exception",fn:"first_open",gn:"first_visit",ra:"gtag.config",Ob:"gtag.get",hn:"in_app_purchase",Wc:"page_view",jn:"screen_view",kn:"session_start",ln:"source_update",mn:"timing_complete",nn:"track_social",Xd:"user_engagement",on:"user_id_update",Me:"gclid_link_decoration_source",Ne:"gclid_storage_source",nc:"gclgb",xb:"gclid",dk:"gclid_len",Yd:"gclgs",Zd:"gcllp",ae:"gclst",Ba:"ads_data_redaction",Oe:"gad_source",Pe:"gad_source_src",
Xc:"gclid_url",ek:"gclsrc",Qe:"gbraid",be:"wbraid",Ha:"allow_ad_personalization_signals",zg:"allow_custom_scripts",Re:"allow_direct_google_requests",Ag:"allow_display_features",Bg:"allow_enhanced_conversions",Pb:"allow_google_signals",lb:"allow_interest_groups",pn:"app_id",qn:"app_installer_id",rn:"app_name",sn:"app_version",Qb:"auid",tn:"auto_detection_enabled",Yc:"aw_remarketing",Ph:"aw_remarketing_only",Cg:"discount",Dg:"aw_feed_country",Eg:"aw_feed_language",xa:"items",Fg:"aw_merchant_id",fk:"aw_basket_type",
Se:"campaign_content",Te:"campaign_id",Ue:"campaign_medium",Ve:"campaign_name",We:"campaign",Xe:"campaign_source",Ye:"campaign_term",Rb:"client_id",gk:"rnd",Qh:"consent_update_type",un:"content_group",vn:"content_type",mb:"conversion_cookie_prefix",Ze:"conversion_id",Va:"conversion_linker",Rh:"conversion_linker_disabled",Zc:"conversion_api",Gg:"cookie_deprecation",yb:"cookie_domain",zb:"cookie_expires",Db:"cookie_flags",bd:"cookie_name",Sb:"cookie_path",nb:"cookie_prefix",Ec:"cookie_update",dd:"country",
ab:"currency",Sh:"customer_buyer_stage",af:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",bf:"custom_map",Vh:"gcldc",ed:"dclid",hk:"debug_mode",na:"developer_id",wn:"disable_merchant_reported_purchases",fd:"dc_custom_params",xn:"dc_natural_search",ik:"dynamic_event_settings",jk:"affiliation",Hg:"checkout_option",Wh:"checkout_step",kk:"coupon",cf:"item_list_name",Xh:"list_name",yn:"promotions",ce:"shipping",lk:"tax",Ig:"engagement_time_msec",Jg:"enhanced_client_id",Yh:"enhanced_conversions",
mk:"enhanced_conversions_automatic_settings",df:"estimated_delivery_date",Zh:"euid_logged_in_state",ef:"event_callback",zn:"event_category",Tb:"event_developer_id_string",An:"event_label",gd:"event",Kg:"event_settings",Lg:"event_timeout",Bn:"description",Cn:"fatal",Dn:"experiments",ai:"firebase_id",de:"first_party_collection",Mg:"_x_20",qc:"_x_19",nk:"fledge_drop_reason",pk:"fledge",qk:"flight_error_code",rk:"flight_error_message",sk:"fl_activity_category",tk:"fl_activity_group",bi:"fl_advertiser_id",
uk:"fl_ar_dedupe",ff:"match_id",vk:"fl_random_number",wk:"tran",xk:"u",Ng:"gac_gclid",ee:"gac_wbraid",yk:"gac_wbraid_multiple_conversions",zk:"ga_restrict_domain",di:"ga_temp_client_id",En:"ga_temp_ecid",hd:"gdpr_applies",Ak:"geo_granularity",jd:"value_callback",Fc:"value_key",rc:"google_analysis_params",fe:"_google_ng",he:"google_signals",Bk:"google_tld",hf:"gpp_sid",jf:"gpp_string",Og:"groups",Ck:"gsa_experiment_id",kf:"gtag_event_feature_usage",Dk:"gtm_up",Gc:"iframe_state",lf:"ignore_referrer",
ei:"internal_traffic_results",Ek:"_is_fpm",Hc:"is_legacy_converted",Ic:"is_legacy_loaded",Pg:"is_passthrough",kd:"_lps",Ab:"language",Qg:"legacy_developer_id_string",Wa:"linker",nf:"accept_incoming",Jc:"decorate_forms",oa:"domains",ld:"url_position",md:"merchant_feed_label",nd:"merchant_feed_language",od:"merchant_id",Fk:"method",Gn:"name",Gk:"navigation_type",pf:"new_customer",Rg:"non_interaction",Hn:"optimize_id",Hk:"page_hostname",qf:"page_path",Xa:"page_referrer",Eb:"page_title",Ik:"passengers",
Jk:"phone_conversion_callback",In:"phone_conversion_country_code",Kk:"phone_conversion_css_class",Jn:"phone_conversion_ids",Lk:"phone_conversion_number",Mk:"phone_conversion_options",Kn:"_platinum_request_status",Ln:"_protected_audience_enabled",ie:"quantity",Sg:"redact_device_info",fi:"referral_exclusion_definition",Oq:"_request_start_time",Vb:"restricted_data_processing",Mn:"retoken",Nn:"sample_rate",gi:"screen_name",Kc:"screen_resolution",Nk:"_script_source",On:"search_term",ob:"send_page_view",
pd:"send_to",rd:"server_container_url",rf:"session_duration",Tg:"session_engaged",hi:"session_engaged_time",Wb:"session_id",Ug:"session_number",tf:"_shared_user_id",je:"delivery_postal_code",Pq:"_tag_firing_delay",Qq:"_tag_firing_time",Rq:"temporary_client_id",ii:"_timezone",ji:"topmost_url",Pn:"tracking_id",ki:"traffic_type",Qa:"transaction_id",sc:"transport_url",Ok:"trip_type",ud:"update",Fb:"url_passthrough",Pk:"uptgs",uf:"_user_agent_architecture",vf:"_user_agent_bitness",wf:"_user_agent_full_version_list",
xf:"_user_agent_mobile",yf:"_user_agent_model",zf:"_user_agent_platform",Af:"_user_agent_platform_version",Bf:"_user_agent_wow64",cb:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",uc:"user_data_mode",Vg:"user_data_settings",Ma:"user_id",Xb:"user_properties",Qk:"_user_region",Cf:"us_privacy_string",Da:"value",Rk:"wbraid_multiple_conversions",wd:"_fpm_parameters",xi:"_host_name",al:"_in_page_command",
bl:"_ip_override",kl:"_is_passthrough_cid",vc:"non_personalized_ads",Hi:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Ub:"global_developer_id_string",sd:"tc_privacy_string"}};var ei={},fi=(ei[K.m.fa]="gcu",ei[K.m.nc]="gclgb",ei[K.m.xb]="gclaw",ei[K.m.dk]="gclid_len",ei[K.m.Yd]="gclgs",ei[K.m.Zd]="gcllp",ei[K.m.ae]="gclst",ei[K.m.Qb]="auid",ei[K.m.Cg]="dscnt",ei[K.m.Dg]="fcntr",ei[K.m.Eg]="flng",ei[K.m.Fg]="mid",ei[K.m.fk]="bttype",ei[K.m.Rb]="gacid",ei[K.m.oc]="label",ei[K.m.Zc]="capi",ei[K.m.Gg]="pscdl",ei[K.m.ab]="currency_code",ei[K.m.Sh]="clobs",ei[K.m.af]="vdltv",ei[K.m.Th]="clolo",ei[K.m.Uh]="clolb",ei[K.m.hk]="_dbg",ei[K.m.df]="oedeld",ei[K.m.Tb]="edid",ei[K.m.nk]=
"fdr",ei[K.m.pk]="fledge",ei[K.m.Ng]="gac",ei[K.m.ee]="gacgb",ei[K.m.yk]="gacmcov",ei[K.m.hd]="gdpr",ei[K.m.Ub]="gdid",ei[K.m.fe]="_ng",ei[K.m.hf]="gpp_sid",ei[K.m.jf]="gpp",ei[K.m.Ck]="gsaexp",ei[K.m.kf]="_tu",ei[K.m.Gc]="frm",ei[K.m.Pg]="gtm_up",ei[K.m.kd]="lps",ei[K.m.Qg]="did",ei[K.m.md]="fcntr",ei[K.m.nd]="flng",ei[K.m.od]="mid",ei[K.m.pf]=void 0,ei[K.m.Eb]="tiba",ei[K.m.Vb]="rdp",ei[K.m.Wb]="ecsid",ei[K.m.tf]="ga_uid",ei[K.m.je]="delopc",ei[K.m.sd]="gdpr_consent",ei[K.m.Qa]="oid",ei[K.m.Pk]=
"uptgs",ei[K.m.uf]="uaa",ei[K.m.vf]="uab",ei[K.m.wf]="uafvl",ei[K.m.xf]="uamb",ei[K.m.yf]="uam",ei[K.m.zf]="uap",ei[K.m.Af]="uapv",ei[K.m.Bf]="uaw",ei[K.m.li]="ec_lat",ei[K.m.mi]="ec_meta",ei[K.m.ni]="ec_m",ei[K.m.oi]="ec_sel",ei[K.m.ri]="ec_s",ei[K.m.uc]="ec_mode",ei[K.m.Ma]="userId",ei[K.m.Cf]="us_privacy",ei[K.m.Da]="value",ei[K.m.Rk]="mcov",ei[K.m.xi]="hn",ei[K.m.al]="gtm_ee",ei[K.m.vc]="npa",ei[K.m.Ze]=null,ei[K.m.Kc]=null,ei[K.m.Ab]=null,ei[K.m.xa]=null,ei[K.m.Ca]=null,ei[K.m.Xa]=null,ei[K.m.ji]=
null,ei[K.m.wd]=null,ei[K.m.Me]=null,ei[K.m.Ne]=null,ei[K.m.rc]=null,ei);function gi(a,b){if(a){var c=a.split("x");c.length===2&&(hi(b,"u_w",c[0]),hi(b,"u_h",c[1]))}}
function ii(a){var b=ji;b=b===void 0?ki:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(li(q.value)),r.push(li(q.quantity)),r.push(li(q.item_id)),r.push(li(q.start_date)),r.push(li(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ki(a){return mi(a.item_id,a.id,a.item_name)}function mi(){for(var a=l(Ca.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ni(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function hi(a,b,c){c===void 0||c===null||c===""&&!Fg[b]||(a[b]=c)}function li(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var oi={},pi=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=tb(0,1)===0,b=tb(0,1)===0,c++,c>30)return;return a},ri={oq:qi};function si(a,b){var c=oi[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;oi[b].active||(oi[b].probability>.5?ti(a,e):f<=0||f>1||ri.oq(a,b))}}
function qi(a,b){var c=oi[b],d=c.controlId2;if(!(tb(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;ui(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function ti(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function ui(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=pi()?0:1;e&&(g|=(pi()?0:1)<<1);g===0?(ti(a,c),f()):g===1?ti(a,d):g===2&&ti(a,e)}};var L={J:{Jj:"call_conversion",W:"conversion",Qn:"floodlight",Ef:"ga_conversion",Di:"landing_page",Ia:"page_view",ma:"remarketing",Ya:"user_data_lead",Ka:"user_data_web"}};function xi(a,b){if(!yi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var zi=!1;
if(A.querySelectorAll)try{var Ai=A.querySelectorAll(":root");Ai&&Ai.length==1&&Ai[0]==A.documentElement&&(zi=!0)}catch(a){}var yi=zi;var Bi="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Ci="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Di(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Ei(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Ei(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Fi(a){if(F(178)&&a){Di(Bi,a);for(var b=rb(a.address),c=0;c<b.length;c++){var d=b[c];d&&Di(Ci,d)}var e=a.home_address;e&&Di(Ci,e)}}
function Gi(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Hi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var Ii=[],Ji=[],Ki,Li;function Mi(a){Ki?Ki(a):Ii.push(a)}function Ni(a,b){if(!F(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Mi(a),b):c}function Oi(a,b){if(!F(190))return b;var c=Pi(a,"");return c!==b?(Mi(a),b):c}function Pi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Qi(a,b){if(!F(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Mi(a),b)}function Ri(a,b){var c;c=c===void 0?"":c;if(!F(225))return b;var d,e,f,g=(d=(e=data)==null?void 0:(f=e.blob)==null?void 0:f[46])&&(d==null?0:d.hasOwnProperty(a))?String(d[a]):c;return g!==b?(Li?Li(a):Ji.push(a),b):g}
function Si(){var a=Ti,b=Ui;Ki=a;for(var c=l(Ii),d=c.next();!d.done;d=c.next())a(d.value);Ii.length=0;if(F(225)){Li=b;for(var e=l(Ji),f=e.next();!f.done;f=e.next())b(f.value);Ji.length=0}};function Vi(){this.blockSize=-1};function Wi(a,b){this.blockSize=-1;this.blockSize=64;this.N=Da.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ia=a;this.R=b;this.la=Da.Int32Array?new Int32Array(64):Array(64);Xi===void 0&&(Da.Int32Array?Xi=new Int32Array(Yi):Xi=Yi);this.reset()}Ea(Wi,Vi);for(var Zi=[],$i=0;$i<63;$i++)Zi[$i]=0;var aj=[].concat(128,Zi);
Wi.prototype.reset=function(){this.P=this.H=0;var a;if(Da.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var bj=function(a){for(var b=a.N,c=a.la,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Xi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Wi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(bj(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(bj(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Wi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(aj,56-this.H):this.update(aj,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;bj(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Yi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Xi;function cj(){Wi.call(this,8,dj)}Ea(cj,Wi);var dj=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var ej=/^[0-9A-Fa-f]{64}$/;function fj(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function gj(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(ej.test(a))return Promise.resolve(a);try{var d=fj(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return hj(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function hj(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var ij={Jm:Ri(20,'5000'),Km:Ri(21,'5000'),Tm:Ri(15,''),Um:Ri(14,'1000'),Un:Ri(16,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD'),Vn:Ri(17,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD'),po:Oi(44,'101509157~103116026~103200004~103233427~104684208~104684211')},jj={yo:Number(ij.Jm)||-1,zo:Number(ij.Km)||-1,Uo:Number(ij.Tm)||
0,Wo:Number(ij.Um)||0,pp:ij.Un.split("~"),qp:ij.Vn.split("~"),Hq:ij.po};ma(Object,"assign").call(Object,{},jj);function N(a){ib("GTM",a)};
var nj=function(a,b){var c=F(178),d=["tv.1"],e=["tvd.1"],f=kj(a);if(f)return d.push(f),{jb:!1,Cj:d.join("~"),og:{},Ch:c?e.join("~"):void 0};var g={},h=0;var m=0,n=lj(a,function(u,v,w){m++;var y=u.value,z;if(w){var B=v+"__"+h++;z="${userData."+B+"|sha256}";g[B]=y}else z=encodeURIComponent(encodeURIComponent(y));u.index!==void 0&&(v+=u.index);d.push(v+"."+z);if(c){var D=Gi(m,v,u.metadata);D&&e.push(D)}}).jb,p=e.join("~");
var q=d.join("~"),r={userData:g},t=b===3;return b===2||t?{jb:n,Cj:q,og:r,Vo:t?"tv.9~${"+(q+"|encryptRsa}"):"tv.1~${"+(q+"|encrypt}"),encryptionKeyString:t?Ri(13,'MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAvMBNun6iQWLRC7leE+bbdzvSfi/vuWbUVnHQbRZGCQu9gU8gUhDTQvTCJ6vIl+PvFNutjUQo3svAxeWk9LyQdMWml3w8hLNKy2oaiCBwi5xPmpzrCWeYG4JaGpBom2PAojrRZdzNnrtutX5XvkcQ1ao/Z8CtYrC6cf9bhdVn46zTQaOBS2uokc4ihM9s0p3yESKcdaihK0wlFie0XvNwp/wR4mKlIwWOfDfnz3QUVDJiuFirBjZNoYsa3TmRRaJA3iih9I1fVwh4p7RSXHg6a+8ERQlJxx6HNm+GBh4VhzPwfRXGQX6sCVLVpbF9N/jr3DbE08lghW07/soO4Lq8IOWmaoo0kGvWwebbXSx9UpPCofGxXrbrDbuKaoFrrtnmqBsiaVOHxcg07N23bnxv9NfgjIuUBGaR2vykgWvWqViN3yrfAHmhXurjQtFu/csE8W95D3yP7a9rywXpELv047MSD+YthoXxGQmSOB4A1SG3SmJgbs8Ee8x/JBmBOylTAgMBAAE\x3d'):mj(),Ch:c?p:void 0}:{jb:n,Cj:q,og:r,Ch:c?p:void 0}},pj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=oj(a);return lj(b,function(){}).jb},lj=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=
qj[g.name];if(h){var m=rj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{jb:d,cj:c}},rj=function(a){var b=sj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(tj.test(e)||ej.test(e))}return d},sj=function(a){return uj.indexOf(a)!==-1},mj=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BLmFt6UXBhRmCroatpW1SXiUGX8nlIzsjWuo/35QAO+zaS+otiG5QcR9nM1Cps71ya2tmVIsN5veaAal7MHFLEs\x3d\x22,\x22version\x22:0},\x22id\x22:\x22aec78412-6373-47d5-ac96-9fcee93fb999\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BC/FqS2VfJxJt+KUoa5szFzBglEsbyx+I9x123cX99SEO7P1N7hO6AIp93nTAdi/z2DFSAto+EqKKdcuaTb9W0s\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a8322124-3ea2-4d88-b25b-86e2f0112cae\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BKfFh+mfP+VYN5VmB9shcyG0A1lRYz8Xzw3WGLlsKlBmFEaKsavgS+aJLQV57OOtxcD75yF5XPI4JCpAEVT6aZE\x3d\x22,\x22version\x22:0},\x22id\x22:\x2269d58b45-d2bb-4a7f-9952-57e6e8373ee3\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BGKg2rrDYEGZYBnoJcCvLOBw40XwX02uo+UmyosodkDpDhfJRS/gnmzpZxgdB0K64JD4BNvJP8lOXmDgfjDJnr0\x3d\x22,\x22version\x22:0},\x22id\x22:\x221cfcadd3-649d-4616-a730-b7cbb203d3b2\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BIj0YjU0Id8OOxdy8oAkpsYU3WUMzeTX3IB3zolk/AGHi8e4L1Wndgs+eEljcMtqAzqNrV2PUboMi62U86LWEtA\x3d\x22,\x22version\x22:0},\x22id\x22:\x2212ffea68-4f40-48ea-9714-010853b2215c\x22}]}'},xj=function(a){if(x.Promise){var b=void 0;return b}},Bj=function(a,b,c){if(x.Promise)try{var d=oj(a),
e=yj(d).then(zj);return e}catch(h){}},Dj=function(a){try{return zj(Cj(oj(a)))}catch(b){}},wj=function(a,b){var c=void 0;return c},zj=function(a){var b=F(178),c=a.Rc,d=["tv.1"],e=["tvd.1"],f=kj(c);if(f)return d.push(f),{Jb:d.join("~"),cj:!1,jb:!1,bj:!0,Ch:b?e.join("~"):void 0};var g=c.filter(function(q){return!rj(q)}),h=0,m=lj(g,function(q,r){h++;var t=q.value,u=q.index;u!==void 0&&(r+=u);d.push(r+"."+t);
if(b){var v=Gi(h,r,q.metadata);v&&e.push(v)}}),n=m.cj,p=m.jb;return{Jb:encodeURIComponent(d.join("~")),cj:n,jb:p,bj:!1,Ch:b?e.join("~"):void 0}},kj=function(a){if(a.length===1&&a[0].name==="error_code")return qj.error_code+"."+a[0].value},Aj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(qj[d.name]&&d.value)return!0}return!1},oj=function(a){function b(t,u,v,w,y){var z=Ej(t);if(z!=="")if(ej.test(z)){y&&(y.isPreHashed=!0);
var B={name:u,value:z,index:w};y&&(B.metadata=y);m.push(B)}else{var D=v(z),G={name:u,value:D,index:w};y&&(G.metadata=y,D&&(y.rawLength=String(z).length,y.normalizedLength=D.length));m.push(G)}}function c(t,u){var v=t;if(pb(v)||Array.isArray(v)){v=rb(t);for(var w=0;w<v.length;++w){var y=Ej(v[w]),z=ej.test(y);u&&!z&&N(89);!u&&z&&N(88)}}}function d(t,u){var v=t[u];c(v,!1);var w=Fj[u];t[w]&&(t[u]&&N(90),v=t[w],c(v,!0));return v}function e(t,u,v,w){var y=t._tag_metadata||{},z=t[u],B=y[u];c(z,!1);var D=
Fj[u];if(D){var G=t[D],I=y[D];G&&(z&&N(90),z=G,B=I,c(z,!0))}if(w!==void 0)b(z,u,v,w,B);else{z=rb(z);B=rb(B);for(var M=0;M<z.length;++M)b(z[M],u,v,void 0,B[M])}}function f(t,u,v){if(F(178))e(t,u,v,void 0);else for(var w=rb(d(t,u)),y=0;y<w.length;++y)b(w[y],u,v)}function g(t,u,v,w){if(F(178))e(t,u,v,w);else{var y=d(t,u);b(y,u,v,w)}}function h(t){return function(u){N(64);return t(u)}}var m=[];if(x.location.protocol!=="https:")return m.push({name:"error_code",value:"e3",index:void 0}),m;f(a,"email",Gj);
f(a,"phone_number",Hj);f(a,"first_name",h(Ij));f(a,"last_name",h(Ij));var n=a.home_address||{};f(n,"street",h(Jj));f(n,"city",h(Jj));f(n,"postal_code",h(Kj));f(n,"region",h(Jj));f(n,"country",h(Kj));for(var p=rb(a.address||{}),q=0;q<p.length;q++){var r=p[q];g(r,"first_name",Ij,q);g(r,"last_name",Ij,q);g(r,"street",Jj,q);g(r,"city",Jj,q);g(r,"postal_code",Kj,q);g(r,"region",Jj,q);g(r,"country",Kj,q)}return m},Lj=function(a){var b=a?oj(a):[];return zj({Rc:b})},Mj=function(a){return a&&a!=null&&Object.keys(a).length>
0&&x.Promise?oj(a).some(function(b){return b.value&&sj(b.name)&&!ej.test(b.value)}):!1},Ej=function(a){return a==null?"":pb(a)?Bb(String(a)):"e0"},Kj=function(a){return a.replace(Nj,"")},Ij=function(a){return Jj(a.replace(/\s/g,""))},Jj=function(a){return Bb(a.replace(Oj,"").toLowerCase())},Hj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Pj.test(a)?a:"e0"},Gj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&
(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Qj.test(c))return c}return"e0"},Cj=function(a){try{return a.forEach(function(b){if(b.value&&sj(b.name)){var c;var d=b.value,e=x;if(d===""||d==="e0"||ej.test(d))c=d;else try{var f=new cj;f.update(fj(d));c=hj(f.digest(),e)}catch(g){c="e2"}b.value=c}}),{Rc:a}}catch(b){return{Rc:[]}}},yj=function(a){return a.some(function(b){return b.value&&sj(b.name)})?x.Promise?Promise.all(a.map(function(b){return b.value&&sj(b.name)?gj(b.value).then(function(c){b.value=c}):Promise.resolve()})).then(function(){return{Rc:a}}).catch(function(){return{Rc:[]}}):
Promise.resolve({Rc:[]}):Promise.resolve({Rc:a})},Oj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Qj=/^\S+@\S+\.\S+$/,Pj=/^\+\d{10,15}$/,Nj=/[.~]/g,tj=/^[0-9A-Za-z_-]{43}$/,Rj={},qj=(Rj.email="em",Rj.phone_number="pn",Rj.first_name="fn",Rj.last_name="ln",Rj.street="sa",Rj.city="ct",Rj.region="rg",Rj.country="co",Rj.postal_code="pc",Rj.error_code="ec",Rj),Sj={},Fj=(Sj.email="sha256_email_address",Sj.phone_number="sha256_phone_number",Sj.first_name="sha256_first_name",Sj.last_name="sha256_last_name",Sj.street=
"sha256_street",Sj);var uj=Object.freeze(["email","phone_number","first_name","last_name","street"]);var Tj={},Uj=(Tj[K.m.lb]=1,Tj[K.m.rd]=2,Tj[K.m.sc]=2,Tj[K.m.Ba]=3,Tj[K.m.af]=4,Tj[K.m.zg]=5,Tj[K.m.Ec]=6,Tj[K.m.nb]=6,Tj[K.m.yb]=6,Tj[K.m.bd]=6,Tj[K.m.Sb]=6,Tj[K.m.Db]=6,Tj[K.m.zb]=7,Tj[K.m.Vb]=9,Tj[K.m.Ag]=10,Tj[K.m.Pb]=11,Tj),Vj={},Wj=(Vj.unknown=13,Vj.standard=14,Vj.unique=15,Vj.per_session=16,Vj.transactions=17,Vj.items_sold=18,Vj);var kb=[];function Xj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Uj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Uj[f],h=b;h=h===void 0?!1:h;ib("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(kb[g]=!0)}}};var Yj=function(){this.C=new Set;this.H=new Set},ak=function(a){var b=Zj.R;a=a===void 0?[]:a;var c=[].concat(ya(b.C)).concat([].concat(ya(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},bk=function(){var a=[].concat(ya(Zj.R.C));a.sort(function(b,c){return b-c});return a},ck=function(){var a=Zj.R,b=jj.Hq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var dk={},ek=Oi(14,"57s0"),fk=Qi(15,Number("2")),gk=Oi(19,"dataLayer");Oi(20,"");Oi(16,"ChEI8OKhxAYQ47yi8OGBmrn8ARIlAKjB7+6ORaQaidKUGxpSDty6l507k5Sny09Kt9G+q1KuHYC57hoCKwE\x3d");var hk={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},ik={__paused:1,__tg:1},jk;for(jk in hk)hk.hasOwnProperty(jk)&&(ik[jk]=1);var kk=Ni(11,zb("true")),lk=!1;
function mk(){var a=!1;a=!0;return a}var nk=F(218)?Ni(45,mk()):mk(),ok,pk=!1;ok=pk;dk.xg=Oi(3,"www.googletagmanager.com");var qk=""+dk.xg+(nk?"/gtag/js":"/gtm.js"),rk=null,sk=null,tk={},uk={};dk.Nm=Ni(2,zb("true"));var vk="";
dk.Ii=vk;var Zj=new function(){this.R=new Yj;this.C=this.N=!1;this.H=0;this.Ea=this.Sa=this.pb=this.P="";this.ia=this.la=!1};function wk(){var a;a=a===void 0?[]:a;return ak(a).join("~")}function xk(){var a=Zj.P.length;return Zj.P[a-1]==="/"?Zj.P.substring(0,a-1):Zj.P}function yk(){return Zj.C?F(84)?Zj.H===0:Zj.H!==1:!1}function zk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Ak=new vb,Bk={},Ck={},Fk={name:gk,set:function(a,b){pd(Lb(a,b),Bk);Dk()},get:function(a){return Ek(a,2)},reset:function(){Ak=new vb;Bk={};Dk()}};function Ek(a,b){return b!=2?Ak.get(a):Gk(a)}function Gk(a,b){var c=a.split(".");b=b||[];for(var d=Bk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Hk(a,b){Ck.hasOwnProperty(a)||(Ak.set(a,b),pd(Lb(a,b),Bk),Dk())}
function Ik(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Ek(c,1);if(Array.isArray(d)||od(d))d=pd(d,null);Ck[c]=d}}function Dk(a){wb(Ck,function(b,c){Ak.set(b,c);pd(Lb(b),Bk);pd(Lb(b,c),Bk);a&&delete Ck[b]})}function Jk(a,b){var c,d=(b===void 0?2:b)!==1?Gk(a):Ak.get(a);md(d)==="array"||md(d)==="object"?c=pd(d,null):c=d;return c};
var Lk=function(a){for(var b=[],c=Object.keys(Kk),d=0;d<c.length;d++){var e=c[d],f=Kk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},Mk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},Nk=function(a,b,c){if(a!==void 0)return Array.isArray(a)?a.map(function(){return{mode:"m",location:b,selector:c}}):{mode:"m",location:b,selector:c}},Ok=function(a,b,c,d,e){if(!c)return!1;for(var f=String(c.value),g,h=void 0,m=f.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(D){return D.trim()}).filter(function(D){return D&&
!Jb(D,"#")&&!Jb(D,".")}),n=0;n<m.length;n++){var p=m[n];if(Jb(p,"dataLayer."))g=Ek(p.substring(10)),h=Nk(g,"d",p);else{var q=p.split(".");g=x[q.shift()];for(var r=0;r<q.length;r++)g=g&&g[q[r]];h=Nk(g,"j",p)}if(g!==void 0)break}if(g===void 0&&yi)try{var t=yi?A.querySelectorAll(f):null;if(t&&t.length>0){g=[];for(var u=0;u<t.length&&u<(b==="email"||b==="phone_number"?5:1);u++)g.push(Sc(t[u])||Bb(t[u].value));g=g.length===1?g[0]:g;h=Nk(g,"c",f)}}catch(D){N(149)}if(F(60)){for(var v,w,y=0;y<m.length;y++){var z=
m[y];v=Ek(z);if(v!==void 0){w=Nk(v,"d",z);break}}var B=g!==void 0;e[b]=Mk(v!==void 0,B);B||(g=v,h=w)}return g?(a[b]=g,d&&h&&(d[b]=h),!0):!1},Pk=function(a,b,c){b=b===void 0?{}:b;c=c===void 0?!1:c;if(a){var d={},e=!1,f={};e=Ok(d,"email",a.email,f,b)||e;e=Ok(d,"phone_number",a.phone,f,b)||e;d.address=[];for(var g=a.name_and_address||[],h=0;h<g.length;h++){var m={},n={};e=Ok(m,"first_name",g[h].first_name,n,b)||e;e=Ok(m,"last_name",g[h].last_name,n,b)||e;e=Ok(m,"street",g[h].street,n,b)||e;e=Ok(m,"city",
g[h].city,n,b)||e;e=Ok(m,"region",g[h].region,n,b)||e;e=Ok(m,"country",g[h].country,n,b)||e;e=Ok(m,"postal_code",g[h].postal_code,n,b)||e;d.address.push(m);c&&(m._tag_metadata=n)}c&&(d._tag_metadata=f);return e?d:void 0}},Qk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&od(b))return b;var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&ib("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Pk(a[K.m.mk])}},Rk=function(a){return od(a)?
!!a.enable_code:!1},Kk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Uk=/:[0-9]+$/,Vk=/^\d+\.fls\.doubleclick\.net$/;function Wk(a,b,c,d){var e=Xk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Xk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=xa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Yk(a){try{return decodeURIComponent(a)}catch(b){}}function Zk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=$k(a.protocol)||$k(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Uk,"").toLowerCase());return al(a,b,c,d,e)}
function al(a,b,c,d,e){var f,g=$k(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=bl(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Uk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||ib("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Wk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function $k(a){return a?a.replace(":","").toLowerCase():""}function bl(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var cl={},dl=0;
function el(a){var b=cl[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||ib("TAGGING",1),d="/"+d);var e=c.hostname.replace(Uk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};dl<5&&(cl[a]=b,dl++)}return b}function fl(a,b,c){var d=el(a);return Qb(b,d,c)}
function gl(a){var b=el(x.location.href),c=Zk(b,"host",!1);if(c&&c.match(Vk)){var d=Zk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var hl={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},il=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function jl(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return el(""+c+b).href}}function kl(a,b){if(yk()||Zj.N)return jl(a,b)}
function ll(){return!!dk.Ii&&dk.Ii.split("@@").join("")!=="SGTM_TOKEN"}function ml(a){for(var b=l([K.m.rd,K.m.sc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function nl(a,b,c){c=c===void 0?"":c;if(!yk())return a;var d=b?hl[a]||"":"";d==="/gs"&&(c="");return""+xk()+d+c}function ol(a){if(!yk())return a;for(var b=l(il),c=b.next();!c.done;c=b.next())if(Jb(a,""+xk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function pl(a){var b=String(a[lf.Ra]||"").replace(/_/g,"");return Jb(b,"cvt")?"cvt":b}var ql=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var rl={kq:Qi(27,Number("0.005000")),So:Qi(42,Number("0.010000"))},sl=Math.random(),tl=ql||sl<Number(rl.kq),ul=ql||sl>=1-Number(rl.So);var vl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},wl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var xl,yl;a:{for(var zl=["CLOSURE_FLAGS"],Al=Da,Bl=0;Bl<zl.length;Bl++)if(Al=Al[zl[Bl]],Al==null){yl=null;break a}yl=Al}var Cl=yl&&yl[610401301];xl=Cl!=null?Cl:!1;function Dl(){var a=Da.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var El,Fl=Da.navigator;El=Fl?Fl.userAgentData||null:null;function Gl(a){if(!xl||!El)return!1;for(var b=0;b<El.brands.length;b++){var c=El.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Hl(a){return Dl().indexOf(a)!=-1};function Il(){return xl?!!El&&El.brands.length>0:!1}function Jl(){return Il()?!1:Hl("Opera")}function Kl(){return Hl("Firefox")||Hl("FxiOS")}function Ll(){return Il()?Gl("Chromium"):(Hl("Chrome")||Hl("CriOS"))&&!(Il()?0:Hl("Edge"))||Hl("Silk")};var Ml=function(a){Ml[" "](a);return a};Ml[" "]=function(){};var Nl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Ol(){return xl?!!El&&!!El.platform:!1}function Pl(){return Hl("iPhone")&&!Hl("iPod")&&!Hl("iPad")}function Ql(){Pl()||Hl("iPad")||Hl("iPod")};Jl();Il()||Hl("Trident")||Hl("MSIE");Hl("Edge");!Hl("Gecko")||Dl().toLowerCase().indexOf("webkit")!=-1&&!Hl("Edge")||Hl("Trident")||Hl("MSIE")||Hl("Edge");Dl().toLowerCase().indexOf("webkit")!=-1&&!Hl("Edge")&&Hl("Mobile");Ol()||Hl("Macintosh");Ol()||Hl("Windows");(Ol()?El.platform==="Linux":Hl("Linux"))||Ol()||Hl("CrOS");Ol()||Hl("Android");Pl();Hl("iPad");Hl("iPod");Ql();Dl().toLowerCase().indexOf("kaios");var Rl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Ml(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Sl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Tl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Ul=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Rl(b.top)?1:2},Vl=function(a){a=a===void 0?document:a;return a.createElement("img")},Wl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Rl(a)&&(b=a);return b};function Xl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Yl(){return Xl("join-ad-interest-group")&&ob(xc.joinAdInterestGroup)}
function Zl(a,b,c){var d=Ta[3]===void 0?1:Ta[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ta[2]===void 0?50:Ta[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Db()-q<(Ta[1]===void 0?6E4:Ta[1])?(ib("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)$l(f[0]);else{if(n)return ib("TAGGING",10),!1}else f.length>=d?$l(f[0]):n&&$l(m[0]);Lc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Db()});return!0}function $l(a){try{a.parentNode.removeChild(a)}catch(b){}};function am(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var bm=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Kl();Pl()||Hl("iPod");Hl("iPad");!Hl("Android")||Ll()||Kl()||Jl()||Hl("Silk");Ll();!Hl("Safari")||Ll()||(Il()?0:Hl("Coast"))||Jl()||(Il()?0:Hl("Edge"))||(Il()?Gl("Microsoft Edge"):Hl("Edg/"))||(Il()?Gl("Opera"):Hl("OPR"))||Kl()||Hl("Silk")||Hl("Android")||Ql();var cm={},dm=null,em=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!dm){dm={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));cm[m]=n;for(var p=0;p<n.length;p++){var q=n[p];dm[q]===void 0&&(dm[q]=p)}}}for(var r=cm[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
z=b[v+1],B=b[v+2],D=r[y>>2],G=r[(y&3)<<4|z>>4],I=r[(z&15)<<2|B>>6],M=r[B&63];t[w++]=""+D+G+I+M}var T=0,ea=u;switch(b.length-v){case 2:T=b[v+1],ea=r[(T&15)<<2]||u;case 1:var Q=b[v];t[w]=""+r[Q>>2]+r[(Q&3)<<4|T>>4]+ea+u}return t.join("")};var fm=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},gm=/#|$/,hm=function(a,b){var c=a.search(gm),d=fm(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Nl(a.slice(d,e!==-1?e:0))},im=/[?&]($|#)/,jm=function(a,b,c){for(var d,e=a.search(gm),f=0,g,h=[];(g=fm(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(im,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function km(a,b,c,d,e,f,g){var h=hm(c,"fmt");if(d){var m=hm(c,"random"),n=hm(c,"label")||"";if(!m)return!1;var p=em(Nl(n)+":"+Nl(m));if(!am(a,p,d))return!1}h&&Number(h)!==4&&(c=jm(c,"rfmt",h));var q=jm(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||lm(g);Jc(q,function(){g==null||mm(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||mm(g);e==null||e()},f,r||void 0);return!0};var nm={},om=(nm[1]={},nm[2]={},nm[3]={},nm[4]={},nm);function pm(a,b,c){var d=qm(b,c);if(d){var e=om[b][d];e||(e=om[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function rm(a,b){var c=qm(a,b);if(c){var d=om[a][c];d&&(om[a][c]=d.filter(function(e){return!e.wm}))}}function sm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function qm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function tm(a){var b=Ca.apply(1,arguments);ul&&(pm(a,2,b[0]),pm(a,3,b[0]));Vc.apply(null,ya(b))}function um(a){var b=Ca.apply(1,arguments);ul&&pm(a,2,b[0]);return Wc.apply(null,ya(b))}function vm(a){var b=Ca.apply(1,arguments);ul&&pm(a,3,b[0]);Mc.apply(null,ya(b))}
function wm(a){var b=Ca.apply(1,arguments),c=b[0];ul&&(pm(a,2,c),pm(a,3,c));return Yc.apply(null,ya(b))}function xm(a){var b=Ca.apply(1,arguments);ul&&pm(a,1,b[0]);Jc.apply(null,ya(b))}function ym(a){var b=Ca.apply(1,arguments);b[0]&&ul&&pm(a,4,b[0]);Lc.apply(null,ya(b))}function zm(a){var b=Ca.apply(1,arguments);ul&&pm(a,1,b[2]);return km.apply(null,ya(b))}function Am(a){var b=Ca.apply(1,arguments);ul&&pm(a,4,b[0]);Zl.apply(null,ya(b))};var Bm=/gtag[.\/]js/,Cm=/gtm[.\/]js/,Dm=!1;function Em(a){if(Dm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(Bm.test(c))return"3";if(Cm.test(c))return"2"}return"0"};function Fm(a,b,c){var d=Gm(),e=Hm().container[a];e&&e.state!==3||(Hm().container[a]={state:1,context:b,parent:d},Im({ctid:a,isDestination:!1},c))}function Im(a,b){var c=Hm();c.pending||(c.pending=[]);sb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Jm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Km=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Jm()};function Hm(){var a=Bc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Km,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Jm());return c};var Lm={},ng={ctid:Oi(5,"G-9XY5DSYD8S"),canonicalContainerId:Oi(6,"104972449"),lm:Oi(10,"G-9XY5DSYD8S|GT-K825BWS"),om:Oi(9,"G-9XY5DSYD8S")};Lm.qe=Ni(7,zb(""));function Mm(){return Lm.qe&&Nm().some(function(a){return a===ng.ctid})}function Om(){return ng.canonicalContainerId||"_"+ng.ctid}function Pm(){return ng.lm?ng.lm.split("|"):[ng.ctid]}
function Nm(){return ng.om?ng.om.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Qm(){var a=Rm(Gm()),b=a&&a.parent;if(b)return Rm(b)}function Sm(){var a=Rm(Gm());if(a){for(;a.parent;){var b=Rm(a.parent);if(!b)break;a=b}return a}}function Rm(a){var b=Hm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Tm(){var a=Hm();if(a.pending){for(var b,c=[],d=!1,e=Pm(),f=Nm(),g={},h=0;h<a.pending.length;g={lg:void 0},h++)g.lg=a.pending[h],sb(g.lg.target.isDestination?f:e,function(m){return function(n){return n===m.lg.target.ctid}}(g))?d||(b=g.lg.onLoad,d=!0):c.push(g.lg);a.pending=c;if(b)try{b(Om())}catch(m){}}}
function Um(){for(var a=ng.ctid,b=Pm(),c=Nm(),d=function(n,p){var q={canonicalContainerId:ng.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};zc&&(q.scriptElement=zc);Ac&&(q.scriptSource=Ac);if(Qm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Zj.C,y=el(v),z=w?y.pathname:""+y.hostname+y.pathname,B=A.scripts,D="",G=0;G<B.length;++G){var I=B[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(z)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}D=String(G)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){Dm=!0;r=M;break a}}var T=[].slice.call(A.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Em(q)}var ea=p?e.destination:e.container,Q=ea[n];Q?(p&&Q.state===0&&N(93),ma(Object,"assign").call(Object,Q,q)):ea[n]=q},e=Hm(),f=l(b),
g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Om()]={};Tm()}function Vm(){var a=Om();return!!Hm().canonical[a]}function Wm(a){return!!Hm().container[a]}function Xm(a){var b=Hm().destination[a];return!!b&&!!b.state}function Gm(){return{ctid:ng.ctid,isDestination:Lm.qe}}function Ym(){var a=Hm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Zm(){var a={};wb(Hm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function $m(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function an(){for(var a=Hm(),b=l(Pm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var bn={Ja:{me:0,pe:1,Ei:2}};bn.Ja[bn.Ja.me]="FULL_TRANSMISSION";bn.Ja[bn.Ja.pe]="LIMITED_TRANSMISSION";bn.Ja[bn.Ja.Ei]="NO_TRANSMISSION";var cn={X:{Gb:0,Ga:1,Cc:2,Lc:3}};cn.X[cn.X.Gb]="NO_QUEUE";cn.X[cn.X.Ga]="ADS";cn.X[cn.X.Cc]="ANALYTICS";cn.X[cn.X.Lc]="MONITORING";function dn(){var a=Bc("google_tag_data",{});return a.ics=a.ics||new en}var en=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
en.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;ib("TAGGING",19);b==null?ib("TAGGING",18):fn(this,a,b==="granted",c,d,e,f,g)};en.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)fn(this,a[d],void 0,void 0,"","",b,c)};
var fn=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&pb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(ib("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=en.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())gn(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())gn(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&pb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Fd:b})};var gn=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.qm=!0)}};en.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.qm){d.qm=!1;try{d.Fd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var hn=!1,jn=!1,kn={},ln={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(kn.ad_storage=1,kn.analytics_storage=1,kn.ad_user_data=1,kn.ad_personalization=1,kn),usedContainerScopedDefaults:!1};function mn(a){var b=dn();b.accessedAny=!0;return(pb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,ln)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function nn(a){var b=dn();b.accessedAny=!0;return b.getConsentState(a,ln)}function on(a){var b=dn();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function pn(){if(!Ua(7))return!1;var a=dn();a.accessedAny=!0;if(a.active)return!0;if(!ln.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(ln.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(ln.containerScopedDefaults[c.value]!==1)return!0;return!1}function qn(a,b){dn().addListener(a,b)}
function rn(a,b){dn().notifyListeners(a,b)}function sn(a,b){function c(){for(var e=0;e<b.length;e++)if(!on(b[e]))return!0;return!1}if(c()){var d=!1;qn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function tn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];mn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=pb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),qn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var un={},vn=(un[cn.X.Gb]=bn.Ja.me,un[cn.X.Ga]=bn.Ja.me,un[cn.X.Cc]=bn.Ja.me,un[cn.X.Lc]=bn.Ja.me,un),wn=function(a,b){this.C=a;this.consentTypes=b};wn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return mn(a)});case 1:return this.consentTypes.some(function(a){return mn(a)});default:pc(this.C,"consentsRequired had an unknown type")}};
var xn={},yn=(xn[cn.X.Gb]=new wn(0,[]),xn[cn.X.Ga]=new wn(0,["ad_storage"]),xn[cn.X.Cc]=new wn(0,["analytics_storage"]),xn[cn.X.Lc]=new wn(1,["ad_storage","analytics_storage"]),xn);var An=function(a){var b=this;this.type=a;this.C=[];qn(yn[a].consentTypes,function(){zn(b)||b.flush()})};An.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var zn=function(a){return vn[a.type]===bn.Ja.Ei&&!yn[a.type].isConsentGranted()},Bn=function(a,b){zn(a)?a.C.push(b):b()},Cn=new Map;function Dn(a){Cn.has(a)||Cn.set(a,new An(a));return Cn.get(a)};var En={Z:{Im:"aw_user_data_cache",Mh:"cookie_deprecation_label",yg:"diagnostics_page_id",Rn:"fl_user_data_cache",Tn:"ga4_user_data_cache",Ff:"ip_geo_data_cache",zi:"ip_geo_fetch_in_progress",ol:"nb_data",ql:"page_experiment_ids",Pf:"pt_data",rl:"pt_listener_set",wl:"service_worker_endpoint",yl:"shared_user_id",zl:"shared_user_id_requested",kh:"shared_user_id_source"}};var Fn=function(a){return df(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(En.Z);
function Gn(a,b){b=b===void 0?!1:b;if(Fn(a)){var c,d,e=(d=(c=Bc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Hn(a,b){var c=Gn(a,!0);c&&c.set(b)}function In(a){var b;return(b=Gn(a))==null?void 0:b.get()}function Jn(a){var b={},c=Gn(a);if(!c){c=Gn(a,!0);if(!c)return;c.set(b)}return c.get()}function Kn(a,b){if(typeof b==="function"){var c;return(c=Gn(a,!0))==null?void 0:c.subscribe(b)}}function Ln(a,b){var c=Gn(a);return c?c.unsubscribe(b):!1};var Mn="https://"+Oi(21,"www.googletagmanager.com"),Nn="/td?id="+ng.ctid,On={},Pn=(On.tdp=1,On.exp=1,On.pid=1,On.dl=1,On.seq=1,On.t=1,On.v=1,On),Qn=["mcc"],Rn={},Sn={},Tn=!1;function Un(a,b,c){Sn[a]=b;(c===void 0||c)&&Vn(a)}function Vn(a,b){Rn[a]!==void 0&&(b===void 0||!b)||Jb(ng.ctid,"GTM-")&&a==="mcc"||(Rn[a]=!0)}
function Wn(a){a=a===void 0?!1:a;var b=Object.keys(Rn).filter(function(c){return Rn[c]===!0&&Sn[c]!==void 0&&(a||!Qn.includes(c))}).map(function(c){var d=Sn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+nl(Mn)+Nn+(""+b+"&z=0")}function Xn(){Object.keys(Rn).forEach(function(a){Pn[a]||(Rn[a]=!1)})}
function Yn(a){a=a===void 0?!1:a;if(Zj.ia&&ul&&ng.ctid){var b=Dn(cn.X.Lc);if(zn(b))Tn||(Tn=!0,Bn(b,Yn));else{var c=Wn(a),d={destinationId:ng.ctid,endpoint:61};a?wm(d,c,void 0,{Eh:!0},void 0,function(){vm(d,c+"&img=1")}):vm(d,c);Xn();Tn=!1}}}function Zn(){Object.keys(Rn).filter(function(a){return Rn[a]&&!Pn[a]}).length>0&&Yn(!0)}var $n;function ao(){if(In(En.Z.yg)===void 0){var a=function(){Hn(En.Z.yg,tb());$n=0};a();x.setInterval(a,864E5)}else Kn(En.Z.yg,function(){$n=0});$n=0}
function bo(){ao();Un("v","3");Un("t","t");Un("pid",function(){return String(In(En.Z.yg))});Un("seq",function(){return String(++$n)});Un("exp",wk());Oc(x,"pagehide",Zn)};var co=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],eo=[K.m.rd,K.m.sc,K.m.de,K.m.Rb,K.m.Wb,K.m.Ma,K.m.Wa,K.m.nb,K.m.yb,K.m.Sb],fo=!1,go=!1,ho={},io={};function jo(){!go&&fo&&(co.some(function(a){return ln.containerScopedDefaults[a]!==1})||ko("mbc"));go=!0}function ko(a){ul&&(Un(a,"1"),Yn())}function lo(a,b){if(!ho[b]&&(ho[b]=!0,io[b]))for(var c=l(eo),d=c.next();!d.done;d=c.next())if(O(a,d.value)){ko("erc");break}};function mo(a){ib("HEALTH",a)};var no={kp:Oi(22,"eyIwIjoiVk4iLCIxIjoiVk4tU0ciLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udm4iLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ")},oo={},po=!1;function qo(){function a(){c!==void 0&&Ln(En.Z.Ff,c);try{var e=In(En.Z.Ff);oo=JSON.parse(e)}catch(f){N(123),mo(2),oo={}}po=!0;b()}var b=ro,c=void 0,d=In(En.Z.Ff);d?a(d):(c=Kn(En.Z.Ff,a),so())}
function so(){function a(c){Hn(En.Z.Ff,c||"{}");Hn(En.Z.zi,!1)}if(!In(En.Z.zi)){Hn(En.Z.zi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function to(){var a=no.kp;try{return JSON.parse(gb(a))}catch(b){return N(123),mo(2),{}}}function uo(){return oo["0"]||""}function vo(){return oo["1"]||""}function wo(){var a=!1;a=!!oo["2"];return a}function xo(){return oo["6"]!==!1}function yo(){var a="";a=oo["4"]||"";return a}
function zo(){var a=!1;a=!!oo["5"];return a}function Ao(){var a="";a=oo["3"]||"";return a};var Bo={},Co=Object.freeze((Bo[K.m.Ha]=1,Bo[K.m.Ag]=1,Bo[K.m.Bg]=1,Bo[K.m.Pb]=1,Bo[K.m.xa]=1,Bo[K.m.yb]=1,Bo[K.m.zb]=1,Bo[K.m.Db]=1,Bo[K.m.bd]=1,Bo[K.m.Sb]=1,Bo[K.m.nb]=1,Bo[K.m.Ec]=1,Bo[K.m.bf]=1,Bo[K.m.na]=1,Bo[K.m.ik]=1,Bo[K.m.ef]=1,Bo[K.m.Kg]=1,Bo[K.m.Lg]=1,Bo[K.m.de]=1,Bo[K.m.zk]=1,Bo[K.m.rc]=1,Bo[K.m.he]=1,Bo[K.m.Bk]=1,Bo[K.m.Og]=1,Bo[K.m.ei]=1,Bo[K.m.Hc]=1,Bo[K.m.Ic]=1,Bo[K.m.Wa]=1,Bo[K.m.fi]=1,Bo[K.m.Vb]=1,Bo[K.m.ob]=1,Bo[K.m.pd]=1,Bo[K.m.rd]=1,Bo[K.m.rf]=1,Bo[K.m.hi]=1,Bo[K.m.je]=1,Bo[K.m.sc]=
1,Bo[K.m.ud]=1,Bo[K.m.Vg]=1,Bo[K.m.Xb]=1,Bo[K.m.wd]=1,Bo[K.m.Hi]=1,Bo));Object.freeze([K.m.Ca,K.m.Xa,K.m.Eb,K.m.Ab,K.m.gi,K.m.Ma,K.m.ai,K.m.un]);
var Do={},Eo=Object.freeze((Do[K.m.Vm]=1,Do[K.m.Wm]=1,Do[K.m.Xm]=1,Do[K.m.Ym]=1,Do[K.m.Zm]=1,Do[K.m.fn]=1,Do[K.m.gn]=1,Do[K.m.hn]=1,Do[K.m.kn]=1,Do[K.m.Xd]=1,Do)),Fo={},Go=Object.freeze((Fo[K.m.Xj]=1,Fo[K.m.Yj]=1,Fo[K.m.Td]=1,Fo[K.m.Ud]=1,Fo[K.m.Zj]=1,Fo[K.m.Vc]=1,Fo[K.m.Vd]=1,Fo[K.m.kc]=1,Fo[K.m.Dc]=1,Fo[K.m.mc]=1,Fo[K.m.wb]=1,Fo[K.m.Wd]=1,Fo[K.m.Nb]=1,Fo[K.m.bk]=1,Fo)),Ho=Object.freeze([K.m.Ha,K.m.Re,K.m.Pb,K.m.Ec,K.m.de,K.m.lf,K.m.ob,K.m.ud]),Io=Object.freeze([].concat(ya(Ho))),Jo=Object.freeze([K.m.zb,
K.m.Lg,K.m.rf,K.m.hi,K.m.Ig]),Ko=Object.freeze([].concat(ya(Jo))),Lo={},Mo=(Lo[K.m.U]="1",Lo[K.m.ja]="2",Lo[K.m.V]="3",Lo[K.m.La]="4",Lo),No={},Oo=Object.freeze((No.search="s",No.youtube="y",No.playstore="p",No.shopping="h",No.ads="a",No.maps="m",No));function Po(a){return typeof a!=="object"||a===null?{}:a}function Qo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Ro(a){if(a!==void 0&&a!==null)return Qo(a)}function So(a){return typeof a==="number"?a:Ro(a)};function To(a){return a&&a.indexOf("pending:")===0?Uo(a.substr(8)):!1}function Uo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Db();return b<c+3E5&&b>c-9E5};var Vo=!1,Wo=!1,Xo=!1,Yo=0,Zo=!1,$o=[];function ap(a){if(Yo===0)Zo&&$o&&($o.length>=100&&$o.shift(),$o.push(a));else if(bp()){var b=Oi(41,'google.tagmanager.ta.prodqueue'),c=Bc(b,[]);c.length>=50&&c.shift();c.push(a)}}function cp(){dp();Pc(A,"TAProdDebugSignal",cp)}function dp(){if(!Wo){Wo=!0;ep();var a=$o;$o=void 0;a==null||a.forEach(function(b){ap(b)})}}
function ep(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Uo(a)?Yo=1:!To(a)||Vo||Xo?Yo=2:(Xo=!0,Oc(A,"TAProdDebugSignal",cp,!1),x.setTimeout(function(){dp();Vo=!0},200))}function bp(){if(!Zo)return!1;switch(Yo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var fp=!1;function gp(a,b){var c=Pm(),d=Nm();if(bp()){var e=hp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;ap(e)}}
function ip(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Na;e=a.isBatched;var f;if(f=bp()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=hp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);ap(h)}}function jp(a){bp()&&ip(a())}
function hp(a,b){b=b===void 0?{}:b;b.groupId=kp;var c,d=b,e={publicId:lp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=fp?"OGT":"GTM";c.key.targetRef=mp;return c}var lp="",mp={ctid:"",isDestination:!1},kp;
function np(a){var b=ng.ctid,c=Mm();Yo=0;Zo=!0;ep();kp=a;lp=b;fp=nk;mp={ctid:b,isDestination:c}};var op=[K.m.U,K.m.ja,K.m.V,K.m.La],pp,qp;function rp(a){var b=a[K.m.hc];b||(b=[""]);for(var c={dg:0};c.dg<b.length;c={dg:c.dg},++c.dg)wb(a,function(d){return function(e,f){if(e!==K.m.hc){var g=Qo(f),h=b[d.dg],m=uo(),n=vo();jn=!0;hn&&ib("TAGGING",20);dn().declare(e,g,h,m,n)}}}(c))}
function sp(a){jo();!qp&&pp&&ko("crc");qp=!0;var b=a[K.m.sg];b&&N(41);var c=a[K.m.hc];c?N(40):c=[""];for(var d={eg:0};d.eg<c.length;d={eg:d.eg},++d.eg)wb(a,function(e){return function(f,g){if(f!==K.m.hc&&f!==K.m.sg){var h=Ro(g),m=c[e.eg],n=Number(b),p=uo(),q=vo();n=n===void 0?0:n;hn=!0;jn&&ib("TAGGING",20);dn().default(f,h,m,p,q,n,ln)}}}(d))}
function tp(a){ln.usedContainerScopedDefaults=!0;var b=a[K.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(vo())&&!c.includes(uo()))return}wb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}ln.usedContainerScopedDefaults=!0;ln.containerScopedDefaults[d]=e==="granted"?3:2})}
function up(a,b){jo();pp=!0;wb(a,function(c,d){var e=Qo(d);hn=!0;jn&&ib("TAGGING",20);dn().update(c,e,ln)});rn(b.eventId,b.priorityId)}function vp(a){a.hasOwnProperty("all")&&(ln.selectedAllCorePlatformServices=!0,wb(Oo,function(b){ln.corePlatformServices[b]=a.all==="granted";ln.usedCorePlatformServices=!0}));wb(a,function(b,c){b!=="all"&&(ln.corePlatformServices[b]=c==="granted",ln.usedCorePlatformServices=!0)})}function wp(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return mn(b)})}
function xp(a,b){qn(a,b)}function yp(a,b){tn(a,b)}function zp(a,b){sn(a,b)}function Ap(){var a=[K.m.U,K.m.La,K.m.V];dn().waitForUpdate(a,500,ln)}function Bp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;dn().clearTimeout(d,void 0,ln)}rn()}function Cp(){if(!ok)for(var a=xo()?zk(Zj.Sa):zk(Zj.pb),b=0;b<op.length;b++){var c=op[b],d=c,e=a[c]?"granted":"denied";dn().implicit(d,e)}};var Dp=!1;F(218)&&(Dp=Ni(49,Dp));var Ep=!1,Fp=[];function Gp(){if(!Ep){Ep=!0;for(var a=Fp.length-1;a>=0;a--)Fp[a]();Fp=[]}};var Hp=x.google_tag_manager=x.google_tag_manager||{};function Ip(a,b){return Hp[a]=Hp[a]||b()}function Jp(){var a=ng.ctid,b=Kp;Hp[a]=Hp[a]||b}function Lp(){var a=Hp.sequence||1;Hp.sequence=a+1;return a}x.google_tag_data=x.google_tag_data||{};function Mp(){if(Hp.pscdl!==void 0)In(En.Z.Mh)===void 0&&Hn(En.Z.Mh,Hp.pscdl);else{var a=function(c){Hp.pscdl=c;Hn(En.Z.Mh,c)},b=function(){a("error")};try{xc.cookieDeprecationLabel?(a("pending"),xc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Np=0;function Op(a){ul&&a===void 0&&Np===0&&(Un("mcc","1"),Np=1)};var Pp={Df:{Om:"cd",Pm:"ce",Qm:"cf",Rm:"cpf",Sm:"cu"}};var Qp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Rp=/\s/;
function Sp(a,b){if(pb(a)){a=Bb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Qp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Rp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Tp(a,b){for(var c={},d=0;d<a.length;++d){var e=Sp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Up[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Vp={},Up=(Vp[0]=0,Vp[1]=1,Vp[2]=2,Vp[3]=0,Vp[4]=1,Vp[5]=0,Vp[6]=0,Vp[7]=0,Vp);var Wp=Number(Ri(34,''))||500,Xp={},Yp={},Zp={initialized:11,complete:12,interactive:13},$p={},aq=Object.freeze(($p[K.m.ob]=!0,$p)),bq=void 0;function cq(a,b){if(b.length&&ul){var c;(c=Xp)[a]!=null||(c[a]=[]);Yp[a]!=null||(Yp[a]=[]);var d=b.filter(function(e){return!Yp[a].includes(e)});Xp[a].push.apply(Xp[a],ya(d));Yp[a].push.apply(Yp[a],ya(d));!bq&&d.length>0&&(Vn("tdc",!0),bq=x.setTimeout(function(){Yn();Xp={};bq=void 0},Wp))}}
function dq(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function eq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;md(t)==="object"?u=t[r]:md(t)==="array"&&(u=t[r]);return u===void 0?aq[r]:u},f=dq(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=md(m)==="object"||md(m)==="array",q=md(n)==="object"||md(n)==="array";if(p&&q)eq(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function fq(){Un("tdc",function(){bq&&(x.clearTimeout(bq),bq=void 0);var a=[],b;for(b in Xp)Xp.hasOwnProperty(b)&&a.push(b+"*"+Xp[b].join("."));return a.length?a.join("!"):void 0},!1)};var gq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},hq=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(hq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},iq=function(a){for(var b={},c=hq(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
gq.prototype.getMergedValues=function(a,b,c){function d(n){od(n)&&wb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=hq(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var jq=function(a){for(var b=[K.m.We,K.m.Se,K.m.Te,K.m.Ue,K.m.Ve,K.m.Xe,K.m.Ye],c=hq(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},kq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ia={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},lq=function(a,
b){a.H=b;return a},mq=function(a,b){a.R=b;return a},nq=function(a,b){a.C=b;return a},oq=function(a,b){a.N=b;return a},pq=function(a,b){a.ia=b;return a},qq=function(a,b){a.P=b;return a},rq=function(a,b){a.eventMetadata=b||{};return a},sq=function(a,b){a.onSuccess=b;return a},tq=function(a,b){a.onFailure=b;return a},uq=function(a,b){a.isGtmEvent=b;return a},vq=function(a){return new gq(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={A:{Gj:"accept_by_default",rg:"add_tag_timing",Ih:"allow_ad_personalization",Ij:"batch_on_navigation",Kj:"client_id_source",Ie:"consent_event_id",Je:"consent_priority_id",Kq:"consent_state",fa:"consent_updated",Uc:"conversion_linker_enabled",wa:"cookie_options",ug:"create_dc_join",vg:"create_fpm_geo_join",wg:"create_fpm_signals_join",Sd:"create_google_join",Le:"em_event",Nq:"endpoint_for_debug",Wj:"enhanced_client_id_source",Oh:"enhanced_match_result",ke:"euid_mode_enabled",eb:"event_start_timestamp_ms",
Vk:"event_usage",Xg:"extra_tag_experiment_ids",Uq:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",Yg:"send_as_iframe",Vq:"parameter_order",Zg:"parsed_target",Sn:"ga4_collection_subdomain",Yk:"gbraid_cookie_marked",ba:"hit_type",xd:"hit_type_override",Xn:"is_config_command",Gf:"is_consent_update",Hf:"is_conversion",fl:"is_ecommerce",yd:"is_external_event",Ai:"is_fallback_aw_conversion_ping_allowed",If:"is_first_visit",il:"is_first_visit_conversion",ah:"is_fl_fallback_conversion_flow_allowed",
Jf:"is_fpm_encryption",bh:"is_fpm_split",ne:"is_gcp_conversion",jl:"is_google_signals_allowed",zd:"is_merchant_center",eh:"is_new_to_site",fh:"is_server_side_destination",oe:"is_session_start",ml:"is_session_start_conversion",Yq:"is_sgtm_ga_ads_conversion_study_control_group",Zq:"is_sgtm_prehit",nl:"is_sgtm_service_worker",Bi:"is_split_conversion",Yn:"is_syn",Kf:"join_id",Ci:"join_elapsed",Lf:"join_timer_sec",se:"tunnel_updated",hr:"prehit_for_retry",jr:"promises",kr:"record_aw_latency",wc:"redact_ads_data",
te:"redact_click_ids",jo:"remarketing_only",tl:"send_ccm_parallel_ping",jh:"send_fledge_experiment",mr:"send_ccm_parallel_test_ping",Qf:"send_to_destinations",Gi:"send_to_targets",vl:"send_user_data_hit",fb:"source_canonical_id",za:"speculative",Al:"speculative_in_message",Bl:"suppress_script_load",Cl:"syn_or_mod",Gl:"transient_ecsid",Rf:"transmission_type",hb:"user_data",rr:"user_data_from_automatic",ur:"user_data_from_automatic_getter",ve:"user_data_from_code",nh:"user_data_from_manual",Il:"user_data_mode",
Sf:"user_id_updated"}};var wq={Hm:Number(Ri(3,'5')),Lr:Number(Ri(33,""))},xq=[],yq=!1;function zq(a){xq.push(a)}var Aq="?id="+ng.ctid,Bq=void 0,Cq={},Dq=void 0,Eq=new function(){var a=5;wq.Hm>0&&(a=wq.Hm);this.H=a;this.C=0;this.N=[]},Fq=1E3;
function Gq(a,b){var c=Bq;if(c===void 0)if(b)c=Lp();else return"";for(var d=[nl("https://www.googletagmanager.com"),"/a",Aq],e=l(xq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Rd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Hq(){if(Zj.ia&&(Dq&&(x.clearTimeout(Dq),Dq=void 0),Bq!==void 0&&Iq)){var a=Dn(cn.X.Lc);if(zn(a))yq||(yq=!0,Bn(a,Hq));else{var b;if(!(b=Cq[Bq])){var c=Eq;b=c.C<c.H?!1:Db()-c.N[c.C%c.H]<1E3}if(b||Fq--<=0)N(1),Cq[Bq]=!0;else{var d=Eq,e=d.C++%d.H;d.N[e]=Db();var f=Gq(!0);vm({destinationId:ng.ctid,endpoint:56,eventId:Bq},f);yq=Iq=!1}}}}function Jq(){if(tl&&Zj.ia){var a=Gq(!0,!0);vm({destinationId:ng.ctid,endpoint:56,eventId:Bq},a)}}var Iq=!1;
function Kq(a){Cq[a]||(a!==Bq&&(Hq(),Bq=a),Iq=!0,Dq||(Dq=x.setTimeout(Hq,500)),Gq().length>=2022&&Hq())}var Lq=tb();function Mq(){Lq=tb()}function Nq(){return[["v","3"],["t","t"],["pid",String(Lq)]]};var Oq={};function Pq(a,b,c){tl&&a!==void 0&&(Oq[a]=Oq[a]||[],Oq[a].push(c+b),Kq(a))}function Qq(a){var b=a.eventId,c=a.Rd,d=[],e=Oq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Oq[b];return d};function Rq(a,b,c,d){var e=Sp(a,!0);e&&Sq.register(e,b,c,d)}function Tq(a,b,c,d){var e=Sp(c,d.isGtmEvent);e&&(lk&&(d.deferrable=!0),Sq.push("event",[b,a],e,d))}function Uq(a,b,c,d){var e=Sp(c,d.isGtmEvent);e&&Sq.push("get",[a,b],e,d)}function Vq(a){var b=Sp(a,!0),c;b?c=Wq(Sq,b).C:c={};return c}function Yq(a,b){var c=Sp(a,!0);c&&Zq(Sq,c,b)}
var $q=function(){this.R={};this.C={};this.H={};this.ia=null;this.P={};this.N=!1;this.status=1},ar=function(a,b,c,d){this.H=Db();this.C=b;this.args=c;this.messageContext=d;this.type=a},br=function(){this.destinations={};this.C={};this.commands=[]},Wq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new $q},cr=function(a,b,c,d){if(d.C){var e=Wq(a,d.C),f=e.ia;if(f){var g=pd(c,null),h=pd(e.R[d.C.id],null),m=pd(e.P,null),n=pd(e.C,null),p=pd(a.C,null),q={};if(tl)try{q=
pd(Bk,null)}catch(w){N(72)}var r=d.C.prefix,t=function(w){Pq(d.messageContext.eventId,r,w)},u=vq(uq(tq(sq(rq(pq(oq(qq(nq(mq(lq(new kq(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Pq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(ul&&w==="config"){var z,B=(z=Sp(y))==null?void 0:z.ids;if(!(B&&B.length>1)){var D,G=Bc("google_tag_data",{});G.td||(G.td={});D=G.td;var I=pd(u.P);pd(u.C,I);var M=[],T;for(T in D)D.hasOwnProperty(T)&&eq(D[T],I).length&&M.push(T);M.length&&(cq(y,M),ib("TAGGING",Zp[A.readyState]||14));D[y]=I}}f(d.C.id,b,d.H,u)}catch(ea){Pq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():Bn(e.la,v)}}};
br.prototype.register=function(a,b,c,d){var e=Wq(this,a);e.status!==3&&(e.ia=b,e.status=3,e.la=Dn(c),Zq(this,a,d||{}),this.flush())};
br.prototype.push=function(a,b,c,d){c!==void 0&&(Wq(this,c).status===1&&(Wq(this,c).status=2,this.push("require",[{}],c,{})),Wq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.A.Qf]||(d.eventMetadata[P.A.Qf]=[c.destinationId]),d.eventMetadata[P.A.Gi]||(d.eventMetadata[P.A.Gi]=[c.id]));this.commands.push(new ar(a,c,b,d));d.deferrable||this.flush()};
br.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Nc:void 0,sh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Wq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Wq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];wb(h,function(t,u){pd(Lb(t,u),b.C)});Xj(h,!0);break;case "config":var m=Wq(this,g);
e.Nc={};wb(f.args[0],function(t){return function(u,v){pd(Lb(u,v),t.Nc)}}(e));var n=!!e.Nc[K.m.ud];delete e.Nc[K.m.ud];var p=g.destinationId===g.id;Xj(e.Nc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||cr(this,K.m.ra,e.Nc,f);m.N=!0;p?pd(e.Nc,m.P):(pd(e.Nc,m.R[g.id]),N(70));d=!0;break;case "event":e.sh={};wb(f.args[0],function(t){return function(u,v){pd(Lb(u,v),t.sh)}}(e));Xj(e.sh);cr(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[K.m.Fc]=f.args[0],q[K.m.jd]=f.args[1],q);cr(this,K.m.Ob,r,f)}this.commands.shift();
dr(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var dr=function(a,b){if(b.type!=="require")if(b.C)for(var c=Wq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Zq=function(a,b,c){var d=pd(c,null);pd(Wq(a,b).C,d);Wq(a,b).C=d},Sq=new br;function er(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function fr(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function gr(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Vl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=uc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}fr(e,"load",f);fr(e,"error",f)};er(e,"load",f);er(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function hr(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Sl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});ir(c,b)}
function ir(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else gr(c,a,b===void 0?!1:b,d===void 0?!1:d)};var jr=function(){this.ia=this.ia;this.P=this.P};jr.prototype.ia=!1;jr.prototype.dispose=function(){this.ia||(this.ia=!0,this.N())};jr.prototype[ia.Symbol.dispose]=function(){this.dispose()};jr.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};jr.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function kr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var lr=function(a,b){b=b===void 0?{}:b;jr.call(this);this.C=null;this.la={};this.pb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ea=(d=b.Ar)!=null?d:!1};va(lr,jr);lr.prototype.N=function(){this.la={};this.R&&(fr(this.H,"message",this.R),delete this.R);delete this.la;delete this.H;delete this.C;jr.prototype.N.call(this)};var nr=function(a){return typeof a.H.__tcfapi==="function"||mr(a)!=null};
lr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ea},d=wl(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=kr(c),c.internalBlockOnErrors=b.Ea,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{or(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};lr.prototype.removeEventListener=function(a){a&&a.listenerId&&or(this,"removeEventListener",null,a.listenerId)};
var qr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=pr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&pr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?pr(a.purpose.legitimateInterests,
b)&&pr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},pr=function(a,b){return!(!a||!a[b])},or=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(mr(a)){rr(a);var g=++a.pb;a.la[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},mr=function(a){if(a.C)return a.C;a.C=Tl(a.H,"__tcfapiLocator");return a.C},rr=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;er(a.H,"message",b)}},sr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=kr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(hr({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var tr={1:0,3:0,4:0,7:3,9:3,10:3};Ri(32,'');function ur(){return Ip("tcf",function(){return{}})}var vr=function(){return new lr(x,{timeoutMs:-1})};
function wr(){var a=ur(),b=vr();nr(b)&&!xr()&&!yr()&&N(124);if(!a.active&&nr(b)){xr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,dn().active=!0,a.tcString="tcunavailable");Ap();try{b.addEventListener(function(c){if(c.internalErrorState!==0)zr(a),Bp([K.m.U,K.m.La,K.m.V]),dn().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,yr()&&(a.active=!0),!Ar(c)||xr()||yr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in tr)tr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(Ar(c)){var g={},h;for(h in tr)if(tr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={jp:!0};p=p===void 0?{}:p;m=sr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.jp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?qr(n,"1",0):!0:!1;g["1"]=m}else g[h]=qr(c,h,tr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Bp([K.m.U,K.m.La,K.m.V]),dn().active=!0):(r[K.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Bp([K.m.V]),up(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:Br()||""}))}}else Bp([K.m.U,K.m.La,K.m.V])})}catch(c){zr(a),Bp([K.m.U,K.m.La,K.m.V]),dn().active=!0}}}
function zr(a){a.type="e";a.tcString="tcunavailable"}function Ar(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function xr(){return x.gtag_enable_tcf_support===!0}function yr(){return ur().enableAdvertiserConsentMode===!0}function Br(){var a=ur();if(a.active)return a.tcString}function Cr(){var a=ur();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Dr(a){if(!tr.hasOwnProperty(String(a)))return!0;var b=ur();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Er=[K.m.U,K.m.ja,K.m.V,K.m.La],Fr={},Gr=(Fr[K.m.U]=1,Fr[K.m.ja]=2,Fr);function Hr(a){if(a===void 0)return 0;switch(O(a,K.m.Ha)){case void 0:return 1;case !1:return 3;default:return 2}}function Ir(){return(F(183)?jj.pp:jj.qp).indexOf(vo())!==-1&&xc.globalPrivacyControl===!0}function Jr(a){if(Ir())return!1;var b=Hr(a);if(b===3)return!1;switch(nn(K.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Kr(){return pn()||!mn(K.m.U)||!mn(K.m.ja)}function Lr(){var a={},b;for(b in Gr)Gr.hasOwnProperty(b)&&(a[Gr[b]]=nn(b));return"G1"+gf(a[1]||0)+gf(a[2]||0)}var Mr={},Nr=(Mr[K.m.U]=0,Mr[K.m.ja]=1,Mr[K.m.V]=2,Mr[K.m.La]=3,Mr);function Or(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Pr(a){for(var b="1",c=0;c<Er.length;c++){var d=b,e,f=Er[c],g=ln.delegatedConsentTypes[f];e=g===void 0?0:Nr.hasOwnProperty(g)?12|Nr[g]:8;var h=dn();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Or(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Or(m.declare)<<4|Or(m.default)<<2|Or(m.update)])}var n=b,p=(Ir()?1:0)<<3,q=(pn()?1:0)<<2,r=Hr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[ln.containerScopedDefaults.ad_storage<<4|ln.containerScopedDefaults.analytics_storage<<2|ln.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(ln.usedContainerScopedDefaults?1:0)<<2|ln.containerScopedDefaults.ad_personalization]}
function Qr(){if(!mn(K.m.V))return"-";for(var a=Object.keys(Oo),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=ln.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Oo[m])}(ln.usedCorePlatformServices?ln.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Rr(){return xo()||(xr()||yr())&&Cr()==="1"?"1":"0"}function Sr(){return(xo()?!0:!(!xr()&&!yr())&&Cr()==="1")||!mn(K.m.V)}
function Tr(){var a="0",b="0",c;var d=ur();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=ur();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;xo()&&(h|=1);Cr()==="1"&&(h|=2);xr()&&(h|=4);var m;var n=ur();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);dn().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Ur(){return vo()==="US-CO"};var Vr;function Wr(){if(Ac===null)return 0;var a=dd();if(!a)return 0;var b=a.getEntriesByName(Ac,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Xr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Yr(a){a=a===void 0?{}:a;var b=ng.ctid.split("-")[0].toUpperCase(),c={ctid:ng.ctid,wj:fk,Aj:ek,Zl:Lm.qe?2:1,xq:a.zm,we:ng.canonicalContainerId};if(F(210)){var d;c.mq=(d=Sm())==null?void 0:d.canonicalContainerId}if(F(204)){var e;c.Io=(e=Vr)!=null?e:Vr=Wr()}c.we!==a.Oa&&(c.Oa=a.Oa);var f=Qm();c.jm=f?f.canonicalContainerId:void 0;nk?(c.Sc=Xr[b],c.Sc||(c.Sc=0)):c.Sc=ok?13:10;Zj.C?(c.Bh=0,c.Nl=2):c.Bh=Zj.N?1:3;var g={6:!1};Zj.H===2?g[7]=!0:Zj.H===1&&(g[2]=!0);if(Ac){var h=Zk(el(Ac),"host");h&&
(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Pl=g;return kf(c,a.ph)}
function Zr(){if(!F(192))return Yr();if(F(193))return kf({wj:fk,Aj:ek});var a=ng.ctid.split("-")[0].toUpperCase(),b={ctid:ng.ctid,wj:fk,Aj:ek,Zl:Lm.qe?2:1,we:ng.canonicalContainerId},c=Qm();b.jm=c?c.canonicalContainerId:void 0;nk?(b.Sc=Xr[a],b.Sc||(b.Sc=0)):b.Sc=ok?13:10;Zj.C?(b.Bh=0,b.Nl=2):b.Bh=Zj.N?1:3;var d={6:!1};Zj.H===2?d[7]=!0:Zj.H===1&&(d[2]=!0);if(Ac){var e=Zk(el(Ac),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Pl=d;return kf(b)};function $r(a,b,c,d){var e,f=Number(a.Qc!=null?a.Qc:void 0);f!==0&&(e=new Date((b||Db())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Bc:d}};var as=["ad_storage","ad_user_data"];function bs(a,b){if(!a)return ib("TAGGING",32),10;if(b===null||b===void 0||b==="")return ib("TAGGING",33),11;var c=cs(!1);if(c.error!==0)return ib("TAGGING",34),c.error;if(!c.value)return ib("TAGGING",35),2;c.value[a]=b;var d=ds(c);d!==0&&ib("TAGGING",36);return d}
function es(a){if(!a)return ib("TAGGING",27),{error:10};var b=cs();if(b.error!==0)return ib("TAGGING",29),b;if(!b.value)return ib("TAGGING",30),{error:2};if(!(a in b.value))return ib("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(ib("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function cs(a){a=a===void 0?!0:a;if(!mn(as))return ib("TAGGING",43),{error:3};try{if(!x.localStorage)return ib("TAGGING",44),{error:1}}catch(f){return ib("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return ib("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return ib("TAGGING",47),{error:12}}}catch(f){return ib("TAGGING",48),{error:8}}if(b.schema!=="gcl")return ib("TAGGING",49),{error:4};
if(b.version!==1)return ib("TAGGING",50),{error:5};try{var e=fs(b);a&&e&&ds({value:b,error:0})}catch(f){return ib("TAGGING",48),{error:8}}return{value:b,error:0}}
function fs(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,ib("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=fs(a[e.value])||c;return c}return!1}
function ds(a){if(a.error)return a.error;if(!a.value)return ib("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return ib("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return ib("TAGGING",53),7}return 0};var gs={mj:"value",qb:"conversionCount"},hs={Yl:9,sm:10,mj:"timeouts",qb:"timeouts"},is=[gs,hs];function js(a){if(!ks(a))return{};var b=ls(is),c=b[a.qb];if(c===void 0||c===-1)return b;var d={},e=ma(Object,"assign").call(Object,{},b,(d[a.qb]=c+1,d));return ms(e)?e:b}
function ls(a){var b;a:{var c=es("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&ks(m)){var n=e[m.mj];n===void 0||Number.isNaN(n)?f[m.qb]=-1:f[m.qb]=Number(n)}else f[m.qb]=-1}return f}
function ns(){var a=js(gs),b=a[gs.qb];if(b===void 0||b<=0)return"";var c=a[hs.qb];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function ms(a,b){b=b||{};for(var c=Db(),d=$r(b,c,!0),e={},f=l(is),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.qb];m!==void 0&&m!==-1&&(e[h.mj]=m)}e.creationTimeMs=c;return bs("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function ks(a){return mn(["ad_storage","ad_user_data"])?!a.sm||Ua(a.sm):!1}
function os(a){return mn(["ad_storage","ad_user_data"])?!a.Yl||Ua(a.Yl):!1};function ps(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var qs={O:{ko:0,Hj:1,tg:2,Nj:3,Kh:4,Lj:5,Mj:6,Oj:7,Lh:8,Tk:9,Sk:10,si:11,Uk:12,Wg:13,Xk:14,Nf:15,io:16,ue:17,Ki:18,Li:19,Mi:20,El:21,Ni:22,Nh:23,Vj:24}};qs.O[qs.O.ko]="RESERVED_ZERO";qs.O[qs.O.Hj]="ADS_CONVERSION_HIT";qs.O[qs.O.tg]="CONTAINER_EXECUTE_START";qs.O[qs.O.Nj]="CONTAINER_SETUP_END";qs.O[qs.O.Kh]="CONTAINER_SETUP_START";qs.O[qs.O.Lj]="CONTAINER_BLOCKING_END";qs.O[qs.O.Mj]="CONTAINER_EXECUTE_END";qs.O[qs.O.Oj]="CONTAINER_YIELD_END";qs.O[qs.O.Lh]="CONTAINER_YIELD_START";qs.O[qs.O.Tk]="EVENT_EXECUTE_END";
qs.O[qs.O.Sk]="EVENT_EVALUATION_END";qs.O[qs.O.si]="EVENT_EVALUATION_START";qs.O[qs.O.Uk]="EVENT_SETUP_END";qs.O[qs.O.Wg]="EVENT_SETUP_START";qs.O[qs.O.Xk]="GA4_CONVERSION_HIT";qs.O[qs.O.Nf]="PAGE_LOAD";qs.O[qs.O.io]="PAGEVIEW";qs.O[qs.O.ue]="SNIPPET_LOAD";qs.O[qs.O.Ki]="TAG_CALLBACK_ERROR";qs.O[qs.O.Li]="TAG_CALLBACK_FAILURE";qs.O[qs.O.Mi]="TAG_CALLBACK_SUCCESS";qs.O[qs.O.El]="TAG_EXECUTE_END";qs.O[qs.O.Ni]="TAG_EXECUTE_START";qs.O[qs.O.Nh]="CUSTOM_PERFORMANCE_START";qs.O[qs.O.Vj]="CUSTOM_PERFORMANCE_END";var rs=[],ss={},ts={};var us=["2"];function vs(a){return a.origin!=="null"};function ws(a,b,c){for(var d={},e=b.split(";"),f=function(r){return Ua(11)?r.trim():r.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&a(m)){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));var p=void 0,q=void 0;((p=d)[q=m]||(p[q]=[])).push(n)}}return d};var xs;function ys(a,b,c,d){var e;return(e=zs(function(f){return f===a},b,c,d)[a])!=null?e:[]}function zs(a,b,c,d){return As(d)?ws(a,String(b||Bs()),c):{}}function Cs(a,b,c,d,e){if(As(e)){var f=Ds(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=Es(f,function(g){return g.To},b);if(f.length===1)return f[0];f=Es(f,function(g){return g.Wp},c);return f[0]}}}function Fs(a,b,c,d){var e=Bs(),f=window;vs(f)&&(f.document.cookie=a);var g=Bs();return e!==g||c!==void 0&&ys(b,g,!1,d).indexOf(c)>=0}
function Gs(a,b,c,d){function e(w,y,z){if(z==null)return delete h[y],w;h[y]=z;return w+"; "+y+"="+z}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!As(c.Bc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Hs(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Rp);g=e(g,"samesite",c.nq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Is(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!Js(u,c.path)&&Fs(v,a,b,c.Bc))return Ua(15)&&(xs=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Js(n,c.path)?1:Fs(g,a,b,c.Bc)?0:1}
function Ks(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(rs.includes("2")){var d;(d=dd())==null||d.mark("2-"+qs.O.Nh+"-"+(ts["2"]||0))}var e=Gs(a,b,c);if(rs.includes("2")){var f="2-"+qs.O.Vj+"-"+(ts["2"]||0),g={start:"2-"+qs.O.Nh+"-"+(ts["2"]||0),end:f},h;(h=dd())==null||h.mark(f);var m,n,p=(n=(m=dd())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(ts["2"]=(ts["2"]||0)+1,ss["2"]=p+(ss["2"]||0))}return e}
function Es(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function Ds(a,b,c){for(var d=[],e=ys(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Ko:e[f],Lo:g.join("."),To:Number(n[0])||1,Wp:Number(n[1])||1})}}}return d}function Hs(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Ls=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Ms=/(^|\.)doubleclick\.net$/i;function Js(a,b){return a!==void 0&&(Ms.test(window.document.location.hostname)||b==="/"&&Ls.test(a))}function Ns(a){if(!a)return 1;var b=a;Ua(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Os(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Ps(a,b){var c=""+Ns(a),d=Os(b);d>1&&(c+="-"+d);return c}
var Bs=function(){return vs(window)?window.document.cookie:""},As=function(a){return a&&Ua(7)?(Array.isArray(a)?a:[a]).every(function(b){return on(b)&&mn(b)}):!0},Is=function(){var a=xs,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Ms.test(g)||Ls.test(g)||b.push("none");return b};function Qs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^ps(a)&2147483647):String(b)}function Rs(a){return[Qs(a),Math.round(Db()/1E3)].join(".")}function Ss(a,b,c,d,e){var f=Ns(b),g;return(g=Cs(a,f,Os(c),d,e))==null?void 0:g.Lo};var Ts;function Us(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Vs,d=Ws,e=Xs();if(!e.init){Oc(A,"mousedown",a);Oc(A,"keyup",a);Oc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ys(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Xs().decorators.push(f)}
function Zs(a,b,c){for(var d=Xs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Hb(e,g.callback())}}return e}
function Xs(){var a=Bc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var $s=/(.*?)\*(.*?)\*(.*)/,at=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,bt=/^(?:www\.|m\.|amp\.)+/,ct=/([^?#]+)(\?[^#]*)?(#.*)?/;function dt(a){var b=ct.exec(a);if(b)return{sj:b[1],query:b[2],fragment:b[3]}}function et(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function ft(a,b){var c=[xc.userAgent,(new Date).getTimezoneOffset(),xc.userLanguage||xc.language,Math.floor(Db()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Ts)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Ts=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Ts[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function gt(a){return function(b){var c=el(x.location.href),d=c.search.replace("?",""),e=Wk(d,"_gl",!1,!0)||"";b.query=ht(e)||{};var f=Zk(c,"fragment"),g;var h=-1;if(Jb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=ht(g||"")||{};a&&it(c,d,f)}}function jt(a,b){var c=et(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function it(a,b,c){function d(g,h){var m=jt("_gl",g);m.length&&(m=h+m);return m}if(wc&&wc.replaceState){var e=et("_gl");if(e.test(b)||e.test(c)){var f=Zk(a,"path");b=d(b,"?");c=d(c,"#");wc.replaceState({},"",""+f+b+c)}}}function kt(a,b){var c=gt(!!b),d=Xs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Hb(e,f.query),a&&Hb(e,f.fragment));return e}
var ht=function(a){try{var b=lt(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=gb(d[e+1]);c[f]=g}ib("TAGGING",6);return c}}catch(h){ib("TAGGING",8)}};function lt(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=$s.exec(d);if(f){c=f;break a}d=Yk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===ft(h,p)){m=!0;break a}m=!1}if(m)return h;ib("TAGGING",7)}}}
function mt(a,b,c,d,e){function f(p){p=jt(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=dt(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.sj+h+m}
function nt(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(fb(String(y))))}var z=v.join("*");u=["1",ft(z),z].join("*");d?(Ua(3)||Ua(1)||!p)&&ot("_gl",u,a,p,q):pt("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Zs(b,1,d),f=Zs(b,2,d),g=Zs(b,4,d),h=Zs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ua(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
qt(m,h[m],a)}function qt(a,b,c){c.tagName.toLowerCase()==="a"?pt(a,b,c):c.tagName.toLowerCase()==="form"&&ot(a,b,c)}function pt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ua(4)||d)){var h=x.location.href,m=dt(c.href),n=dt(h);g=!(m&&n&&m.sj===n.sj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=mt(a,b,c.href,d,e);mc.test(p)&&(c.href=p)}}
function ot(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=mt(a,b,f,d,e);mc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Vs(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||nt(e,e.hostname)}}catch(g){}}function Ws(a){try{var b=a.getAttribute("action");if(b){var c=Zk(el(b),"host");nt(a,c)}}catch(d){}}function rt(a,b,c,d){Us();var e=c==="fragment"?2:1;d=!!d;Ys(a,b,e,d,!1);e===2&&ib("TAGGING",23);d&&ib("TAGGING",24)}
function st(a,b){Us();Ys(a,[al(x.location,"host",!0)],b,!0,!0)}function tt(){var a=A.location.hostname,b=at.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Yk(f[2])||"":Yk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(bt,""),m=e.replace(bt,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ut(a,b){return a===!1?!1:a||b||tt()};var vt=["1"],wt={},xt={};function zt(a,b){b=b===void 0?!0:b;var c=At(a.prefix);if(wt[c])Bt(a);else if(Ct(c,a.path,a.domain)){var d=xt[At(a.prefix)]||{id:void 0,Ah:void 0};b&&Dt(a,d.id,d.Ah);Bt(a)}else{var e=gl("auiddc");if(e)ib("TAGGING",17),wt[c]=e;else if(b){var f=At(a.prefix),g=Rs();Et(f,g,a);Ct(c,a.path,a.domain);Bt(a,!0)}}}
function Bt(a,b){if((b===void 0?0:b)&&ks(gs)){var c=cs(!1);c.error!==0?ib("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,ds(c)!==0&&ib("TAGGING",41)):ib("TAGGING",40):ib("TAGGING",39)}if(os(gs)&&ls([gs])[gs.qb]===-1){for(var d={},e=(d[gs.qb]=0,d),f=l(is),g=f.next();!g.done;g=f.next()){var h=g.value;h!==gs&&os(h)&&(e[h.qb]=0)}ms(e,a)}}
function Dt(a,b,c){var d=At(a.prefix),e=wt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Db()/1E3)));Et(d,h,a,g*1E3)}}}}function Et(a,b,c,d){var e;e=["1",Ps(c.domain,c.path),b].join(".");var f=$r(c,d);f.Bc=Ft();Ks(a,e,f)}function Ct(a,b,c){var d=Ss(a,b,c,vt,Ft());if(!d)return!1;Gt(a,d);return!0}
function Gt(a,b){var c=b.split(".");c.length===5?(wt[a]=c.slice(0,2).join("."),xt[a]={id:c.slice(2,4).join("."),Ah:Number(c[4])||0}):c.length===3?xt[a]={id:c.slice(0,2).join("."),Ah:Number(c[2])||0}:wt[a]=b}function At(a){return(a||"_gcl")+"_au"}function Ht(a){function b(){mn(c)&&a()}var c=Ft();sn(function(){b();mn(c)||tn(b,c)},c)}
function It(a){var b=kt(!0),c=At(a.prefix);Ht(function(){var d=b[c];if(d){Gt(c,d);var e=Number(wt[c].split(".")[1])*1E3;if(e){ib("TAGGING",16);var f=$r(a,e);f.Bc=Ft();var g=["1",Ps(a.domain,a.path),d].join(".");Ks(c,g,f)}}})}function Jt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Ss(a,e.path,e.domain,vt,Ft());h&&(g[a]=h);return g};Ht(function(){rt(f,b,c,d)})}function Ft(){return["ad_storage","ad_user_data"]};function Kt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Ej:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Lt(a,b){var c=Kt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Ej]||(d[c[e].Ej]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Ej].push(g)}}return d};var Mt={},Nt=(Mt.k={da:/^[\w-]+$/},Mt.b={da:/^[\w-]+$/,xj:!0},Mt.i={da:/^[1-9]\d*$/},Mt.h={da:/^\d+$/},Mt.t={da:/^[1-9]\d*$/},Mt.d={da:/^[A-Za-z0-9_-]+$/},Mt.j={da:/^\d+$/},Mt.u={da:/^[1-9]\d*$/},Mt.l={da:/^[01]$/},Mt.o={da:/^[1-9]\d*$/},Mt.g={da:/^[01]$/},Mt.s={da:/^.+$/},Mt);var Ot={},St=(Ot[5]={Hh:{2:Pt},lj:"2",qh:["k","i","b","u"]},Ot[4]={Hh:{2:Pt,GCL:Qt},lj:"2",qh:["k","i","b"]},Ot[2]={Hh:{GS2:Pt,GS1:Rt},lj:"GS2",qh:"sogtjlhd".split("")},Ot);function Tt(a,b,c){var d=St[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hh[e];if(f)return f(a,b)}}}
function Pt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=St[b];if(f){for(var g=f.qh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Nt[p];r&&(r.xj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ut(a,b,c){var d=St[b];if(d)return[d.lj,c||"1",Vt(a,b)].join(".")}
function Vt(a,b){var c=St[b];if(c){for(var d=[],e=l(c.qh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Nt[g];if(h){var m=a[g];if(m!==void 0)if(h.xj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Qt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Rt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Wt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Xt(a,b,c){if(St[b]){for(var d=[],e=ys(a,void 0,void 0,Wt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Tt(g.value,b,c);h&&d.push(Yt(h))}return d}}
function Zt(a){var b=$t;if(St[2]){for(var c={},d=zs(a,void 0,void 0,Wt.get(2)),e=Object.keys(d).sort(),f=l(e),g=f.next();!g.done;g=f.next())for(var h=g.value,m=l(d[h]),n=m.next();!n.done;n=m.next()){var p=Tt(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(Yt(p)))}return c}}function au(a,b,c,d,e){d=d||{};var f=Ps(d.domain,d.path),g=Ut(b,c,f);if(!g)return 1;var h=$r(d,e,void 0,Wt.get(c));return Ks(a,g,h)}function bu(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Yt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Vf:void 0},c=b.next()){var e=c.value,f=a[e];d.Vf=Nt[e];d.Vf?d.Vf.xj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return bu(h,g.Vf)}}(d)):void 0:typeof f==="string"&&bu(f,d.Vf)||(a[e]=void 0):a[e]=void 0}return a};var cu=function(){this.value=0};cu.prototype.set=function(a){return this.value|=1<<a};var du=function(a,b){b<=0||(a.value|=1<<b-1)};cu.prototype.get=function(){return this.value};cu.prototype.clear=function(a){this.value&=~(1<<a)};cu.prototype.clearAll=function(){this.value=0};cu.prototype.equals=function(a){return this.value===a.value};function eu(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function fu(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function gu(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Rb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Rb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(ps((""+b+e).toLowerCase()))};var hu={},iu=(hu.gclid=!0,hu.dclid=!0,hu.gbraid=!0,hu.wbraid=!0,hu),ju=/^\w+$/,ku=/^[\w-]+$/,lu={},mu=(lu.aw="_aw",lu.dc="_dc",lu.gf="_gf",lu.gp="_gp",lu.gs="_gs",lu.ha="_ha",lu.ag="_ag",lu.gb="_gb",lu),nu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,ou=/^www\.googleadservices\.com$/;function pu(){return["ad_storage","ad_user_data"]}function qu(a){return!Ua(7)||mn(a)}function ru(a,b){function c(){var d=qu(b);d&&a();return d}sn(function(){c()||tn(c,b)},b)}
function su(a){return tu(a).map(function(b){return b.gclid})}function uu(a){return vu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function vu(a){var b=wu(a.prefix),c=xu("gb",b),d=xu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=tu(c).map(e("gb")),g=yu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function zu(a,b,c,d,e){var f=sb(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Pc=e),f.labels=Au(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Pc:e})}function yu(a){for(var b=Xt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=Bu(f);h&&zu(c,g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function tu(a){for(var b=[],c=ys(a,A.cookie,void 0,pu()),d=l(c),e=d.next();!e.done;e=d.next()){var f=Cu(e.value);f!=null&&(f.Pc=void 0,f.Aa=new cu,f.kb=[1],Du(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return Eu(b)}function Fu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function Du(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Aa&&b.Aa&&h.Aa.equals(b.Aa)&&(e=h)}if(d){var m,n,p=(m=d.Aa)!=null?m:new cu,q=(n=b.Aa)!=null?n:new cu;p.value|=q.value;d.Aa=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Pc=b.Pc);d.labels=Fu(d.labels||[],b.labels||[]);d.kb=Fu(d.kb||[],b.kb||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function Gu(a){if(!a)return new cu;var b=new cu;if(a===1)return du(b,2),du(b,3),b;du(b,a);return b}
function Hu(){var a=es("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(ku))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new cu;typeof e==="number"?g=Gu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Aa:g,kb:[2]}}catch(h){return null}}
function Iu(){var a=es("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(ku))return b;var f=new cu,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Aa:f,kb:[2]});return b},[])}catch(b){return null}}
function Ju(a){for(var b=[],c=ys(a,A.cookie,void 0,pu()),d=l(c),e=d.next();!e.done;e=d.next()){var f=Cu(e.value);f!=null&&(f.Pc=void 0,f.Aa=new cu,f.kb=[1],Du(b,f))}var g=Hu();g&&(g.Pc=void 0,g.kb=g.kb||[2],Du(b,g));if(Ua(13)){var h=Iu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Pc=void 0;p.kb=p.kb||[2];Du(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Eu(b)}
function Au(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function wu(a){return a&&typeof a==="string"&&a.match(ju)?a:"_gcl"}function Ku(a,b){if(a){var c={value:a,Aa:new cu};du(c.Aa,b);return c}}
function Lu(a,b,c){var d=el(a),e=Zk(d,"query",!1,void 0,"gclsrc"),f=Ku(Zk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Ku(Wk(g,"gclid",!1),3));e||(e=Wk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Mu(a,b){var c=el(a),d=Zk(c,"query",!1,void 0,"gclid"),e=Zk(c,"query",!1,void 0,"gclsrc"),f=Zk(c,"query",!1,void 0,"wbraid");f=Pb(f);var g=Zk(c,"query",!1,void 0,"gbraid"),h=Zk(c,"query",!1,void 0,"gad_source"),m=Zk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Wk(n,"gclid",!1);e=e||Wk(n,"gclsrc",!1);f=f||Wk(n,"wbraid",!1);g=g||Wk(n,"gbraid",!1);h=h||Wk(n,"gad_source",!1)}return Nu(d,e,m,f,g,h)}function Ou(){return Mu(x.location.href,!0)}
function Nu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(ku))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&ku.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&ku.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&ku.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Pu(a){for(var b=Ou(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Mu(x.document.referrer,!1),b.gad_source=void 0);Qu(b,!1,a)}
function Ru(a){Pu(a);var b=Lu(x.location.href,!0,!1);b.length||(b=Lu(x.document.referrer,!1,!0));a=a||{};Su(a);if(b.length){var c=b[0],d=Db(),e=$r(a,d,!0),f=pu(),g=function(){qu(f)&&e.expires!==void 0&&bs("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Aa.get()},expires:Number(e.expires)})};sn(function(){g();qu(f)||tn(g,f)},f)}}
function Su(a){var b;if(b=Ua(14)){var c=Tu();b=nu.test(c)||ou.test(c)||Uu()}if(b){var d;a:{for(var e=el(x.location.href),f=Xk(Zk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!iu[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=eu(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=fu(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,z=w.next().value,B=y,D=z,G=B&7;if(B>>3===16382){if(G!==0)break;var I=fu(t,D);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,ea=t,Q=D;switch(G){case 0:M=(T=fu(ea,Q))==null?void 0:T[1];break d;case 1:M=Q+8;break d;case 2:var W=fu(ea,Q);if(W===void 0)break;var ja=l(W),ka=ja.next().value;M=ja.next().value+ka;break d;case 5:M=Q+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Vu(Y,7,a)}}
function Vu(a,b,c){c=c||{};var d=Db(),e=$r(c,d,!0),f=pu(),g=function(){if(qu(f)&&e.expires!==void 0){var h=Iu()||[];Du(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Aa:Gu(b)},!0);bs("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Aa?m.Aa.get():0},expires:Number(m.expires)}}))}};sn(function(){qu(f)?g():tn(g,f)},f)}
function Qu(a,b,c,d,e){c=c||{};e=e||[];var f=wu(c.prefix),g=d||Db(),h=Math.round(g/1E3),m=pu(),n=!1,p=!1,q=function(){if(qu(m)){var r=$r(c,g,!0);r.Bc=m;for(var t=function(T,ea){var Q=xu(T,f);Q&&(Ks(Q,ea,r),T!=="gb"&&(n=!0))},u=function(T){var ea=["GCL",h,T];e.length>0&&ea.push(e.join("."));return ea.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],B=xu("gb",f);!b&&tu(B).some(function(T){return T.gclid===z&&T.labels&&
T.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&qu("ad_storage")&&(p=!0,!n)){var D=a.gbraid,G=xu("ag",f);if(b||!yu(G).some(function(T){return T.gclid===D&&T.labels&&T.labels.length>0})){var I={},M=(I.k=D,I.i=""+h,I.b=e,I);au(G,M,5,c,g)}}Wu(a,f,g,c)};sn(function(){q();qu(m)||tn(q,m)},m)}
function Wu(a,b,c,d){if(a.gad_source!==void 0&&qu("ad_storage")){var e=cd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=xu("gs",b);if(g){var h=Math.floor((Db()-(bd()||0))/1E3),m,n=gu(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);au(g,m,5,d,c)}}}}
function Xu(a,b){var c=kt(!0);ru(function(){for(var d=wu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(mu[f]!==void 0){var g=xu(f,d),h=c[g];if(h){var m=Math.min(Yu(h),Db()),n;b:{for(var p=m,q=ys(g,A.cookie,void 0,pu()),r=0;r<q.length;++r)if(Yu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=$r(b,m,!0);t.Bc=pu();Ks(g,h,t)}}}}Qu(Nu(c.gclid,c.gclsrc),!1,b)},pu())}
function Zu(a){var b=["ag"],c=kt(!0),d=wu(a.prefix);ru(function(){for(var e=0;e<b.length;++e){var f=xu(b[e],d);if(f){var g=c[f];if(g){var h=Tt(g,5);if(h){var m=Bu(h);m||(m=Db());var n;a:{for(var p=m,q=Xt(f,5),r=0;r<q.length;++r)if(Bu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);au(f,h,5,a,m)}}}}},["ad_storage"])}function xu(a,b){var c=mu[a];if(c!==void 0)return b+c}function Yu(a){return $u(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function Bu(a){return a?(Number(a.i)||0)*1E3:0}function Cu(a){var b=$u(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function $u(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!ku.test(a[2])?[]:a}
function av(a,b,c,d,e){if(Array.isArray(b)&&vs(x)){var f=wu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=xu(a[m],f);if(n){var p=ys(n,A.cookie,void 0,pu());p.length&&(h[n]=p.sort()[p.length-1])}}return h};ru(function(){rt(g,b,c,d)},pu())}}
function bv(a,b,c,d){if(Array.isArray(a)&&vs(x)){var e=["ag"],f=wu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=xu(e[m],f);if(!n)return{};var p=Xt(n,5);if(p.length){var q=p.sort(function(r,t){return Bu(t)-Bu(r)})[0];h[n]=Ut(q,5)}}return h};ru(function(){rt(g,a,b,c)},["ad_storage"])}}function Eu(a){return a.filter(function(b){return ku.test(b.gclid)})}
function cv(a,b){if(vs(x)){for(var c=wu(b.prefix),d={},e=0;e<a.length;e++)mu[a[e]]&&(d[a[e]]=mu[a[e]]);ru(function(){wb(d,function(f,g){var h=ys(c+g,A.cookie,void 0,pu());h.sort(function(t,u){return Yu(u)-Yu(t)});if(h.length){var m=h[0],n=Yu(m),p=$u(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=$u(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Qu(q,!0,b,n,p)}})},pu())}}
function dv(a){var b=["ag"],c=["gbraid"];ru(function(){for(var d=wu(a.prefix),e=0;e<b.length;++e){var f=xu(b[e],d);if(!f)break;var g=Xt(f,5);if(g.length){var h=g.sort(function(q,r){return Bu(r)-Bu(q)})[0],m=Bu(h),n=h.b,p={};p[c[e]]=h.k;Qu(p,!0,a,m,n)}}},["ad_storage"])}function ev(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function fv(a){function b(h,m,n){n&&(h[m]=n)}if(pn()){var c=Ou(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:kt(!1)._gs);if(ev(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);st(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);st(function(){return g},1)}}}function Uu(){var a=el(x.location.href);return Zk(a,"query",!1,void 0,"gad_source")}
function gv(a){if(!Ua(1))return null;var b=kt(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ua(2)){b=Uu();if(b!=null)return b;var c=Ou();if(ev(c,a))return"0"}return null}function hv(a){var b=gv(a);b!=null&&st(function(){var c={};return c.gad_source=b,c},4)}function iv(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function jv(a,b,c,d){var e=[];c=c||{};if(!qu(pu()))return e;var f=tu(a),g=iv(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=$r(c,p,!0);r.Bc=pu();Ks(a,q,r)}return e}
function kv(a,b){var c=[];b=b||{};var d=vu(b),e=iv(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=wu(b.prefix),n=xu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);au(n,y,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=$r(b,u,!0);B.Bc=pu();Ks(n,z,B)}}return c}
function lv(a,b){var c=wu(b),d=xu(a,c);if(!d)return 0;var e;e=a==="ag"?yu(d):tu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function mv(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function nv(a){var b=Math.max(lv("aw",a),mv(qu(pu())?Lt():{})),c=Math.max(lv("gb",a),mv(qu(pu())?Lt("_gac_gb",!0):{}));c=Math.max(c,lv("ag",a));return c>b}
function Tu(){return A.referrer?Zk(el(A.referrer),"host"):""};
var ov=function(a,b){b=b===void 0?!1:b;var c=Ip("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},pv=function(a){return fl(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},wv=function(a,b,c,d,e){var f=wu(a.prefix);if(ov(f,!0)){var g=Ou(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=qv(),r=q.Zf,t=q.Vl;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Gd:p});n&&h.push({gclid:n,Gd:"ds"});h.length===2&&N(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Gd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Gd:"aw.ds"});rv(function(){var u=wp(sv());if(u){zt(a);var v=[],w=u?wt[At(a.prefix)]:void 0;w&&v.push("auid="+w);if(wp(K.m.V)){e&&v.push("userId="+e);var y=In(En.Z.yl);if(y===void 0)Hn(En.Z.zl,!0);else{var z=In(En.Z.kh);v.push("ga_uid="+z+"."+y)}}var B=Tu(),D=u||!d?h:[];D.length===0&&(nu.test(B)||ou.test(B))&&D.push({gclid:"",Gd:""});if(D.length!==0||r!==void 0){B&&v.push("ref="+encodeURIComponent(B));var G=tv();v.push("url="+
encodeURIComponent(G));v.push("tft="+Db());var I=bd();I!==void 0&&v.push("tfd="+Math.round(I));var M=Ul(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var T={};c=vq(lq(new kq(0),(T[K.m.Ha]=Sq.C[K.m.Ha],T)))}v.push("gtm="+Yr({Oa:b}));Kr()&&v.push("gcs="+Lr());v.push("gcd="+Pr(c));Sr()&&v.push("dma_cps="+Qr());v.push("dma="+Rr());Jr(c)?v.push("npa=0"):v.push("npa=1");Ur()&&v.push("_ng=1");nr(vr())&&
v.push("tcfd="+Tr());var ea=Cr();ea&&v.push("gdpr="+ea);var Q=Br();Q&&v.push("gdpr_consent="+Q);F(23)&&v.push("apve=0");F(123)&&kt(!1)._up&&v.push("gtm_up=1");wk()&&v.push("tag_exp="+wk());if(D.length>0)for(var W=0;W<D.length;W++){var ja=D[W],ka=ja.gclid,Y=ja.Gd;if(!uv(a.prefix,Y+"."+ka,w!==void 0)){var X=vv+"?"+v.join("&");ka!==""?X=Y==="gb"?X+"&wbraid="+ka:X+"&gclid="+ka+"&gclsrc="+Y:Y==="aw.ds"&&(X+="&gclsrc=aw.ds");Vc(X)}}else if(r!==void 0&&!uv(a.prefix,"gad",w!==void 0)){var ha=vv+"?"+v.join("&");
Vc(ha)}}}})}},uv=function(a,b,c){var d=Ip("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},qv=function(){var a=el(x.location.href),b=void 0,c=void 0,d=Zk(a,"query",!1,void 0,"gad_source"),e=Zk(a,"query",!1,void 0,"gad_campaignid"),f,g=a.hash.replace("#","").match(xv);f=g?g[1]:void 0;d&&f?(b=d,c=1):d?(b=d,c=2):f&&(b=f,c=3);return{Zf:b,Vl:c,Xi:e}},tv=function(){var a=Ul(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},yv=function(a){var b=
[];wb(a,function(c,d){d=Eu(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},Av=function(a,b){return zv("dc",a,b)},Bv=function(a,b){return zv("aw",a,b)},zv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=gl("gcl"+a);if(d)return d.split(".")}var e=wu(b);if(e==="_gcl"){var f=!wp(sv())&&c,g;g=Ou()[a]||[];if(g.length>0)return f?["0"]:g}var h=xu(a,e);return h?su(h):[]},rv=function(a){var b=sv();zp(function(){a();wp(b)||tn(a,b)},b)},sv=
function(){return[K.m.U,K.m.V]},vv=Oi(36,'https://adservice.google.com/pagead/regclk'),xv=/^gad_source[_=](\d+)$/;function Cv(){return Ip("dedupe_gclid",function(){return Rs()})};var Dv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Ev=/^www.googleadservices.com$/;function Fv(a){a||(a=Gv());return a.Gq?!1:a.yp||a.Ap||a.Dp||a.Bp||a.Zf||a.Xi||a.hp||a.Cp||a.np?!0:!1}function Gv(){var a={},b=kt(!0);a.Gq=!!b._up;var c=Ou(),d=qv();a.yp=c.aw!==void 0;a.Ap=c.dc!==void 0;a.Dp=c.wbraid!==void 0;a.Bp=c.gbraid!==void 0;a.Cp=c.gclsrc==="aw.ds";a.Zf=d.Zf;a.Xi=d.Xi;var e=A.referrer?Zk(el(A.referrer),"host"):"";a.np=Dv.test(e);a.hp=Ev.test(e);return a};function Hv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function Iv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Jv(){return["ad_storage","ad_user_data"]}function Kv(a){if(F(38)&&!In(En.Z.ol)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{Hv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Hn(En.Z.ol,function(d){d.gclid&&Vu(d.gclid,5,a)}),Iv(c)||N(178))})}catch(c){N(177)}};sn(function(){qu(Jv())?b():tn(b,Jv())},Jv())}};var Lv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Mv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?Hn(En.Z.Pf,{gadSource:a.data.gadSource}):N(173)}
function Nv(a,b){if(F(a)){if(In(En.Z.Pf))return N(176),En.Z.Pf;if(In(En.Z.rl))return N(170),En.Z.Pf;var c=Wl();if(!c)N(171);else if(c.opener){var d=function(g){if(Lv.includes(g.origin)){a===119?Mv(g):a===200&&(Mv(g),g.data.gclid&&Vu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);fr(c,"message",d)}else N(172)};if(er(c,"message",d)){Hn(En.Z.rl,!0);for(var e=l(Lv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);N(174);return En.Z.Pf}N(175)}}}
;var Ov=function(){this.C=this.gppString=void 0};Ov.prototype.reset=function(){this.C=this.gppString=void 0};var Pv=new Ov;var Qv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Rv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Sv=/^\d+\.fls\.doubleclick\.net$/,Tv=/;gac=([^;?]+)/,Uv=/;gacgb=([^;?]+)/;
function Vv(a,b){if(Sv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(Qv)?Yk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Wv(a,b,c){for(var d=qu(pu())?Lt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=jv("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{fp:f?e.join(";"):"",ep:Vv(d,Uv)}}function Xv(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Rv)?b[1]:void 0}
function Yv(a){var b={},c,d,e;Sv.test(A.location.host)&&(c=Xv("gclgs"),d=Xv("gclst"),e=Xv("gcllp"));if(c&&d&&e)b.th=c,b.wh=d,b.uh=e;else{var f=Db(),g=yu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Pc});h.length>0&&m.length>0&&n.length>0&&(b.th=h.join("."),b.wh=m.join("."),b.uh=n.join("."))}return b}
function Zv(a,b,c,d){d=d===void 0?!1:d;if(Sv.test(A.location.host)){var e=Xv(c);if(e){if(d){var f=new cu;du(f,2);du(f,3);return e.split(".").map(function(h){return{gclid:h,Aa:f,kb:[1]}})}return e.split(".").map(function(h){return{gclid:h,Aa:new cu,kb:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Ju(g):tu(g)}if(b==="wbraid")return tu((a||"_gcl")+"_gb");if(b==="braids")return vu({prefix:a})}return[]}function $v(a){return Sv.test(A.location.host)?!(Xv("gclaw")||Xv("gac")):nv(a)}
function aw(a,b,c){var d;d=c?kv(a,b):jv((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function bw(){var a=x.__uspapi;if(ob(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var gw=function(a){if(a.eventName===K.m.ra&&R(a,P.A.ba)===L.J.Ia)if(F(24)){S(a,P.A.te,O(a.D,K.m.Ba)!=null&&O(a.D,K.m.Ba)!==!1&&!wp([K.m.U,K.m.V]));var b=cw(a),c=O(a.D,K.m.Va)!==!1;c||U(a,K.m.Rh,"1");var d=wu(b.prefix),e=R(a,P.A.fh);if(!R(a,P.A.fa)&&!R(a,P.A.Sf)&&!R(a,P.A.se)){var f=O(a.D,K.m.Fb),g=O(a.D,K.m.Wa)||{};dw({xe:c,Ce:g,He:f,Oc:b});if(!e&&!ov(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{U(a,K.m.gd,K.m.Wc);if(R(a,P.A.fa))U(a,K.m.gd,K.m.bn),U(a,K.m.fa,"1");else if(R(a,P.A.Sf))U(a,K.m.gd,
K.m.on);else if(R(a,P.A.se))U(a,K.m.gd,K.m.ln);else{var h=Ou();U(a,K.m.Xc,h.gclid);U(a,K.m.ed,h.dclid);U(a,K.m.ek,h.gclsrc);ew(a,K.m.Xc)||ew(a,K.m.ed)||(U(a,K.m.be,h.wbraid),U(a,K.m.Qe,h.gbraid));U(a,K.m.Xa,Tu());U(a,K.m.Ca,tv());if(F(27)&&Ac){var m=Zk(el(Ac),"host");m&&U(a,K.m.Nk,m)}if(!R(a,P.A.se)){var n=qv();U(a,K.m.Oe,n.Zf);U(a,K.m.Pe,n.Vl)}U(a,K.m.Gc,Ul(!0));var p=Gv();Fv(p)&&U(a,K.m.kd,"1");U(a,K.m.gk,Cv());kt(!1)._up==="1"&&U(a,K.m.Dk,"1")}fo=!0;U(a,K.m.Eb);U(a,K.m.Qb);var q=wp([K.m.U,K.m.V]);
q&&(U(a,K.m.Eb,fw()),c&&(zt(b),U(a,K.m.Qb,wt[At(b.prefix)])));U(a,K.m.nc);U(a,K.m.xb);if(!ew(a,K.m.Xc)&&!ew(a,K.m.ed)&&$v(d)){var r=uu(b);r.length>0&&U(a,K.m.nc,r.join("."))}else if(!ew(a,K.m.be)&&q){var t=su(d+"_aw");t.length>0&&U(a,K.m.xb,t.join("."))}U(a,K.m.Gk,cd());a.D.isGtmEvent&&(a.D.C[K.m.Ha]=Sq.C[K.m.Ha]);Jr(a.D)?U(a,K.m.vc,!1):U(a,K.m.vc,!0);S(a,P.A.rg,!0);var u=bw();u!==void 0&&U(a,K.m.Cf,u||"error");var v=Cr();v&&U(a,K.m.hd,v);if(F(137))try{var w=Intl.DateTimeFormat().resolvedOptions().timeZone;
U(a,K.m.ii,w||"-")}catch(D){U(a,K.m.ii,"e")}var y=Br();y&&U(a,K.m.sd,y);var z=Pv.gppString;z&&U(a,K.m.jf,z);var B=Pv.C;B&&U(a,K.m.hf,B);S(a,P.A.za,!1)}}else a.isAborted=!0},cw=function(a){var b={prefix:O(a.D,K.m.mb)||O(a.D,K.m.nb),domain:O(a.D,K.m.yb),Qc:O(a.D,K.m.zb),flags:O(a.D,K.m.Db)};a.D.isGtmEvent&&(b.path=O(a.D,K.m.Sb));return b},hw=function(a,b){var c,d,e,f,g,h,m,n;c=a.xe;d=a.Ce;e=a.He;f=a.Oa;g=a.D;h=a.De;m=a.Cr;n=a.Fm;dw({xe:c,Ce:d,He:e,Oc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,wv(b,
f,g,h,n))},iw=function(a,b){if(!R(a,P.A.se)){var c=Nv(119);if(c){var d=In(c),e=function(g){S(a,P.A.se,!0);var h=ew(a,K.m.Oe),m=ew(a,K.m.Pe);U(a,K.m.Oe,String(g.gadSource));U(a,K.m.Pe,6);S(a,P.A.fa);S(a,P.A.Sf);U(a,K.m.fa);b();U(a,K.m.Oe,h);U(a,K.m.Pe,m);S(a,P.A.se,!1)};if(d)e(d);else{var f=void 0;f=Kn(c,function(g,h){e(h);Ln(c,f)})}}}},dw=function(a){var b,c,d,e;b=a.xe;c=a.Ce;d=a.He;e=a.Oc;b&&(ut(c[K.m.nf],!!c[K.m.oa])&&(Xu(jw,e),Zu(e),It(e)),Ul()!==2?(Ru(e),Kv(e),Nv(200,e)):Pu(e),cv(jw,e),dv(e));
c[K.m.oa]&&(av(jw,c[K.m.oa],c[K.m.ld],!!c[K.m.Jc],e.prefix),bv(c[K.m.oa],c[K.m.ld],!!c[K.m.Jc],e.prefix),Jt(At(e.prefix),c[K.m.oa],c[K.m.ld],!!c[K.m.Jc],e),Jt("FPAU",c[K.m.oa],c[K.m.ld],!!c[K.m.Jc],e));d&&(F(101)?fv(kw):fv(lw));hv(lw)},mw=function(a,b,c,d){var e,f,g;e=a.Gm;f=a.callback;g=a.bm;if(typeof f==="function")if(e===K.m.xb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===K.m.Qb?(N(65),zt(b,!1),f(wt[At(b.prefix)])):f(g)},nw=function(a,b){Array.isArray(b)||
(b=[b]);var c=R(a,P.A.ba);return b.indexOf(c)>=0},jw=["aw","dc","gb"],lw=["aw","dc","gb","ag"],kw=["aw","dc","gb","ag","gad_source"];function ow(a){var b=O(a.D,K.m.Ic),c=O(a.D,K.m.Hc);b&&!c?(a.eventName!==K.m.ra&&a.eventName!==K.m.Xd&&N(131),a.isAborted=!0):!b&&c&&(N(132),a.isAborted=!0)}function pw(a){var b=wp(K.m.U)?Hp.pscdl:"denied";b!=null&&U(a,K.m.Gg,b)}function qw(a){var b=Ul(!0);U(a,K.m.Gc,b)}function rw(a){Ur()&&U(a,K.m.fe,1)}
function fw(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Yk(a.substring(0,b))===void 0;)b--;return Yk(a.substring(0,b))||""}function sw(a){tw(a,Pp.Df.Pm,O(a.D,K.m.zb))}function tw(a,b,c){ew(a,K.m.wd)||U(a,K.m.wd,{});ew(a,K.m.wd)[b]=c}function uw(a){S(a,P.A.Rf,cn.X.Ga)}function vw(a){var b=lb("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,K.m.kf,b),jb())}function ww(a){var b=a.D.getMergedValues(K.m.rc);b&&a.mergeHitDataForKey(K.m.rc,b)}
function xw(a,b){b=b===void 0?!1:b;var c=R(a,P.A.Qf);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,P.A.Gj,!1),b||!yw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,P.A.Gj,!0)}function zw(a){ul&&(fo=!0,a.eventName===K.m.ra?lo(a.D,a.target.id):(R(a,P.A.Le)||(io[a.target.id]=!0),Op(R(a,P.A.fb))))};function Jw(a,b,c,d){var e=Kc(),f;if(e===1)a:{var g=qk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Vw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return ew(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){ew(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return od(c)?a.mergeHitDataForKey(b,c):!1}}};var Xw=function(a){var b=Ww[a.target.destinationId];if(!a.isAborted&&b)for(var c=Vw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Yw=function(a,b){var c=Ww[a];c||(c=Ww[a]=[]);c.push(b)},Ww={};function $w(a,b){return arguments.length===1?ax("set",a):ax("set",a,b)}function bx(a,b){return arguments.length===1?ax("config",a):ax("config",a,b)}function cx(a,b,c){c=c||{};c[K.m.pd]=a;return ax("event",b,c)}function ax(){return arguments};var ex=function(){this.messages=[];this.C=[]};ex.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};ex.prototype.listen=function(a){this.C.push(a)};
ex.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};ex.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function fx(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.A.fb]=ng.canonicalContainerId;gx().enqueue(a,b,c)}
function hx(){var a=ix;gx().listen(a)}function gx(){return Ip("mb",function(){return new ex})};var jx,kx=!1;function lx(){kx=!0;jx=jx||{}}function mx(a){kx||lx();return jx[a]};function nx(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function ox(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var qx=function(a){var b=px(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},px=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var tx=function(a){if(rx){if(a>=0&&a<sx.length&&sx[a]){var b;(b=sx[a])==null||b.disconnect();sx[a]=void 0}}else x.clearInterval(a)},wx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(rx){var e=!1;Qc(function(){e||ux(a,b,c)()});return vx(function(f){e=!0;for(var g={fg:0};g.fg<f.length;g={fg:g.fg},g.fg++)Qc(function(h){return function(){a(f[h.fg])}}(g))},
b,c)}return x.setInterval(ux(a,b,c),1E3)},ux=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:Db()};Qc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=qx(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),
f[h]++;else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},vx=function(a,b,c){for(var d=new x.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<sx.length;f++)if(!sx[f])return sx[f]=d,f;return sx.push(d)-1},sx=[],rx=!(!x.IntersectionObserver||!x.IntersectionObserverEntry);
var yx=function(a){return a.tagName+":"+a.isVisible+":"+a.ka.length+":"+xx.test(a.ka)},Mx=function(a){a=a||{Ae:!0,Be:!0,Gh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=zx(a),c=Ax[b];if(c&&Db()-c.timestamp<200)return c.result;var d=Bx(),e=d.status,f=[],g,h,m=[];if(!F(33)){if(a.Zb&&a.Zb.email){var n=Cx(d.elements);f=Dx(n,a&&a.Wf);g=Ex(f);n.length>10&&(e="3")}!a.Gh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(Fx(f[p],!!a.Ae,!!a.Be));m=m.slice(0,10)}else if(a.Zb){}g&&(h=Fx(g,!!a.Ae,!!a.Be));var G={elements:m,
vj:h,status:e};Ax[b]={timestamp:Db(),result:G};return G},Nx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Px=function(a){var b=Ox(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},Ox=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},Lx=function(a,b,c){var d=a.element,e={ka:a.ka,type:a.sa,tagName:d.tagName};b&&(e.querySelector=Qx(d));c&&(e.isVisible=!ox(d));return e},Fx=function(a,b,c){return Lx({element:a.element,ka:a.ka,sa:Kx.jc},b,c)},zx=function(a){var b=!(a==null||!a.Ae)+"."+!(a==null||!a.Be);a&&a.Wf&&a.Wf.length&&(b+="."+a.Wf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},Ex=function(a){if(a.length!==0){var b;b=Rx(a,function(c){return!Sx.test(c.ka)});b=Rx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Rx(b,function(c){return!ox(c.element)});return b[0]}},Dx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&xi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Rx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Qx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Qx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},Cx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Tx);if(f){var g=f[0],h;if(x.location){var m=al(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ka:g})}}}return b},Bx=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Ux.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Vx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||F(33)&&Wx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Tx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,xx=/@(gmail|googlemail)\./i,Sx=/support|noreply/i,Ux="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Vx=
["BR"],Xx=yg(Ri(36,''),2),Kx={jc:"1",Cd:"2",vd:"3",Bd:"4",Ke:"5",Of:"6",gh:"7",Ji:"8",Jh:"9",Fi:"10"},Ax={},Wx=["INPUT","SELECT"],Yx=Ox(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var wy=function(a,b,c){var d={};a.mergeHitDataForKey(K.m.Hi,(d[b]=c,d))},xy=function(a,b){var c=yw(a,K.m.Kg,a.D.H[K.m.Kg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},yy=function(a){var b=R(a,P.A.hb);if(od(b))return b},zy=function(a){if(R(a,P.A.zd)||!ml(a.D))return!1;if(!O(a.D,K.m.rd)){var b=O(a.D,K.m.de);return b===!0||b==="true"}return!0},Ay=function(a){return yw(a,K.m.he,O(a.D,K.m.he))||!!yw(a,"google_ng",!1)};var jg;var By=Number(Ri(57,''))||5,Cy=Number(Ri(58,''))||50,Dy=tb();
var Fy=function(a,b){a&&(Ey("sid",a.targetId,b),Ey("cc",a.clientCount,b),Ey("tl",a.totalLifeMs,b),Ey("hc",a.heartbeatCount,b),Ey("cl",a.clientLifeMs,b))},Ey=function(a,b,c){b!=null&&c.push(a+"="+b)},Gy=function(){var a=A.referrer;if(a){var b;return Zk(el(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},Hy="https://"+Oi(21,"www.googletagmanager.com")+"/a?",Jy=function(){this.R=Iy;this.N=0};Jy.prototype.H=function(a,b,c,d){var e=Gy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&Ey("si",a.hg,g);Ey("m",0,g);Ey("iss",f,g);Ey("if",c,g);Fy(b,g);d&&Ey("fm",encodeURIComponent(d.substring(0,Cy)),g);this.P(g);};Jy.prototype.C=function(a,b,c,d,e){var f=[];Ey("m",1,f);Ey("s",a,f);Ey("po",Gy(),f);b&&(Ey("st",b.state,f),Ey("si",b.hg,f),Ey("sm",b.ng,f));Fy(c,f);Ey("c",d,f);e&&Ey("fm",encodeURIComponent(e.substring(0,
Cy)),f);this.P(f);};Jy.prototype.P=function(a){a=a===void 0?[]:a;!tl||this.N>=By||(Ey("pid",Dy,a),Ey("bc",++this.N,a),a.unshift("ctid="+ng.ctid+"&t=s"),this.R(""+Hy+a.join("&")))};var Ky=Number('')||500,Ly=Number('')||5E3,My=Number('20')||10,Ny=Number('')||5E3;function Oy(a){return a.performance&&a.performance.now()||Date.now()}
var Py=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{gm:function(){},hm:function(){},fm:function(){},onFailure:function(){}}:h;this.qo=f;this.C=g;this.N=h;this.ia=this.la=this.heartbeatCount=this.oo=0;this.hh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.hg=Oy(this.C);this.ng=Oy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ea()};e.prototype.getState=function(){return{state:this.state,
hg:Math.round(Oy(this.C)-this.hg),ng:Math.round(Oy(this.C)-this.ng)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.ng=Oy(this.C))};e.prototype.Dl=function(){return String(this.oo++)};e.prototype.Ea=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Dl(),maxDelay:this.ih()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ia++,g.isDead||f.ia>My){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.no();var n,p;(p=(n=f.N).fm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Hl();else{if(f.heartbeatCount>g.stats.heartbeatCount+My){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.hh){var u,v;(v=(u=f.N).hm)==null||v.call(u)}else{f.hh=!0;var w,y;(y=(w=f.N).gm)==null||y.call(w)}f.ia=0;f.ro();f.Hl()}}})};e.prototype.ih=function(){return this.state===2?
Ly:Ky};e.prototype.Hl=function(){var f=this;this.C.setTimeout(function(){f.Ea()},Math.max(0,this.ih()-(Oy(this.C)-this.la)))};e.prototype.wo=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Dl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Mf(t,7)},(p=f.maxDelay)!=null?p:Ny),r={request:f,xm:g,rm:m,Qp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.la=Oy(this.C);f.rm=!1;this.qo(f.request)};e.prototype.ro=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.rm&&this.sendRequest(h)}};e.prototype.no=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Mf(this.H[g.value],this.R)};e.prototype.Mf=function(f,g){this.pb(f);var h=f.request;h.failure={failureType:g};f.xm(h)};e.prototype.pb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Qp)};e.prototype.wp=function(f){this.la=Oy(this.C);var g=this.H[f.requestId];if(g)this.pb(g),g.xm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Qy;
var Ry=function(){Qy||(Qy=new Jy);return Qy},Iy=function(a){Bn(Dn(cn.X.Lc),function(){Nc(a)})},Sy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ty=function(a){var b=a,c=Zj.Ea;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Uy=function(a){var b=In(En.Z.wl);return b&&b[a]},Vy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ia=null;this.initTime=c;this.C=15;this.N=this.No(a);x.setTimeout(function(){f.initialize()},1E3);Qc(function(){f.Hp(a,b,e)})};k=Vy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),hg:this.initTime,ng:Math.round(Db())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.wo(a,b,c)};k.getState=function(){return this.N.getState().state};k.Hp=function(a,b,c){var d=x.location.origin,e=this,
f=Lc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Sy(h):"",p;F(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Lc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.wp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.No=function(a){var b=this,c=Py(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{gm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},hm:function(){},fm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Wy(){var a=mg(jg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Xy(a,b){var c=Math.round(Db());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Wy()||F(168))return;yk()&&(a=""+d+xk()+"/_/service_worker");var e=Ty(a);if(e===null||Uy(e.origin))return;if(!yc()){Ry().H(void 0,void 0,6);return}var f=new Vy(e,!!a,c||Math.round(Db()),Ry(),b);Jn(En.Z.wl)[e.origin]=f;}
var Yy=function(a,b,c,d){var e;if((e=Uy(a))==null||!e.delegate){var f=yc()?16:6;Ry().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Uy(a).delegate(b,c,d);};
function Zy(a,b,c,d,e){var f=Ty();if(f===null){d(yc()?16:6);return}var g,h=(g=Uy(f.origin))==null?void 0:g.initTime,m=Math.round(Db()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Yy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function $y(a,b,c,d){var e=Ty(a);if(e===null){d("_is_sw=f"+(yc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Db()),h,m=(h=Uy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;F(169)&&(p=!0);Yy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Uy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function az(a){if(F(10)||yk()||Zj.N||ml(a.D)||F(168))return;Xy(void 0,F(131));};var bz="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function cz(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function dz(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function ez(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function fz(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function gz(a){if(!fz(a))return null;var b=cz(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(bz).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var iz=function(a,b){if(a)for(var c=hz(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;U(b,f,c[f])}},hz=function(a){var b={};b[K.m.uf]=a.architecture;b[K.m.vf]=a.bitness;a.fullVersionList&&(b[K.m.wf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.xf]=a.mobile?"1":"0";b[K.m.yf]=a.model;b[K.m.zf]=a.platform;b[K.m.Af]=a.platformVersion;b[K.m.Bf]=a.wow64?"1":"0";return b},jz=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=dz(d);if(e)c(e);else{var f=ez(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.ig||(c.ig=!0,N(106),c(null,Error("Timeout")))},b);f.then(function(h){c.ig||(c.ig=!0,N(104),d.clearTimeout(g),c(h))}).catch(function(h){c.ig||(c.ig=!0,N(105),d.clearTimeout(g),c(null,h))})}else c(null)}},lz=function(){var a=x;if(fz(a)&&(kz=Db(),!ez(a))){var b=gz(a);b&&(b.then(function(){N(95)}),b.catch(function(){N(96)}))}},kz;function mz(a){var b=a.location.href;if(a===a.top)return{url:b,Mp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Mp:c}};var aA=function(){var a;F(90)&&yo()!==""&&(a=yo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},bA=function(){var a="www";F(90)&&yo()&&(a=yo());return"https://"+a+".google-analytics.com/g/collect"};function cA(a,b){var c=!!yk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?xk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?F(90)&&yo()?aA():""+xk()+"/ag/g/c":aA();case 16:return c?F(90)&&yo()?bA():""+xk()+"/ga/g/c":bA();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
xk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?xk()+"/d/pagead/form-data":F(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.xo+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?xk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return c?xk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?xk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?xk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?xk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return F(205)?"https://www.google.com/measurement/conversion/":
c?xk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?xk()+"/d/ccm/form-data":F(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:pc(a,"Unknown endpoint")}};function dA(a){a=a===void 0?[]:a;return ak(a).join("~")}function eA(){if(!F(118))return"";var a,b;return(((a=Rm(Gm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function fA(a,b){b&&wb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var hA=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=ew(a,g),m=gA[g];m&&h!==void 0&&h!==""&&(!R(a,P.A.te)||g!==K.m.Xc&&g!==K.m.ed&&g!==K.m.be&&g!==K.m.Qe||(h="0"),d(m,h))}d("gtm",Yr({Oa:R(a,P.A.fb)}));Kr()&&d("gcs",Lr());d("gcd",Pr(a.D));Sr()&&d("dma_cps",Qr());d("dma",Rr());nr(vr())&&d("tcfd",Tr());dA()&&d("tag_exp",dA());eA()&&d("ptag_exp",eA());if(R(a,P.A.rg)){d("tft",
Db());var n=bd();n!==void 0&&d("tfd",Math.round(n))}F(24)&&d("apve","1");(F(25)||F(26))&&d("apvf",Zc()?F(26)?"f":"sb":"nf");vn[cn.X.Ga]!==bn.Ja.pe||yn[cn.X.Ga].isConsentGranted()||(c.limited_ads="1");b(c)},iA=function(a,b,c){var d=b.D;ip({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Na:{eventId:d.eventId,priorityId:d.priorityId},rh:{eventId:R(b,P.A.Ie),priorityId:R(b,P.A.Je)}})},jA=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};iA(a,b,c);wm(d,a,void 0,{Eh:!0,method:"GET"},function(){},function(){vm(d,a+"&img=1")})},kA=function(a){var b=Fc()||Dc()?"www.google.com":"www.googleadservices.com",c=[];wb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},lA=function(a){hA(a,function(b){if(R(a,P.A.ba)===L.J.Ia){var c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);
wb(b,function(r,t){c.push(r+"="+t)});var d=wp([K.m.U,K.m.V])?45:46,e=cA(d)+"?"+c.join("&");iA(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(F(26)&&Zc()){wm(g,e,void 0,{Eh:!0},function(){},function(){vm(g,e+"&img=1")});var h=wp([K.m.U,K.m.V]),m=ew(a,K.m.kd)==="1",n=ew(a,K.m.Rh)==="1";if(h&&m&&!n){var p=kA(b),q=Fc()||Dc()?58:57;jA(p,a,q)}}else um(g,e)||vm(g,e+"&img=1");if(ob(a.D.onSuccess))a.D.onSuccess()}})},mA={},gA=(mA[K.m.fa]="gcu",
mA[K.m.nc]="gclgb",mA[K.m.xb]="gclaw",mA[K.m.Oe]="gad_source",mA[K.m.Pe]="gad_source_src",mA[K.m.Xc]="gclid",mA[K.m.ek]="gclsrc",mA[K.m.Qe]="gbraid",mA[K.m.be]="wbraid",mA[K.m.Qb]="auid",mA[K.m.gk]="rnd",mA[K.m.Rh]="ncl",mA[K.m.Vh]="gcldc",mA[K.m.ed]="dclid",mA[K.m.Tb]="edid",mA[K.m.gd]="en",mA[K.m.hd]="gdpr",mA[K.m.Ub]="gdid",mA[K.m.fe]="_ng",mA[K.m.hf]="gpp_sid",mA[K.m.jf]="gpp",mA[K.m.kf]="_tu",mA[K.m.Dk]="gtm_up",mA[K.m.Gc]="frm",mA[K.m.kd]="lps",mA[K.m.Qg]="did",mA[K.m.Gk]="navt",mA[K.m.Ca]=
"dl",mA[K.m.Xa]="dr",mA[K.m.Eb]="dt",mA[K.m.Nk]="scrsrc",mA[K.m.tf]="ga_uid",mA[K.m.sd]="gdpr_consent",mA[K.m.ii]="u_tz",mA[K.m.Ma]="uid",mA[K.m.Cf]="us_privacy",mA[K.m.vc]="npa",mA);var nA={};nA.O=qs.O;var oA={ar:"L",lo:"S",vr:"Y",Jq:"B",Tq:"E",Xq:"I",qr:"TC",Wq:"HTC"},pA={lo:"S",Sq:"V",Mq:"E",nr:"tag"},qA={},rA=(qA[nA.O.Li]="6",qA[nA.O.Mi]="5",qA[nA.O.Ki]="7",qA);function sA(){function a(c,d){var e=lb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var tA=!1;
function MA(a){}function NA(a){}
function OA(){}function PA(a){}
function QA(a){}function RA(a){}
function SA(){}function TA(a,b){}
function UA(a,b,c){}
function VA(){};var WA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function XA(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},WA);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||lm(h);x.fetch(b,m).then(function(n){h==null||mm(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var w=q.decode(u.value,{stream:!v});YA(d,w);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||mm(h);
g?g():F(128)&&(b+="&_z=retryFetch",c?um(a,b,c):tm(a,b))})};var ZA=function(a){this.P=a;this.C=""},$A=function(a,b){a.H=b;return a},aB=function(a,b){a.N=b;return a},YA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}bB(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},cB=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};bB(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},bB=function(a,b){b&&(dB(b.send_pixel,b.options,a.P),dB(b.create_iframe,b.options,a.H),dB(b.fetch,b.options,a.N))};function eB(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function dB(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=od(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var fB=function(a,b){this.Up=a;this.timeoutMs=b;this.Ua=void 0},lm=function(a){a.Ua||(a.Ua=setTimeout(function(){a.Up();a.Ua=void 0},a.timeoutMs))},mm=function(a){a.Ua&&(clearTimeout(a.Ua),a.Ua=void 0)};var SB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),TB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},UB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},VB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function WB(){var a=Ek("gtm.allowlist")||Ek("gtm.whitelist");a&&N(9);nk&&!F(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:F(212)&&(a=void 0);SB.test(x.location&&x.location.hostname)&&(nk?N(116):(N(117),XB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Ib(Ab(a),TB),c=Ek("gtm.blocklist")||Ek("gtm.blacklist");c||(c=Ek("tagTypeBlacklist"))&&N(3);c?N(8):c=[];SB.test(x.location&&x.location.hostname)&&(c=Ab(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Ab(c).indexOf("google")>=0&&N(2);var d=c&&Ib(Ab(c),UB),e={};return function(f){var g=f&&f[lf.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=uk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(nk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){N(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=ub(d,h||[]);t&&
N(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:nk&&h.indexOf("cmpPartners")>=0?!YB():b&&b.indexOf("sandboxedScripts")!==-1?0:ub(d,VB))&&(u=!0);return e[g]=u}}function YB(){var a=mg(jg.C,ng.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var XB=!1;XB=!0;F(218)&&(XB=Ni(48,XB));function ZB(a,b,c,d,e){if(!Wm(a)){d.loadExperiments=bk();Fm(a,d,e);var f=$B(a),g=function(){Hm().container[a]&&(Hm().container[a].state=3);aC()},h={destinationId:a,endpoint:0};if(yk())xm(h,xk()+"/"+f,void 0,g);else{var m=Jb(a,"GTM-"),n=ll(),p=c?"/gtag/js":"/gtm.js",q=kl(b,p+f);if(!q){var r=dk.xg+p;n&&Ac&&m&&(r=Ac.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=Jw("https://","http://",r+f)}xm(h,q,void 0,g)}}}function aC(){Ym()||wb(Zm(),function(a,b){bC(a,b.transportUrl,b.context);N(92)})}
function bC(a,b,c,d){if(!Xm(a))if(c.loadExperiments||(c.loadExperiments=bk()),Ym()){var e;(e=Hm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:Gm()});Hm().destination[a].state=0;Im({ctid:a,isDestination:!0},d);N(91)}else{var f;(f=Hm().destination)[a]!=null||(f[a]={context:c,state:1,parent:Gm()});Hm().destination[a].state=1;Im({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(yk())xm(g,xk()+("/gtd"+$B(a,!0)));else{var h="/gtag/destination"+$B(a,!0),m=kl(b,
h);m||(m=Jw("https://","http://",dk.xg+h));xm(g,m)}}}function $B(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);gk!=="dataLayer"&&(c+="&l="+gk);if(!Jb(a,"GTM-")||b)c=F(130)?c+(yk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Zr();ll()&&(c+="&sign="+dk.Ii);var d=Zj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!F(191)&&bk().join("~")&&(c+="&tag_exp="+bk().join("~"));return c};var cC=function(){this.H=0;this.C={}};cC.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ge:c};return d};cC.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var eC=function(a,b){var c=[];wb(dC.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ge===void 0||b.indexOf(e.Ge)>=0)&&c.push(e.listener)});return c};function fC(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:ng.ctid}};function gC(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var iC=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;hC(this,a,b)},jC=function(a,b,c,d){if(ik.hasOwnProperty(b)||b==="__zone")return-1;var e={};od(d)&&(e=pd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},kC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},lC=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},hC=function(a,b,c){b!==void 0&&a.Tf(b);c&&x.setTimeout(function(){lC(a)},
Number(c))};iC.prototype.Tf=function(a){var b=this,c=Gb(function(){Qc(function(){a(ng.ctid,b.eventData)})});this.C?c():this.P.push(c)};var mC=function(a){a.N++;return Gb(function(){a.H++;a.R&&a.H>=a.N&&lC(a)})},nC=function(a){a.R=!0;a.H>=a.N&&lC(a)};var oC={};function pC(){return x[qC()]}
function qC(){return x.GoogleAnalyticsObject||"ga"}function tC(){var a=ng.ctid;}
function uC(a,b){return function(){var c=pC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var AC=["es","1"],BC={},CC={};function DC(a,b){if(tl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";BC[a]=[["e",c],["eid",a]];Kq(a)}}function EC(a){var b=a.eventId,c=a.Rd;if(!BC[b])return[];var d=[];CC[b]||d.push(AC);d.push.apply(d,ya(BC[b]));c&&(CC[b]=!0);return d};var FC={},GC={},HC={};function IC(a,b,c,d){tl&&F(120)&&((d===void 0?0:d)?(HC[b]=HC[b]||0,++HC[b]):c!==void 0?(GC[a]=GC[a]||{},GC[a][b]=Math.round(c)):(FC[a]=FC[a]||{},FC[a][b]=(FC[a][b]||0)+1))}function JC(a){var b=a.eventId,c=a.Rd,d=FC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete FC[b];return e.length?[["md",e.join(".")]]:[]}
function KC(a){var b=a.eventId,c=a.Rd,d=GC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete GC[b];return e.length?[["mtd",e.join(".")]]:[]}function LC(){for(var a=[],b=l(Object.keys(HC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+HC[d])}return a.length?[["mec",a.join(".")]]:[]};var MC={},NC={};function OC(a,b,c){if(tl&&b){var d=pl(b);MC[a]=MC[a]||[];MC[a].push(c+d);var e=b[lf.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Nf[e]?"1":"2")+d;NC[a]=NC[a]||[];NC[a].push(f);Kq(a)}}function PC(a){var b=a.eventId,c=a.Rd,d=[],e=MC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=NC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete MC[b],delete NC[b]);return d};function QC(a,b,c){c=c===void 0?!1:c;RC().addRestriction(0,a,b,c)}function SC(a,b,c){c=c===void 0?!1:c;RC().addRestriction(1,a,b,c)}function TC(){var a=Om();return RC().getRestrictions(1,a)}var UC=function(){this.container={};this.C={}},VC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
UC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=VC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
UC.prototype.getRestrictions=function(a,b){var c=VC(this,b);if(a===0){var d,e;return[].concat(ya((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ya((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ya((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ya((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
UC.prototype.getExternalRestrictions=function(a,b){var c=VC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};UC.prototype.removeExternalRestrictions=function(a){var b=VC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function RC(){return Ip("r",function(){return new UC})};function WC(a,b,c,d){var e=Lf[a],f=XC(a,b,c,d);if(!f)return null;var g=Zf(e[lf.xl],c,[]);if(g&&g.length){var h=g[0];f=WC(h.index,{onSuccess:f,onFailure:h.Tl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function XC(a,b,c,d){function e(){function w(){mo(3);var M=Db()-I;OC(c.id,f,"7");kC(c.Mc,D,"exception",M);F(109)&&UA(c,f,nA.O.Ki);G||(G=!0,h())}if(f[lf.eo])h();else{var y=Yf(f,c,[]),z=y[lf.Mm];if(z!=null)for(var B=0;B<z.length;B++)if(!wp(z[B])){h();return}var D=jC(c.Mc,String(f[lf.Ra]),Number(f[lf.mh]),y[lf.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=Db()-I;OC(c.id,Lf[a],"5");kC(c.Mc,D,"success",M);F(109)&&UA(c,f,nA.O.Mi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=Db()-
I;OC(c.id,Lf[a],"6");kC(c.Mc,D,"failure",M);F(109)&&UA(c,f,nA.O.Li);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);OC(c.id,f,"1");F(109)&&TA(c,f);var I=Db();try{$f(y,{event:c,index:a,type:1})}catch(M){w(M)}F(109)&&UA(c,f,nA.O.El)}}var f=Lf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Zf(f[lf.Fl],c,[]);if(n&&n.length){var p=n[0],q=WC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Tl===
2?m:q}if(f[lf.pl]||f[lf.fo]){var r=f[lf.pl]?Mf:c.zq,t=g,u=h;if(!r[a]){var v=YC(a,r,Gb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function YC(a,b,c){var d=[],e=[];b[a]=ZC(d,e,c);return{onSuccess:function(){b[a]=$C;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=aD;for(var f=0;f<e.length;f++)e[f]()}}}function ZC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function $C(a){a()}function aD(a,b){b()};var dD=function(a,b){for(var c=[],d=0;d<Lf.length;d++)if(a[d]){var e=Lf[d];var f=mC(b.Mc);try{var g=WC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[lf.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Nf[h];c.push({Cm:d,priorityOverride:(m?m.priorityOverride||0:0)||gC(e[lf.Ra],1)||0,execute:g})}else bD(d,b),f()}catch(p){f()}}c.sort(cD);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function eD(a,b){if(!dC)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=eC(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=mC(b);try{d[e](a,f)}catch(g){f()}}return!0}function cD(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Cm,h=b.Cm;f=g>h?1:g<h?-1:0}return f}
function bD(a,b){if(tl){var c=function(d){var e=b.isBlocked(Lf[d])?"3":"4",f=Zf(Lf[d][lf.xl],b,[]);f&&f.length&&c(f[0].index);OC(b.id,Lf[d],e);var g=Zf(Lf[d][lf.Fl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var fD=!1,dC;function gD(){dC||(dC=new cC);return dC}
function hD(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(F(109)){}if(d==="gtm.js"){if(fD)return!1;fD=!0}var e=!1,f=TC(),g=pd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}DC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:iD(g,e),zq:[],logMacroError:function(){N(6);mo(0)},cachedModelValues:jD(),Mc:new iC(function(){if(F(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};F(120)&&tl&&(n.reportMacroDiscrepancy=IC);F(109)&&QA(n.id);var p=eg(n);F(109)&&RA(n.id);e&&(p=kD(p));F(109)&&PA(b);var q=dD(p,n),r=eD(a,n.Mc);nC(n.Mc);d!=="gtm.js"&&d!=="gtm.sync"||tC();return lD(p,q)||r}function jD(){var a={};a.event=Jk("event",1);a.ecommerce=Jk("ecommerce",1);a.gtm=Jk("gtm");a.eventModel=Jk("eventModel");return a}
function iD(a,b){var c=WB();return function(d){if(c(d))return!0;var e=d&&d[lf.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Om();f=RC().getRestrictions(0,g);var h=a;b&&(h=pd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=uk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function kD(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Lf[c][lf.Ra]);if(hk[d]||Lf[c][lf.ho]!==void 0||gC(d,2))b[c]=!0}return b}function lD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Lf[c]&&!ik[String(Lf[c][lf.Ra])])return!0;return!1};function mD(){gD().addListener("gtm.init",function(a,b){Zj.ia=!0;Yn();b()})};var nD=!1,oD=0,pD=[];function qD(a){if(!nD){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){nD=!0;for(var e=0;e<pD.length;e++)Qc(pD[e])}pD.push=function(){for(var f=Ca.apply(0,arguments),g=0;g<f.length;g++)Qc(f[g]);return 0}}}function rD(){if(!nD&&oD<140){oD++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");qD()}catch(c){x.setTimeout(rD,50)}}}
function sD(){var a=x;nD=!1;oD=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")qD();else{Oc(A,"DOMContentLoaded",qD);Oc(A,"readystatechange",qD);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&rD()}Oc(a,"load",qD)}}function tD(a){nD?a():pD.push(a)};var uD={},vD={};function wD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={uj:void 0,aj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.uj=Sp(g,b),e.uj){var h=Nm();sb(h,function(r){return function(t){return r.uj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=uD[g]||[];e.aj={};m.forEach(function(r){return function(t){r.aj[t]=!0}}(e));for(var n=Pm(),p=0;p<n.length;p++)if(e.aj[n[p]]){c=c.concat(Nm());break}var q=vD[g]||[];q.length&&(c=c.concat(q))}}return{oj:c,Sp:d}}
function xD(a){wb(uD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function yD(a){wb(vD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var zD=!1,AD=!1;function BD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=pd(b,null),b[K.m.ef]&&(d.eventCallback=b[K.m.ef]),b[K.m.Lg]&&(d.eventTimeout=b[K.m.Lg]));return d}function CD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Lp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function DD(a,b){var c=a&&a[K.m.pd];c===void 0&&(c=Ek(K.m.pd,2),c===void 0&&(c="default"));if(pb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?pb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=wD(d,b.isGtmEvent),f=e.oj,g=e.Sp;if(g.length)for(var h=ED(a),m=0;m<g.length;m++){var n=Sp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=Hm().destination[q];r&&r.state===0||bC(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{oj:Tp(f,b.isGtmEvent),
Ao:Tp(t,b.isGtmEvent)}}}var FD=void 0,GD=void 0;function HD(a,b,c){var d=pd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&N(136);var e=pd(b,null);pd(c,e);fx(bx(Pm()[0],e),a.eventId,d)}function ED(a){for(var b=l([K.m.rd,K.m.sc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Sq.C[d];if(e)return e}}
var ID={config:function(a,b){var c=CD(a,b);if(!(a.length<2)&&pb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!od(a[2])||a.length>3)return;d=a[2]}var e=Sp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Lm.qe){var m=Rm(Gm());if($m(m)){var n=m.parent,p=n.isDestination;h={Vp:Rm(n),Op:p};break a}}h=void 0}var q=h;q&&(f=q.Vp,g=q.Op);DC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Nm().indexOf(r)===-1:Pm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Ic]){var u=ED(d);if(t)bC(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;FD?HD(b,v,FD):GD||(GD=pd(v,null))}else ZB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(N(128),g&&N(130),b.inheritParentConfig)){var w;var y=d;GD?(HD(b,GD,y),w=!1):(!y[K.m.ud]&&kk&&FD||(FD=pd(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}ul&&(Np===1&&(Rn.mcc=!1),Np=2);if(kk&&!t&&!d[K.m.ud]){var z=AD;AD=!0;if(z)return}zD||N(43);if(!b.noTargetGroup)if(t){yD(e.id);
var B=e.id,D=d[K.m.Og]||"default";D=String(D).split(",");for(var G=0;G<D.length;G++){var I=vD[D[G]]||[];vD[D[G]]=I;I.indexOf(B)<0&&I.push(B)}}else{xD(e.id);var M=e.id,T=d[K.m.Og]||"default";T=T.toString().split(",");for(var ea=0;ea<T.length;ea++){var Q=uD[T[ea]]||[];uD[T[ea]]=Q;Q.indexOf(M)<0&&Q.push(M)}}delete d[K.m.Og];var W=b.eventMetadata||{};W.hasOwnProperty(P.A.yd)||(W[P.A.yd]=!b.fromContainerExecution);b.eventMetadata=W;delete d[K.m.ef];for(var ja=t?[e.id]:Nm(),ka=0;ka<ja.length;ka++){var Y=
d,X=ja[ka],ha=pd(b,null),wa=Sp(X,ha.isGtmEvent);wa&&Sq.push("config",[Y],wa,ha)}}}}},consent:function(a,b){if(a.length===3){N(39);var c=CD(a,b),d=a[1],e={},f=Po(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.sg?Array.isArray(h)?NaN:Number(h):g===K.m.hc?(Array.isArray(h)?h:[h]).map(Qo):Ro(h)}b.fromContainerExecution||(e[K.m.V]&&N(139),e[K.m.La]&&N(140));d==="default"?sp(e):d==="update"?up(e,c):d==="declare"&&b.fromContainerExecution&&rp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&pb(c)){var d=void 0;if(a.length>2){if(!od(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=BD(c,d),f=CD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=DD(d,b);if(m){for(var n=m.oj,p=m.Ao,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Nm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}DC(g,
c);for(var y=l(t),z=y.next();!z.done;z=y.next()){var B=z.value,D=pd(b,null),G=pd(d,null);delete G[K.m.ef];var I=D.eventMetadata||{};I.hasOwnProperty(P.A.yd)||(I[P.A.yd]=!D.fromContainerExecution);I[P.A.Gi]=q.slice();I[P.A.Qf]=r.slice();D.eventMetadata=I;Tq(c,G,B,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.pd]=q.join(","):delete e.eventModel[K.m.pd];zD||N(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[P.A.Cl]&&(b.noGtmEvent=!0);e.eventModel[K.m.Hc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){N(53);if(a.length===4&&pb(a[1])&&pb(a[2])&&ob(a[3])){var c=Sp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){zD||N(43);var f=ED();if(sb(Nm(),function(h){return c.destinationId===h})){CD(a,b);var g={};pd((g[K.m.Fc]=d,g[K.m.jd]=e,g),null);Uq(d,function(h){Qc(function(){e(h)})},c.id,b)}else bC(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){zD=!0;var c=CD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&pb(a[1])&&ob(a[2])){if(kg(a[1],a[2]),N(74),a[1]==="all"){N(75);var b=!1;try{b=a[2](ng.ctid,"unknown",{})}catch(c){}b||N(76)}}else N(73)},set:function(a,b){var c=void 0;a.length===2&&od(a[1])?c=pd(a[1],null):a.length===3&&pb(a[1])&&(c={},od(a[2])||Array.isArray(a[2])?c[a[1]]=pd(a[2],null):c[a[1]]=a[2]);if(c){var d=CD(a,b),e=d.eventId,f=d.priorityId;
pd(c,null);var g=pd(c,null);Sq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},JD={policy:!0};var LD=function(a){if(KD(a))return a;this.value=a};LD.prototype.getUntrustedMessageValue=function(){return this.value};var KD=function(a){return!a||md(a)!=="object"||od(a)?!1:"getUntrustedMessageValue"in a};LD.prototype.getUntrustedMessageValue=LD.prototype.getUntrustedMessageValue;var MD=!1,ND=[];function OD(){if(!MD){MD=!0;for(var a=0;a<ND.length;a++)Qc(ND[a])}}function PD(a){MD?Qc(a):ND.push(a)};var QD=0,RD={},SD=[],TD=[],UD=!1,VD=!1;function WD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function XD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return YD(a)}function ZD(a,b){if(!qb(b)||b<0)b=0;var c=Hp[gk],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function $D(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(xb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function aE(){var a;if(TD.length)a=TD.shift();else if(SD.length)a=SD.shift();else return;var b;var c=a;if(UD||!$D(c.message))b=c;else{UD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Lp(),f=Lp(),c.message["gtm.uniqueEventId"]=Lp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};SD.unshift(n,c);b=h}return b}
function bE(){for(var a=!1,b;!VD&&(b=aE());){VD=!0;delete Bk.eventModel;Dk();var c=b,d=c.message,e=c.messageContext;if(d==null)VD=!1;else{e.fromContainerExecution&&Ik();try{if(ob(d))try{d.call(Fk)}catch(G){}else if(Array.isArray(d)){if(pb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Ek(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(G){}}}else{var n=void 0;if(xb(d))a:{if(d.length&&pb(d[0])){var p=ID[d[0]];if(p&&(!e.fromContainerExecution||!JD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var w=v.value;w!=="_clear"&&(t&&Hk(w),Hk(w,r[w]))}rk||(rk=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Lp(),r["gtm.uniqueEventId"]=y,Hk("gtm.uniqueEventId",y)),q=hD(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&Dk(!0);var z=d["gtm.uniqueEventId"];if(typeof z==="number"){for(var B=RD[String(z)]||[],D=0;D<B.length;D++)TD.push(cE(B[D]));B.length&&TD.sort(WD);
delete RD[String(z)];z>QD&&(QD=z)}VD=!1}}}return!a}
function dE(){if(F(109)){var a=!Zj.la;}var c=bE();if(F(109)){}try{var e=ng.ctid,f=x[gk].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function ix(a){if(QD<a.notBeforeEventId){var b=String(a.notBeforeEventId);RD[b]=RD[b]||[];RD[b].push(a)}else TD.push(cE(a)),TD.sort(WD),Qc(function(){VD||bE()})}function cE(a){return{message:a.message,messageContext:a.messageContext}}
function eE(){function a(f){var g={};if(KD(f)){var h=f;f=KD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Bc(gk,[]),c=Hp[gk]=Hp[gk]||{};c.pruned===!0&&N(83);RD=gx().get();hx();tD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});PD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Hp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new LD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});SD.push.apply(SD,h);var m=d.apply(b,f),n=Math.max(100,Number(Ri(1,'1000'))||300);if(this.length>n)for(N(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return bE()&&p};var e=b.slice(0).map(function(f){return a(f)});SD.push.apply(SD,e);if(!Zj.la){if(F(109)){}Qc(dE)}}var YD=function(a){return x[gk].push(a)};function fE(a){YD(a)};function gE(){var a,b=el(x.location.href);(a=b.hostname+b.pathname)&&Un("dl",encodeURIComponent(a));var c;var d=ng.ctid;if(d){var e=Lm.qe?1:0,f,g=Rm(Gm());f=g&&g.context;c=d+";"+ng.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Un("tdp",h);var m=Ul(!0);m!==void 0&&Un("frm",String(m))};var hE={},iE=void 0;
function jE(){if(bp()||ul)Un("csp",function(){return Object.keys(hE).join("~")||void 0},!1),x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){N(179);var b=sm(a.effectiveDirective);if(b){var c;var d=qm(b,a.blockedURI);c=d?om[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=
n.value;if(!p.wm){p.wm=!0;if(F(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(bp()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(bp()){var u=hp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;ap(u)}}}kE(p.endpoint)}}rm(b,a.blockedURI)}}}}})}
function kE(a){var b=String(a);hE.hasOwnProperty(b)||(hE[b]=!0,Vn("csp",!0),iE===void 0&&F(171)&&(iE=x.setTimeout(function(){if(F(171)){var c=Rn.csp;Rn.csp=!0;Rn.seq=!1;var d=Wn(!1);Rn.csp=c;Rn.seq=!0;Jc(d+"&script=1")}iE=void 0},500)))};function lE(){var a;var b=Qm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Un("pcid",e)};var mE=/^(https?:)?\/\//;
function nE(){var a=Sm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=dd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(mE,"")===d.replace(mE,""))){b=g;break a}}N(146)}else N(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Un("rtg",String(a.canonicalContainerId)),Un("slo",String(p)),Un("hlo",a.htmlLoadOrder||"-1"),
Un("lst",String(a.loadScriptType||"0")))}else N(144)};function oE(){var a=[],b=Number('')||0,c=Number('0.0')||0;c||(c=b/100);var d=function(){var B=!1;return B}();a.push({Ee:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,Dd:0});var e=Number('')||
0,f=Number('0.001')||0;f||(f=e/100);var g=function(){var B=!1;return B}();a.push({Ee:219,studyId:219,experimentId:104948811,controlId:104948812,controlId2:0,probability:f,active:g,Dd:0});var h=Number('')||
0,m=Number('1')||0;m||(m=h/100);var n=function(){var B=!1;return B}();a.push({Ee:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:m,active:n,Dd:0});var p=
Number('')||0,q=Number('0')||0;q||(q=p/100);var r=function(){var B=!1;return B}();a.push({Ee:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:q,active:r,Dd:0});var t=Number('')||
0,u=Number('0.01')||0;u||(u=t/100);var v=function(){var B=!1;return B}();a.push({Ee:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:u,active:v,Dd:1});var w=Number('')||0,y=Number('0.01')||0;y||(y=w/100);var z=
function(){var B=!1;return B}();a.push({Ee:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:y,active:z,Dd:0});return a};var pE={};function qE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Zj.R.H.add(Number(c.value))}function rE(a){var b=Jn(En.Z.ql);return!!oi[a].active||oi[a].probability>.5||!!(b.exp||{})[oi[a].experimentId]||!!oi[a].active||oi[a].probability>.5||!!(pE.exp||{})[oi[a].experimentId]}
function sE(){for(var a=l(oE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Ee;oi[d]=c;if(c.Dd===1){var e=d,f=Jn(En.Z.ql);si(f,e);qE(f);rE(e)&&E(e)}else if(c.Dd===0){var g=d,h=pE;si(h,g);qE(h);rE(g)&&E(g)}}};

function NE(){};var OE=function(){};OE.prototype.toString=function(){return"undefined"};var PE=new OE;function WE(){F(212)&&nk&&(kg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),QC(Om(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return gC(d,5)||!(!Nf[d]||!Nf[d][5])||c.includes("cmpPartners")}))};function XE(a,b){function c(g){var h=el(g),m=Zk(h,"protocol"),n=Zk(h,"host",!0),p=Zk(h,"port"),q=Zk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function YE(a){return ZE(a)?1:0}
function ZE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=pd(a,{});pd({arg1:c[d],any_of:void 0},e);if(YE(e))return!0}return!1}switch(a["function"]){case "_cn":return Tg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Og.length;g++){var h=Og[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Pg(b,c);case "_eq":return Ug(b,c);case "_ge":return Vg(b,c);case "_gt":return Xg(b,c);case "_lc":return Qg(b,c);case "_le":return Wg(b,
c);case "_lt":return Yg(b,c);case "_re":return Sg(b,c,a.ignore_case);case "_sw":return Zg(b,c);case "_um":return XE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var $E=function(a,b,c,d){jr.call(this);this.hh=b;this.Mf=c;this.pb=d;this.Sa=new Map;this.ih=0;this.la=new Map;this.Ea=new Map;this.R=void 0;this.H=a};va($E,jr);$E.prototype.N=function(){delete this.C;this.Sa.clear();this.la.clear();this.Ea.clear();this.R&&(fr(this.H,"message",this.R),delete this.R);delete this.H;delete this.pb;jr.prototype.N.call(this)};
var aF=function(a){if(a.C)return a.C;a.Mf&&a.Mf(a.H)?a.C=a.H:a.C=Tl(a.H,a.hh);var b;return(b=a.C)!=null?b:null},cF=function(a,b,c){if(aF(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.la.get(b);if(e&&e.nj){bF(a);var f=++a.ih;a.Ea.set(f,{Fh:e.Fh,Ro:e.am(c),persistent:b==="addEventListener"});a.C.postMessage(e.nj(c,f),"*")}}},bF=function(a){a.R||(a.R=function(b){try{var c;c=a.pb?a.pb(b):void 0;if(c){var d=c.Yp,e=a.Ea.get(d);if(e){e.persistent||a.Ea.delete(d);var f;(f=e.Fh)==null||f.call(e,
e.Ro,c.payload)}}}catch(g){}},er(a.H,"message",a.R))};var dF=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},eF=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},fF={am:function(a){return a.listener},nj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Fh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},gF={am:function(a){return a.listener},nj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Fh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function hF(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Yp:b.__gppReturn.callId}}
var iF=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;jr.call(this);this.caller=new $E(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},hF);this.caller.Sa.set("addEventListener",dF);this.caller.la.set("addEventListener",fF);this.caller.Sa.set("removeEventListener",eF);this.caller.la.set("removeEventListener",gF);this.timeoutMs=c!=null?c:500};va(iF,jr);iF.prototype.N=function(){this.caller.dispose();jr.prototype.N.call(this)};
iF.prototype.addEventListener=function(a){var b=this,c=wl(function(){a(jF,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);cF(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(kF,!0);return}a(lF,!0)}}})};
iF.prototype.removeEventListener=function(a){cF(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var lF={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},jF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},kF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function mF(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Pv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Pv.C=d}}function nF(){try{var a=new iF(x,{timeoutMs:-1});aF(a.caller)&&a.addEventListener(mF)}catch(b){}};function oF(){var a=[["cv",Pi(1)],["rv",ek],["tc",Lf.filter(function(b){return b}).length]];fk&&a.push(["x",fk]);wk()&&a.push(["tag_exp",wk()]);return a};var pF={},qF={};function Ti(a){pF[a]=(pF[a]||0)+1}function Ui(a){qF[a]=(qF[a]||0)+1}function rF(a,b){for(var c=[],d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;c.push(f+"."+b[f])}return c.length===0?[]:[[a,c.join("~")]]}function sF(){return rF("bdm",pF)}function tF(){return rF("vcm",qF)};var uF={},vF={};function wF(a){var b=a.eventId,c=a.Rd,d=[],e=uF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=vF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete uF[b],delete vF[b]);return d};function xF(){return!1}function yF(){var a={};return function(b,c,d){}};function zF(){var a=AF;return function(b,c,d){var e=d&&d.event;BF(c);var f=Eh(b)?void 0:1,g=new ab;wb(c,function(r,t){var u=Ed(t,void 0,f);u===void 0&&t!==void 0&&N(44);g.set(r,u)});a.Mb(cg());var h={Ml:rg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Tf:e!==void 0?function(r){e.Mc.Tf(r)}:void 0,Ib:function(){return b},log:function(){},ap:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},jq:!!gC(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(xF()){var m=yF(),n,p;h.ub={Dj:[],Uf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Dh:Wh()};h.log=function(r){var t=Ca.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=bf(a,h,[b,g]);a.Mb();q instanceof Fa&&(q.type==="return"?q=q.data:q=void 0);return C(q,void 0,f)}}function BF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ob(b)&&(a.gtmOnSuccess=function(){Qc(b)});ob(c)&&(a.gtmOnFailure=function(){Qc(c)})};function CF(a){}CF.M="internal.addAdsClickIds";function DF(a,b){var c=this;}DF.publicName="addConsentListener";var EF=!1;function FF(a){for(var b=0;b<a.length;++b)if(EF)try{a[b]()}catch(c){N(77)}else a[b]()}function GF(a,b,c){var d=this,e;if(!ph(a)||!lh(b)||!qh(c))throw H(this.getName(),["string","function","string|undefined"],arguments);FF([function(){J(d,"listen_data_layer",a)}]);e=gD().addListener(a,C(b),c===null?void 0:c);return e}GF.M="internal.addDataLayerEventListener";function HF(a,b,c){}HF.publicName="addDocumentEventListener";function IF(a,b,c,d){}IF.publicName="addElementEventListener";function JF(a){return a.K.sb()};function KF(a){}KF.publicName="addEventCallback";
var LF=function(a){return typeof a==="string"?a:String(Lp())},OF=function(a,b){MF(a,"init",!1)||(NF(a,"init",!0),b())},MF=function(a,b,c){var d=PF(a);return Fb(d,b,c)},QF=function(a,b,c,d){var e=PF(a),f=Fb(e,b,d);e[b]=c(f)},NF=function(a,b,c){PF(a)[b]=c},PF=function(a){var b=Ip("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},RF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":ad(a,"className"),"gtm.elementId":a.for||Rc(a,"id")||"","gtm.elementTarget":a.formTarget||
ad(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||ad(a,"href")||a.src||a.code||a.codebase||"";return d};
var UF=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e],h=g.tagName.toLowerCase();if(!(SF.indexOf(h)<0||h==="input"&&TF.indexOf(g.type.toLowerCase())>=0)){if(g.dataset[c]===d)return f;f++}}return 0},VF=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:A.getElementById(a.form)}return Uc(a,["form"],100)},SF=["input","select","textarea"],TF=["button","hidden","image","reset","submit"];
function ZF(a){}ZF.M="internal.addFormAbandonmentListener";function $F(a,b,c,d){}
$F.M="internal.addFormData";var aG={},bG=[],cG={},dG=0,eG=0;
var gG=function(){Oc(A,"change",function(a){for(var b=0;b<bG.length;b++)bG[b](a)});Oc(x,"pagehide",function(){fG()})},fG=function(){wb(cG,function(a,b){var c=aG[a];c&&wb(b,function(d,e){hG(e,c)})})},kG=function(a,b){var c=""+a;if(aG[c])aG[c].push(b);else{var d=[b];aG[c]=d;var e=cG[c];e||(e={},cG[c]=e);bG.push(function(f){var g=f.target;if(g){var h=VF(g);if(h){var m=iG(h,"gtmFormInteractId",function(){return dG++}),n=iG(g,"gtmFormInteractFieldId",function(){return eG++}),p=e[m];p?(p.Ua&&(x.clearTimeout(p.Ua),
p.fc.dataset.gtmFormInteractFieldId!==n&&hG(p,d)),p.fc=g,jG(p,d,a)):(e[m]={form:h,fc:g,sequenceNumber:0,Ua:null},jG(e[m],d,a))}}})}},hG=function(a,b){var c=a.form,d=a.fc,e=RF(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=UF(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Ua=null},jG=function(a,b,c){c?a.Ua=x.setTimeout(function(){hG(a,b)},c):hG(a,b)},iG=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function lG(a,b){if(!lh(a)||!jh(b))throw H(this.getName(),["function","Object|undefined"],arguments);var c=C(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=C(a),f;MF("pix.fil","init")?f=MF("pix.fil","reg"):(gG(),f=kG,NF("pix.fil","reg",kG),NF("pix.fil","init",!0));f(d,e);}lG.M="internal.addFormInteractionListener";
var nG=function(a,b,c){var d=RF(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&mG(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},oG=function(a,b){var c=MF("pix.fsl",a?"nv.mwt":"mwt",0);x.setTimeout(b,c)},pG=function(a,b,c,d,e){var f=MF("pix.fsl",c?"nv.mwt":"mwt",0),g=MF("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=nG(a,c,e);N(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return N(122),!0;if(d&&f){for(var m=Ob(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},qG=function(){var a=[],b=function(c){return sb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
mG=function(a){var b=ad(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},rG=function(){var a=qG(),b=HTMLFormElement.prototype.submit;Oc(A,"click",function(c){var d=c.target;if(d){var e=Uc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Rc(e,"value")){var f=VF(e);f&&a.store(f,e)}}},!1);Oc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=mG(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=A.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),oc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&oc(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(pG(d,m,e,f,g))return h=!1,c.returnValue;oG(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};pG(c,e,!1,mG(c))?(b.call(c),d=!1):oG(!1,e)}};
function sG(a,b){if(!lh(a)||!jh(b))throw H(this.getName(),["function","Object|undefined"],arguments);var c=C(b,this.K,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=C(a,this.K,1);if(d){var h=function(n){return Math.max(e,n)};QF("pix.fsl","mwt",h,0);f||QF("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};QF("pix.fsl","runIfUncanceled",m,[]);f||QF("pix.fsl","runIfCanceled",
m,[]);MF("pix.fsl","init")||(rG(),NF("pix.fsl","init",!0));}sG.M="internal.addFormSubmitListener";
function xG(a){}xG.M="internal.addGaSendListener";function yG(a){if(!a)return{};var b=a.ap;return fC(b.type,b.index,b.name)}function zG(a){return a?{originatingEntity:yG(a)}:{}};
var BG=function(a,b,c){AG().updateZone(a,b,c)},DG=function(a,b,c,d,e,f){var g=AG();c=c&&Ib(c,CG);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,ng.ctid,h)){var p=n,q=a,r=d,t=e,u=f;if(Jb(p,"GTM-"))ZB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=ax("js",Cb());ZB(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};fx(v,q,w);fx(bx(p,r),q,w)}}}return h},AG=function(){return Ip("zones",function(){return new EG})},
FG={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},CG={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},EG=function(){this.C={};this.H={};this.N=0};k=EG.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.tj],b))return!1;for(var e=0;e<c.qg.length;e++)if(this.H[c.qg[e]].ze(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.qg.length;f++){var g=this.H[c.qg[f]];g.ze(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.tj],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].N(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.N);this.H[c]=new GG(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.P(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&Hp[a]||!d&&Wm(a)||d&&d.tj!==b)return!1;if(d)return d.qg.push(c),!1;this.C[a]={tj:b,qg:[c]};return!0};var GG=function(a,b){this.H=null;this.C=[{eventId:a,ze:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};GG.prototype.P=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.ze!==b&&this.C.push({eventId:a,ze:b})};GG.prototype.ze=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].ze;return!1};GG.prototype.N=function(a,b){b=b||[];if(!this.H||FG[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function HG(a){var b=Hp.zones;return b?b.getIsAllowedFn(Pm(),a):function(){return!0}}function IG(){var a=Hp.zones;a&&a.unregisterChild(Pm())}
function JG(){SC(Om(),function(a){var b=Hp.zones;return b?b.isActive(Pm(),a.originalEventData["gtm.uniqueEventId"]):!0});QC(Om(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return HG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var KG=function(a,b){this.tagId=a;this.we=b};
function LG(a,b){var c=this;return a}LG.M="internal.loadGoogleTag";function MG(a){return new wd("",function(b){var c=this.evaluate(b);if(c instanceof wd)return new wd("",function(){var d=Ca.apply(0,arguments),e=this,f=pd(JF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Pd(f);return c.Kb.apply(c,[h].concat(ya(g)))})})};function NG(a,b,c){var d=this;}NG.M="internal.addGoogleTagRestriction";var OG={},PG=[];
function WG(a,b){}
WG.M="internal.addHistoryChangeListener";function XG(a,b,c){}XG.publicName="addWindowEventListener";function YG(a,b){return!0}YG.publicName="aliasInWindow";function ZG(a,b,c){}ZG.M="internal.appendRemoteConfigParameter";function $G(a){var b;return b}
$G.publicName="callInWindow";function aH(a){}aH.publicName="callLater";function bH(a){}bH.M="callOnDomReady";function cH(a){}cH.M="callOnWindowLoad";function dH(a,b){var c;return c}dH.M="internal.computeGtmParameter";function eH(a,b){var c=this;}eH.M="internal.consentScheduleFirstTry";function fH(a,b){var c=this;}fH.M="internal.consentScheduleRetry";function gH(a){var b;return b}gH.M="internal.copyFromCrossContainerData";function hH(a,b){var c;var d=Ed(c,this.K,Eh(JF(this).Ib())?2:1);d===void 0&&c!==void 0&&N(45);return d}hH.publicName="copyFromDataLayer";
function iH(a){var b=void 0;return b}iH.M="internal.copyFromDataLayerCache";function jH(a){var b;return b}jH.publicName="copyFromWindow";function kH(a){var b=void 0;return Ed(b,this.K,1)}kH.M="internal.copyKeyFromWindow";var lH=function(a){return a===cn.X.Ga&&vn[a]===bn.Ja.pe&&!wp(K.m.U)};var mH=function(){return"0"},nH=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];F(102)&&b.push("gbraid");return fl(a,b,"0")};var oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH={},yH={},zH={},AH={},BH={},CH={},DH={},EH={},FH={},GH={},HH={},IH={},JH={},KH={},LH={},MH={},NH={},OH=(NH[K.m.Ma]=(oH[2]=[lH],oH),NH[K.m.tf]=(pH[2]=[lH],pH),NH[K.m.ff]=(qH[2]=[lH],qH),NH[K.m.li]=(rH[2]=[lH],rH),NH[K.m.mi]=(sH[2]=[lH],sH),NH[K.m.ni]=(tH[2]=[lH],tH),NH[K.m.oi]=(uH[2]=[lH],uH),NH[K.m.ri]=(vH[2]=[lH],vH),NH[K.m.uc]=(wH[2]=[lH],wH),NH[K.m.uf]=(xH[2]=[lH],xH),NH[K.m.vf]=(yH[2]=[lH],yH),NH[K.m.wf]=(zH[2]=[lH],zH),NH[K.m.xf]=(AH[2]=
[lH],AH),NH[K.m.yf]=(BH[2]=[lH],BH),NH[K.m.zf]=(CH[2]=[lH],CH),NH[K.m.Af]=(DH[2]=[lH],DH),NH[K.m.Bf]=(EH[2]=[lH],EH),NH[K.m.xb]=(FH[1]=[lH],FH),NH[K.m.Xc]=(GH[1]=[lH],GH),NH[K.m.ed]=(HH[1]=[lH],HH),NH[K.m.be]=(IH[1]=[lH],IH),NH[K.m.Qe]=(JH[1]=[function(a){return F(102)&&lH(a)}],JH),NH[K.m.fd]=(KH[1]=[lH],KH),NH[K.m.Ca]=(LH[1]=[lH],LH),NH[K.m.Xa]=(MH[1]=[lH],MH),NH),PH={},QH=(PH[K.m.xb]=mH,PH[K.m.Xc]=mH,PH[K.m.ed]=mH,PH[K.m.be]=mH,PH[K.m.Qe]=mH,PH[K.m.fd]=function(a){if(!od(a))return{};var b=pd(a,
null);delete b.match_id;return b},PH[K.m.Ca]=nH,PH[K.m.Xa]=nH,PH),RH={},SH={},TH=(SH[P.A.hb]=(RH[2]=[lH],RH),SH),UH={};var VH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};VH.prototype.getValue=function(a){a=a===void 0?cn.X.Gb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};VH.prototype.H=function(){return md(this.C)==="array"||od(this.C)?pd(this.C,null):this.C};
var WH=function(){},XH=function(a,b){this.conditions=a;this.C=b},YH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new VH(c,e,g,a.C[b]||WH)},ZH,$H;var aI=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},ew=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,P.A.Rf))},U=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(ZH!=null||(ZH=new XH(OH,QH)),e=YH(ZH,b,c));d[b]=e};
aI.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return U(this,a,b),!0;if(!od(c))return!1;U(this,a,ma(Object,"assign").call(Object,c,b));return!0};var bI=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
aI.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(pb(d)&&c!==void 0&&F(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===P.A.Rf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,P.A.Rf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:($H!=null||($H=new XH(TH,UH)),e=YH($H,b,c));d[b]=e},cI=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},yw=function(a,b,c){var d=mx(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function dI(a,b){var c;if(!ih(a)||!jh(b))throw H(this.getName(),["Object","Object|undefined"],arguments);var d=C(b)||{},e=C(a,this.K,1).Bb(),f=e.D;d.omitEventContext&&(f=vq(new kq(e.D.eventId,e.D.priorityId)));var g=new aI(e.target,e.eventName,f);if(!d.omitHitData)for(var h=bI(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;U(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=cI(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;S(g,u,q[u])}g.isAborted=e.isAborted;c=Ed(Vw(g),this.K,1);return c}dI.M="internal.copyPreHit";function eI(a,b){var c=null;return Ed(c,this.K,2)}eI.publicName="createArgumentsQueue";function fI(a){return Ed(function(c){var d=pC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
pC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}fI.M="internal.createGaCommandQueue";function gI(a){return Ed(function(){if(!ob(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Eh(JF(this).Ib())?2:1)}gI.publicName="createQueue";function hI(a,b){var c=null;return c}hI.M="internal.createRegex";function iI(a){}iI.M="internal.declareConsentState";function jI(a){var b="";return b}jI.M="internal.decodeUrlHtmlEntities";function kI(a,b,c){var d;return d}kI.M="internal.decorateUrlWithGaCookies";function lI(){}lI.M="internal.deferCustomEvents";function mI(a){var b;J(this,"detect_user_provided_data","auto");var c=C(a)||{},d=Mx({Ae:!!c.includeSelector,Be:!!c.includeVisibility,Wf:c.excludeElementSelectors,Zb:c.fieldFilters,Gh:!!c.selectMultipleElements});b=new ab;var e=new sd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(nI(f[g]));d.vj!==void 0&&b.set("preferredEmailElement",nI(d.vj));b.set("status",d.status);if(F(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(xc&&
xc.userAgent||"")){}return b}
var oI=function(a){switch(a){case Kx.jc:return"email";case Kx.Cd:return"phone_number";case Kx.vd:return"first_name";case Kx.Bd:return"last_name";case Kx.Ji:return"street";case Kx.Jh:return"city";case Kx.Fi:return"region";case Kx.Of:return"postal_code";case Kx.Ke:return"country"}},nI=function(a){var b=new ab;b.set("userData",a.ka);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(F(33)){}else switch(a.type){case Kx.jc:b.set("type","email")}return b};mI.M="internal.detectUserProvidedData";
function rI(a,b){return f}rI.M="internal.enableAutoEventOnClick";var uI=function(a){if(!sI){var b=function(){var c=A.body;if(c)if(tI)(new MutationObserver(function(){for(var e=0;e<sI.length;e++)Qc(sI[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Oc(c,"DOMNodeInserted",function(){d||(d=!0,Qc(function(){d=!1;for(var e=0;e<sI.length;e++)Qc(sI[e])}))})}};sI=[];A.body?b():Qc(b)}sI.push(a)},tI=!!x.MutationObserver,sI;
function zI(a,b){return p}zI.M="internal.enableAutoEventOnElementVisibility";function AI(){}AI.M="internal.enableAutoEventOnError";var BI={},CI=[],DI={},EI=0,FI=0;
var HI=function(){wb(DI,function(a,b){var c=BI[a];c&&wb(b,function(d,e){GI(e,c)})})},KI=function(a,b){var c=""+b;if(BI[c])BI[c].push(a);else{var d=[a];BI[c]=d;var e=DI[c];e||(e={},DI[c]=e);CI.push(function(f){var g=f.target;if(g){var h=VF(g);if(h){var m=II(h,"gtmFormInteractId",function(){return EI++}),n=II(g,"gtmFormInteractFieldId",function(){return FI++});if(m!==null&&n!==null){var p=e[m];p?(p.Ua&&(x.clearTimeout(p.Ua),p.fc.getAttribute("data-gtm-form-interact-field-id")!==n&&GI(p,d)),p.fc=g,JI(p,
d,b)):(e[m]={form:h,fc:g,sequenceNumber:0,Ua:null},JI(e[m],d,b))}}}})}},GI=function(a,b){var c=a.form,d=a.fc,e=RF(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
UF(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;YD(e);a.sequenceNumber++;a.Ua=null},JI=function(a,b,c){c?a.Ua=x.setTimeout(function(){GI(a,b)},c):GI(a,b)},II=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function LI(a,b){var c=this;if(!jh(a))throw H(this.getName(),["Object|undefined","any"],arguments);FF([function(){J(c,"detect_form_interaction_events")}]);var d=LF(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(MF("fil","init",!1)){var f=MF("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Oc(A,"change",function(g){for(var h=0;h<CI.length;h++)CI[h](g)}),Oc(x,"pagehide",function(){HI()}),
KI(d,e),NF("fil","reg",KI),NF("fil","init",!0);return d}LI.M="internal.enableAutoEventOnFormInteraction";
var MI=function(a,b,c,d,e){var f=MF("fsl",c?"nv.mwt":"mwt",0),g;g=c?MF("fsl","nv.ids",[]):MF("fsl","ids",[]);if(!g.length)return!0;var h=RF(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);N(121);if(m==="https://www.facebook.com/tr/")return N(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!XD(h,ZD(b,
f),f))return!1}else XD(h,function(){},f||2E3);return!0},NI=function(){var a=[],b=function(c){return sb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},OI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},PI=function(){var a=NI(),b=HTMLFormElement.prototype.submit;Oc(A,"click",function(c){var d=c.target;if(d){var e=Uc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Rc(e,"value")){var f=VF(e);f&&a.store(f,e)}}},!1);Oc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=OI(d)&&!e,g=a.get(d),h=!0;if(MI(d,function(){if(h){var m=null,n={};g&&(m=A.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),oc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
oc(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;MI(c,function(){d&&b.call(c)},!1,OI(c))&&(b.call(c),d=
!1)}};
function QI(a,b){var c=this;if(!jh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");FF([function(){J(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=LF(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};QF("fsl","mwt",h,0);e||QF("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};QF("fsl","ids",m,[]);e||QF("fsl","nv.ids",m,[]);MF("fsl","init",!1)||(PI(),NF("fsl","init",!0));return f}QI.M="internal.enableAutoEventOnFormSubmit";
function VI(){var a=this;}VI.M="internal.enableAutoEventOnGaSend";var WI={},XI=[];
var ZI=function(a,b){var c=""+b;if(WI[c])WI[c].push(a);else{var d=[a];WI[c]=d;var e=YI("gtm.historyChange-v2"),f=-1;XI.push(function(g){f>=0&&x.clearTimeout(f);b?f=x.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},YI=function(a){var b=x.location.href,c={source:null,state:x.history.state||null,url:bl(el(b)),Za:Zk(el(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.Za!==d.Za){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.Za,
"gtm.newUrlFragment":d.Za,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;YD(h)}}},$I=function(a,b){var c=x.history,d=c[a];if(ob(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=x.location.href;b({source:a,state:e,url:bl(el(h)),Za:Zk(el(h),"fragment")})}}catch(e){}},bJ=function(a){x.addEventListener("popstate",function(b){var c=aJ(b);a({source:"popstate",state:b.state,url:bl(el(c)),Za:Zk(el(c),
"fragment")})})},cJ=function(a){x.addEventListener("hashchange",function(b){var c=aJ(b);a({source:"hashchange",state:null,url:bl(el(c)),Za:Zk(el(c),"fragment")})})},aJ=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||x.location.href};
function dJ(a,b){var c=this;if(!jh(a))throw H(this.getName(),["Object|undefined","any"],arguments);FF([function(){J(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!MF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<XI.length;n++)XI[n](m)},f=LF(b),ZI(f,e),NF(d,"reg",ZI)):g=YI("gtm.historyChange");cJ(g);bJ(g);$I("pushState",
g);$I("replaceState",g);NF(d,"init",!0)}else if(d==="ehl"){var h=MF(d,"reg");h&&(f=LF(b),h(f,e))}d==="hl"&&(f=void 0);return f}dJ.M="internal.enableAutoEventOnHistoryChange";var eJ=["http://","https://","javascript:","file://"];
var fJ=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=ad(b,"href");if(c.indexOf(":")!==-1&&!eJ.some(function(h){return Jb(c,h)}))return!1;var d=c.indexOf("#"),e=ad(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=bl(el(c)),g=bl(el(x.location.href));return f!==g}return!0},gJ=function(a,b){for(var c=Zk(el((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||ad(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},hJ=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Uc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=MF("lcl",e?"nv.mwt":"mwt",0),g;g=e?MF("lcl","nv.ids",[]):MF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=MF("lcl","aff.map",{})[n];p&&!gJ(p,d)||h.push(n)}if(h.length){var q=fJ(c,d),r=RF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Sc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!sb(String(ad(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),u=x[(ad(d,"target")||"_self").substring(1)],v=!0,w=ZD(function(){var y;if(y=v&&u){var z;a:if(t){var B;try{B=new MouseEvent(c.type,{bubbles:!0})}catch(D){if(!A.createEvent){z=!1;break a}B=A.createEvent("MouseEvents");B.initEvent(c.type,!0,!0)}B.C=!0;c.target.dispatchEvent(B);z=!0}else z=!1;y=!z}y&&(u.location.href=ad(d,
"href"))},f);if(XD(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else XD(r,function(){},f||2E3);return!0}}}var b=0;Oc(A,"click",a,!1);Oc(A,"auxclick",a,!1)};
function iJ(a,b){var c=this;if(!jh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var d=C(a);FF([function(){J(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=LF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};QF("lcl","mwt",n,0);f||QF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};QF("lcl","ids",p,[]);f||QF("lcl","nv.ids",p,[]);g&&QF("lcl","aff.map",function(q){q[h]=g;return q},{});MF("lcl","init",!1)||(hJ(),NF("lcl","init",!0));return h}iJ.M="internal.enableAutoEventOnLinkClick";var jJ,kJ;
var lJ=function(a){return MF("sdl",a,{})},mJ=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];QF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},pJ=function(){function a(){nJ();oJ(a,!0)}return a},qJ=function(){function a(){f?e=x.setTimeout(a,c):(e=0,nJ(),oJ(b));f=!1}function b(){d&&jJ();e?f=!0:(e=x.setTimeout(a,c),NF("sdl","pending",!0))}var c=250,d=!1;A.scrollingElement&&A.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
oJ=function(a,b){MF("sdl","init",!1)&&!rJ()&&(b?Pc(x,"scrollend",a):Pc(x,"scroll",a),Pc(x,"resize",a),NF("sdl","init",!1))},nJ=function(){var a=jJ(),b=a.depthX,c=a.depthY,d=b/kJ.scrollWidth*100,e=c/kJ.scrollHeight*100;sJ(b,"horiz.pix","PIXELS","horizontal");sJ(d,"horiz.pct","PERCENT","horizontal");sJ(c,"vert.pix","PIXELS","vertical");sJ(e,"vert.pct","PERCENT","vertical");NF("sdl","pending",!1)},sJ=function(a,b,c,d){var e=lJ(b),f={},g;for(g in e)if(f={Fe:f.Fe},f.Fe=g,e.hasOwnProperty(f.Fe)){var h=
Number(f.Fe);if(!(a<h)){var m={};fE((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Fe].join(","),m));QF("sdl",b,function(n){return function(p){delete p[n.Fe];return p}}(f),{})}}},uJ=function(){QF("sdl","scr",function(a){a||(a=A.scrollingElement||A.body&&A.body.parentNode);return kJ=a},!1);QF("sdl","depth",function(a){a||(a=tJ());return jJ=a},!1)},tJ=function(){var a=0,b=0;return function(){var c=px(),d=c.height;
a=Math.max(kJ.scrollLeft+c.width,a);b=Math.max(kJ.scrollTop+d,b);return{depthX:a,depthY:b}}},rJ=function(){return!!(Object.keys(lJ("horiz.pix")).length||Object.keys(lJ("horiz.pct")).length||Object.keys(lJ("vert.pix")).length||Object.keys(lJ("vert.pct")).length)};
function vJ(a,b){var c=this;if(!ih(a))throw H(this.getName(),["Object","any"],arguments);FF([function(){J(c,"detect_scroll_events")}]);uJ();if(!kJ)return;var d=LF(b),e=C(a);switch(e.horizontalThresholdUnits){case "PIXELS":mJ(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":mJ(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":mJ(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":mJ(e.verticalThresholds,
d,"vert.pct")}MF("sdl","init",!1)?MF("sdl","pending",!1)||Qc(function(){nJ()}):(NF("sdl","init",!0),NF("sdl","pending",!0),Qc(function(){nJ();if(rJ()){var f=qJ();"onscrollend"in x?(f=pJ(),Oc(x,"scrollend",f)):Oc(x,"scroll",f);Oc(x,"resize",f)}else NF("sdl","init",!1)}));return d}vJ.M="internal.enableAutoEventOnScroll";function wJ(a){return function(){if(a.limit&&a.qj>=a.limit)a.zh&&x.clearInterval(a.zh);else{a.qj++;var b=Db();YD({event:a.eventName,"gtm.timerId":a.zh,"gtm.timerEventNumber":a.qj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Bm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Bm,"gtm.triggers":a.Fq})}}}
function xJ(a,b){
return f}xJ.M="internal.enableAutoEventOnTimer";
var yJ=function(a,b,c){function d(){var g=a();f+=e?(Db()-e)*g.playbackRate/1E3:0;e=Db()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.Rl,q=m?Math.round(m):h?Math.round(n.Rl*h):Math.round(n.Po),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=A.hidden?!1:qx(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var v=RF(c,"gtm.video",u);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=t;return v},lq:function(){e=Db()},Oi:function(){d()}}};var rc=Aa(["data-gtm-yt-inspected-"]),zJ=["www.youtube.com","www.youtube-nocookie.com"],AJ,BJ=!1;
var CJ=function(a,b,c){var d=a.map(function(g){return{Od:g,ym:g,km:void 0}});if(!b.length)return d;var e=b.map(function(g){return{Od:g*c,ym:void 0,km:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.Od-h.Od});return f},DJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},EJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},FJ=function(a,b){var c,d;function e(){t=yJ(function(){return{url:w,title:y,Rl:v,Po:a.getCurrentTime(),playbackRate:z}},b.Ge,a.getIframe());v=0;y=w="";z=1;return f}function f(I){switch(I){case 1:v=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var M=a.getVideoData();y=M?M.title:""}z=a.getPlaybackRate();if(b.Go){var T=t.createEvent("start");YD(T)}else t.Oi();u=CJ(b.gq,b.fq,a.getDuration());return g(I);default:return f}}function g(){B=a.getCurrentTime();D=Cb().getTime();
t.lq();r();return h}function h(I){var M;switch(I){case 0:return n(I);case 2:M="pause";case 3:var T=a.getCurrentTime()-B;M=Math.abs((Cb().getTime()-D)/1E3*z-T)>1?"seek":M||"buffering";if(a.getCurrentTime())if(b.Fo){var ea=t.createEvent(M);YD(ea)}else t.Oi();q();return m;case -1:return e(I);default:return h}}function m(I){switch(I){case 0:return n(I);case 1:return g(I);case -1:return e(I);default:return m}}function n(){for(;d;){var I=c;x.clearTimeout(d);I()}if(b.Eo){var M=t.createEvent("complete",1);
YD(M)}return e(-1)}function p(){}function q(){d&&(x.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&z!==0){var I=-1,M;do{M=u[0];if(M.Od>a.getDuration())return;I=(M.Od-a.getCurrentTime())/z;if(I<0&&(u.shift(),u.length===0))return}while(I<0);c=function(){d=0;c=p;if(u.length>0&&u[0].Od===M.Od){u.shift();var T=t.createEvent("progress",M.km,M.ym);YD(T)}r()};d=x.setTimeout(c,I*1E3)}}var t,u=[],v,w,y,z,B,D,G=e(-1);d=0;c=p;return{onStateChange:function(I){G=G(I)},onPlaybackRateChange:function(I){B=a.getCurrentTime();
D=Cb().getTime();t.Oi();z=I;q();r()}}},HJ=function(a){Qc(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)GJ(d[f],a)}var c=A;b();uI(b)})},GJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.Ge)&&(tc(a,"data-gtm-yt-inspected-"+b.Ge),IJ(a,b.Ul))){a.id||(a.id=JJ());var c=x.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=FJ(d,b),f={},g;for(g in e)f={jg:f.jg},f.jg=g,e.hasOwnProperty(f.jg)&&d.addEventListener(f.jg,function(h){return function(m){return e[h.jg](m.data)}}(f))}},
IJ=function(a,b){var c=a.getAttribute("src");if(KJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(AJ||(AJ=A.location.protocol+"//"+A.location.hostname,A.location.port&&(AJ+=":"+A.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(AJ));var f;f=Zb(d);a.src=$b(f).toString();return!0}}return!1},KJ=function(a,b){if(!a)return!1;for(var c=0;c<zJ.length;c++)if(a.indexOf("//"+zJ[c]+"/"+b)>=0)return!0;
return!1},JJ=function(){var a=""+Math.round(Math.random()*1E9);return A.getElementById(a)?JJ():a};
function LJ(a,b){var c=this;var d=function(){HJ(q)};if(!ih(a))throw H(this.getName(),["Object","any"],arguments);FF([function(){J(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=LF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=EJ(C(a.get("progressThresholdsPercent"))),n=DJ(C(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Go:f,Eo:g,Fo:h,fq:m,gq:n,Ul:p,Ge:e},r=x.YT;if(r)return r.ready&&r.ready(d),e;var t=x,u=t.onYouTubeIframeAPIReady;t.onYouTubeIframeAPIReady=function(){u&&u();d()};Qc(function(){for(var v=A.getElementsByTagName("script"),w=v.length,y=0;y<w;y++){var z=v[y].getAttribute("src");if(KJ(z,"iframe_api")||KJ(z,"player_api"))return e}for(var B=A.getElementsByTagName("iframe"),D=B.length,G=0;G<D;G++)if(!BJ&&IJ(B[G],q.Ul))return Jc("https://www.youtube.com/iframe_api"),
BJ=!0,e});return e}LJ.M="internal.enableAutoEventOnYouTubeActivity";BJ=!1;function MJ(a,b){if(!ph(a)||!jh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?C(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Lh(f,c);return e}MJ.M="internal.evaluateBooleanExpression";var NJ;function OJ(a){var b=!1;return b}OJ.M="internal.evaluateMatchingRules";function qK(){return Dr(7)&&Dr(9)&&Dr(10)};function lL(a,b,c,d){}lL.M="internal.executeEventProcessor";function mL(a){var b;return Ed(b,this.K,1)}mL.M="internal.executeJavascriptString";function nL(a){var b;return b};function oL(a){var b="";return b}oL.M="internal.generateClientId";function pL(a){var b={};return Ed(b)}pL.M="internal.getAdsCookieWritingOptions";function qL(a,b){var c=!1;return c}qL.M="internal.getAllowAdPersonalization";function rL(){var a;return a}rL.M="internal.getAndResetEventUsage";function sL(a,b){b=b===void 0?!0:b;var c;return c}sL.M="internal.getAuid";var tL=null;
function uL(){var a=new ab;J(this,"read_container_data"),F(49)&&tL?a=tL:(a.set("containerId",'G-9XY5DSYD8S'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",sg),a.set("previewMode",tg.Dm),a.set("environmentMode",tg.Xo),a.set("firstPartyServing",yk()||Zj.N),a.set("containerUrl",Ac),a.Ta(),F(49)&&(tL=a));return a}
uL.publicName="getContainerVersion";function vL(a,b){b=b===void 0?!0:b;var c;return c}vL.publicName="getCookieValues";function wL(){var a="";return a}wL.M="internal.getCorePlatformServicesParam";function xL(){return uo()}xL.M="internal.getCountryCode";function yL(){var a=[];a=Nm();return Ed(a)}yL.M="internal.getDestinationIds";function zL(a){var b=new ab;return b}zL.M="internal.getDeveloperIds";function AL(a){var b;return b}AL.M="internal.getEcsidCookieValue";function BL(a,b){var c=null;return c}BL.M="internal.getElementAttribute";function CL(a){var b=null;return b}CL.M="internal.getElementById";function DL(a){var b="";return b}DL.M="internal.getElementInnerText";function EL(a,b){var c=null;return Ed(c)}EL.M="internal.getElementProperty";function FL(a){var b;return b}FL.M="internal.getElementValue";function GL(a){var b=0;return b}GL.M="internal.getElementVisibilityRatio";function HL(a){var b=null;return b}HL.M="internal.getElementsByCssSelector";
function IL(a){var b;if(!ph(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=JF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",z=l(n),B=z.next();!B.done;B=
z.next()){var D=B.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Ed(c,this.K,1);return b}IL.M="internal.getEventData";var JL={};JL.disableUserDataWithoutCcd=F(223);JL.enableDecodeUri=F(92);JL.enableGaAdsConversions=F(122);JL.enableGaAdsConversionsClientId=F(121);JL.enableOverrideAdsCps=F(170);JL.enableUrlDecodeEventUsage=F(139);function KL(){return Ed(JL)}KL.M="internal.getFlags";function LL(){var a;return a}LL.M="internal.getGsaExperimentId";function ML(){return new Bd(PE)}ML.M="internal.getHtmlId";function NL(a){var b;return b}NL.M="internal.getIframingState";function OL(a,b){var c={};return Ed(c)}OL.M="internal.getLinkerValueFromLocation";function PL(){var a=new ab;return a}PL.M="internal.getPrivacyStrings";function QL(a,b){var c;if(!ph(a)||!ph(b))throw H(this.getName(),["string","string"],arguments);var d=mx(a)||{};c=Ed(d[b],this.K);return c}QL.M="internal.getProductSettingsParameter";function RL(a,b){var c;if(!ph(a)||!th(b))throw H(this.getName(),["string","boolean|undefined"],arguments);J(this,"get_url","query",a);var d=Zk(el(x.location.href),"query"),e=Wk(d,a,b);c=Ed(e,this.K);return c}RL.publicName="getQueryParameters";function SL(a,b){var c;return c}SL.publicName="getReferrerQueryParameters";function TL(a){var b="";return b}TL.publicName="getReferrerUrl";function UL(){return vo()}UL.M="internal.getRegionCode";function VL(a,b){var c;if(!ph(a)||!ph(b))throw H(this.getName(),["string","string"],arguments);var d=Vq(a);c=Ed(d[b],this.K);return c}VL.M="internal.getRemoteConfigParameter";function WL(){var a=new ab;a.set("width",0);a.set("height",0);return a}WL.M="internal.getScreenDimensions";function XL(){var a="";return a}XL.M="internal.getTopSameDomainUrl";function YL(){var a="";return a}YL.M="internal.getTopWindowUrl";function ZL(a){var b="";if(!qh(a))throw H(this.getName(),["string|undefined"],arguments);J(this,"get_url",a);b=Zk(el(x.location.href),a);return b}ZL.publicName="getUrl";function $L(){J(this,"get_user_agent");return xc.userAgent}$L.M="internal.getUserAgent";function aM(){var a;return a?Ed(hz(a)):a}aM.M="internal.getUserAgentClientHints";var cM=function(a){var b=a.eventName===K.m.Wc&&pn()&&zy(a),c=R(a,P.A.nl),d=R(a,P.A.Ij),e=R(a,P.A.Hf),f=R(a,P.A.oe),g=R(a,P.A.ug),h=R(a,P.A.Sd),m=R(a,P.A.vg),n=R(a,P.A.wg),p=!!yy(a)||!!R(a,P.A.Oh);return!(!Zc()&&xc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&bM)},bM=!1;
var dM=function(a){var b=0,c=0;return{start:function(){b=Db()},stop:function(){c=this.get()},get:function(){var d=0;a.gj()&&(d=Db()-b);return d+c}}},eM=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.R=this.P=void 0};k=eM.prototype;k.ao=function(a){var b=this;if(!this.C){this.N=A.hasFocus();this.isVisible=!A.hidden;this.isActive=!0;var c=function(e,f,g){Oc(e,f,function(h){b.C.stop();g(h);b.gj()&&b.C.start()})},d=x;c(d,"focus",function(){b.N=!0});c(d,"blur",function(){b.N=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&N(56);b.R&&b.R()});c(d,"pagehide",function(){b.isActive=!1;b.P&&b.P()});c(A,"visibilitychange",function(){b.isVisible=!A.hidden});zy(a)&&!Dc()&&c(d,"beforeunload",function(){bM=!0});this.yj(!0);this.H=0}};k.yj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.xh(),this.C=dM(this),this.gj()&&this.C.start()};k.Dq=function(a){var b=this.xh();b>0&&U(a,K.m.Ig,b)};k.xp=function(a){U(a,K.m.Ig);this.yj();this.H=0};k.gj=function(){return this.N&&
this.isVisible&&this.isActive};k.mp=function(){return this.H+this.xh()};k.xh=function(){return this.C&&this.C.get()||0};k.iq=function(a){this.P=a};k.vm=function(a){this.R=a};var fM=function(a){ib("GA4_EVENT",a)};var gM=function(a){var b=R(a,P.A.Vk);if(Array.isArray(b))for(var c=0;c<b.length;c++)fM(b[c]);var d=lb("GA4_EVENT");d&&U(a,"_eu",d)},hM=function(){delete hb.GA4_EVENT};function iM(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function jM(){var a=iM();a.hid=a.hid||tb();return a.hid}function kM(a,b){var c=iM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var lM=["GA1"];
var mM=function(a,b,c){var d=R(a,P.A.Kj);if(d===void 0||c<=d)U(a,K.m.Rb,b),S(a,P.A.Kj,c)},oM=function(a,b){var c=ew(a,K.m.Rb);if(O(a.D,K.m.Ic)&&O(a.D,K.m.Hc)||b&&c===b)return c;if(c){c=""+c;if(!nM(c,a))return N(31),a.isAborted=!0,"";kM(c,wp(K.m.ja));return c}N(32);a.isAborted=!0;return""},pM=function(a){var b=R(a,P.A.wa),c=b.prefix+"_ga",d=Ss(b.prefix+"_ga",b.domain,b.path,lM,K.m.ja);if(!d){var e=String(O(a.D,K.m.bd,""));e&&e!==c&&(d=Ss(e,b.domain,b.path,lM,K.m.ja))}return d},nM=function(a,b){var c;
var d=R(b,P.A.wa),e=d.prefix+"_ga",f=$r(d,void 0,void 0,K.m.ja);if(O(b.D,K.m.Ec)===!1&&pM(b)===a)c=!0;else{var g;g=[lM[0],Ps(d.domain,d.path),a].join(".");c=Ks(e,g,f)!==1}return c};
var sM=function(a){var b=new RegExp("^"+(((a==null?void 0:a.prefix)||"")+"_ga_\\w+$")),c=Zt(function(p){return b.test(p)}),d={},e;for(e in c)if(c.hasOwnProperty(e)){var f=qM(c[e]);if(f){var g=Vt(f,2);if(g){var h=rM(g);if(h){var m=void 0,n=(((m=a)==null?void 0:m.prefix)||"").length+4;d["G-"+e.substring(n)]=h}}}}return d},tM=function(a){if(a){var b;a:{var c=(Jb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Tt(c,2);break a}catch(d){}b=void 0}return b}},qM=function(a){if(a&&a.length!==0){for(var b,
c=-Infinity,d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;if(f.t!==void 0){var g=Number(f.t);!isNaN(g)&&g>c&&(c=g,b=f)}}return b}},$t=function(a){a&&(a==="GS1"?fM(33):a==="GS2"&&fM(34))},rM=function(a){var b=tM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||fM(29);d||fM(30);isNaN(e)&&fM(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var vM=function(a,b,c){if(!b)return a;if(!a)return b;var d=rM(a);if(!d)return b;var e,f=yb((e=O(c.D,K.m.rf))!=null?e:30),g=R(c,P.A.eb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=rM(b);if(!h)return a;h.o=d.o+1;var m;return(m=uM(h))!=null?m:b},xM=function(a,b){var c=R(b,P.A.wa),d=wM(b,c),e=tM(a);if(!e)return!1;var f=$r(c||{},void 0,void 0,Wt.get(2));Ks(d,void 0,f);return au(d,e,2,c)!==1},yM=function(a){var b=R(a,P.A.wa),c;var d=wM(a,b),e;b:{var f=$t,g=St[2];if(g){var h,m=Ns(b.domain),n=Os(b.path),
p=Object.keys(g.Hh),q=Wt.get(2),r;if(h=(r=Cs(d,m,n,p,q))==null?void 0:r.Ko){var t=Tt(h,2,f);e=t?Yt(t):void 0;break b}}e=void 0}if(e){var u=Xt(d,2,$t);if(u&&u.length>1){fM(28);var v=qM(u);v&&v.t!==e.t&&(fM(32),e=v)}c=Vt(e,2)}else c=void 0;return c},zM=function(a){var b=R(a,P.A.eb),c={};c.s=ew(a,K.m.Wb);c.o=ew(a,K.m.Ug);var d;d=ew(a,K.m.Tg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=R(a,P.A.Kf),c.j=R(a,P.A.Lf)||0,c.l=!!R(a,K.m.Zh),c.h=ew(a,K.m.Jg),c);return uM(e)},uM=function(a){if(a.s&&a.o){var b={},c=
(b.s=a.s,b.o=String(a.o),b.g=yb(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Vt(c,2)}},wM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Up[6]]};
var AM=function(a){var b=O(a.D,K.m.Wa),c=a.D.H[K.m.Wa];if(c===b)return c;var d=pd(b,null);c&&c[K.m.oa]&&(d[K.m.oa]=(d[K.m.oa]||[]).concat(c[K.m.oa]));return d},BM=function(a,b){var c=kt(!0);return c._up!=="1"?{}:{clientId:c[a],tb:c[b]}},CM=function(a,b,c){var d=kt(!0),e=d[b];e&&(mM(a,e,2),nM(e,a));var f=d[c];f&&xM(f,a);return{clientId:e,tb:f}},DM=function(){var a=al(x.location,"host"),b=al(el(A.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},EM=function(a){if(!O(a.D,
K.m.Fb))return{};var b=R(a,P.A.wa),c=b.prefix+"_ga",d=wM(a,b);st(function(){var e;if(wp("analytics_storage"))e={};else{var f={_up:"1"},g;g=ew(a,K.m.Rb);e=(f[c]=g,f[d]=zM(a),f)}return e},1);return!wp("analytics_storage")&&DM()?BM(c,d):{}},GM=function(a){var b=AM(a)||{},c=R(a,P.A.wa),d=c.prefix+"_ga",e=wM(a,c),f={};ut(b[K.m.nf],!!b[K.m.oa])&&(f=CM(a,d,e),f.clientId&&f.tb&&(FM=!0));b[K.m.oa]&&rt(function(){var g={},h=pM(a);h&&(g[d]=h);var m=yM(a);m&&(g[e]=m);var n=ys("FPLC",void 0,void 0,K.m.ja);n.length&&
(g._fplc=n[0]);return g},b[K.m.oa],b[K.m.ld],!!b[K.m.Jc]);return f},FM=!1;var HM=function(a){if(!R(a,P.A.zd)&&ml(a.D)){var b=AM(a)||{},c=(ut(b[K.m.nf],!!b[K.m.oa])?kt(!0)._fplc:void 0)||(ys("FPLC",void 0,void 0,K.m.ja).length>0?void 0:"0");U(a,"_fplc",c)}};function IM(a){(zy(a)||yk())&&U(a,K.m.Qk,vo()||uo());!zy(a)&&yk()&&U(a,K.m.bl,"::")}function JM(a){if(yk()&&!zy(a)&&(yo()||U(a,K.m.Ek,!0),F(78))){sw(a);tw(a,Pp.Df.Rm,So(O(a.D,K.m.nb)));var b=Pp.Df.Sm;var c=O(a.D,K.m.Ec);tw(a,b,c===!0?1:c===!1?0:void 0);tw(a,Pp.Df.Qm,So(O(a.D,K.m.Db)));tw(a,Pp.Df.Om,Ps(Ro(O(a.D,K.m.yb)),Ro(O(a.D,K.m.Sb))))}};var LM=function(a,b){Ip("grl",function(){return KM()})(b)||(N(35),a.isAborted=!0)},KM=function(){var a=Db(),b=a+864E5,c=20,d=5E3;return function(e){var f=Db();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Qo=d,e.Do=c);return g}};
var MM=function(a){var b=ew(a,K.m.Xa);return Zk(el(b),"host",!0)},NM=function(a){if(O(a.D,K.m.lf)!==void 0)a.copyToHitData(K.m.lf);else{var b=O(a.D,K.m.fi),c,d;a:{if(FM){var e=AM(a)||{};if(e&&e[K.m.oa])for(var f=MM(a),g=e[K.m.oa],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=MM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(U(a,K.m.lf,"1"),
fM(4))}};
var OM=function(a,b){Kr()&&(a.gcs=Lr(),R(b,P.A.Gf)&&(a.gcu="1"));a.gcd=Pr(b.D);a.npa=R(b,P.A.Ih)?"0":"1";Ur()&&(a._ng="1")},PM=function(a){if(R(a,P.A.zd))return{url:nl("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=jl(ml(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=Ay(a),d=O(a.D,K.m.Pb),e=c&&!wo()&&d!==!1&&qK()&&wp(K.m.U)&&wp(K.m.ja)?17:16;return{url:cA(e),endpoint:e}},QM={};QM[K.m.Rb]="cid";QM[K.m.Qh]="gcut";QM[K.m.Zc]="are";QM[K.m.Gg]="pscdl";
QM[K.m.ai]="_fid";QM[K.m.Ak]="_geo";QM[K.m.Ub]="gdid";QM[K.m.fe]="_ng";QM[K.m.Gc]="frm";QM[K.m.lf]="ir";QM[K.m.Ek]="fp";QM[K.m.Ab]="ul";QM[K.m.Rg]="ni";QM[K.m.Ln]="pae";QM[K.m.Sg]="_rdi";QM[K.m.Kc]="sr";QM[K.m.Pn]="tid";QM[K.m.ki]="tt";QM[K.m.uc]="ec_mode";QM[K.m.kl]="gtm_up";QM[K.m.uf]="uaa";QM[K.m.vf]="uab";QM[K.m.wf]="uafvl";QM[K.m.xf]="uamb";QM[K.m.yf]="uam";QM[K.m.zf]=
"uap";QM[K.m.Af]="uapv";QM[K.m.Bf]="uaw";QM[K.m.Qk]="ur";QM[K.m.bl]="_uip";QM[K.m.Kn]="_prs";QM[K.m.kd]="lps";QM[K.m.Yd]="gclgs";QM[K.m.ae]="gclst";QM[K.m.Zd]="gcllp";var RM={};RM[K.m.Se]="cc";RM[K.m.Te]="ci";RM[K.m.Ue]="cm";RM[K.m.Ve]="cn";RM[K.m.Xe]="cs";RM[K.m.Ye]="ck";RM[K.m.ab]=
"cu";RM[K.m.kf]="_tu";RM[K.m.Ca]="dl";RM[K.m.Xa]="dr";RM[K.m.Eb]="dt";RM[K.m.Tg]="seg";RM[K.m.Wb]="sid";RM[K.m.Ug]="sct";RM[K.m.Ma]="uid";F(145)&&(RM[K.m.qf]="dp");var SM={};SM[K.m.Ig]="_et";SM[K.m.Tb]="edid";F(94)&&(SM._eu="_eu");var TM={};TM[K.m.Se]="cc";TM[K.m.Te]="ci";TM[K.m.Ue]="cm";TM[K.m.Ve]="cn";TM[K.m.Xe]="cs";TM[K.m.Ye]="ck";var UM={},VM=(UM[K.m.cb]=1,
UM),WM=function(a,b,c){function d(Q,W){if(W!==void 0&&!Co.hasOwnProperty(Q)){W===null&&(W="");var ja;var ka=W;Q!==K.m.Jg?ja=!1:R(a,P.A.ke)||zy(a)?(e.ecid=ka,ja=!0):ja=void 0;if(!ja&&Q!==K.m.Zh){var Y=W;W===!0&&(Y="1");W===!1&&(Y="0");Y=String(Y);var X;if(QM[Q])X=QM[Q],e[X]=Y;else if(RM[Q])X=RM[Q],g[X]=Y;else if(SM[Q])X=SM[Q],f[X]=Y;else if(Q.charAt(0)==="_")e[Q]=Y;else{var ha;TM[Q]?ha=!0:Q!==K.m.We?ha=!1:(typeof W!=="object"&&B(Q,W),ha=!0);ha||B(Q,W)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;
e.gtm=Yr({Oa:R(a,P.A.fb)});e._p=F(159)?rk:jM();if(c&&(c.jb||c.bj)&&(F(125)||(e.em=c.Jb),c.Hb)){var h=c.Hb.ye;h&&!F(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}R(a,P.A.Sd)&&(e._gaz=1);OM(e,a);Sr()&&(e.dma_cps=Qr());e.dma=Rr();nr(vr())&&(e.tcfd=Tr());dA()&&(e.tag_exp=dA());eA()&&(e.ptag_exp=eA());var m=ew(a,K.m.Ub);m&&(e.gdid=m);f.en=String(a.eventName);if(R(a,P.A.If)){var n=R(a,P.A.il);f._fv=n?2:1}R(a,P.A.eh)&&(f._nsi=1);if(R(a,P.A.oe)){var p=R(a,P.A.ml);f._ss=p?2:1}R(a,P.A.Hf)&&(f._c=1);R(a,P.A.yd)&&
(f._ee=1);if(R(a,P.A.fl)){var q=ew(a,K.m.xa)||O(a.D,K.m.xa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=xg(q[r])}var t=ew(a,K.m.Tb);t&&(f.edid=t);var u=ew(a,K.m.rc);if(u&&typeof u==="object")for(var v=l(Object.keys(u)),w=v.next();!w.done;w=v.next()){var y=w.value,z=u[y];z!==void 0&&(z===null&&(z=""),f["gap."+y]=String(z))}for(var B=function(Q,W){if(typeof W!=="object"||!VM[Q]){var ja="ep."+Q,ka="epn."+Q;Q=qb(W)?ka:ja;var Y=qb(W)?ja:ka;f.hasOwnProperty(Y)&&delete f[Y];f[Q]=
String(W)}},D=l(Object.keys(a.C)),G=D.next();!G.done;G=D.next()){var I=G.value;d(I,ew(a,I))}(function(Q){zy(a)&&typeof Q==="object"&&wb(Q||{},function(W,ja){typeof ja!=="object"&&(e["sst."+W]=String(ja))})})(ew(a,K.m.Hi));fA(e,ew(a,K.m.wd));var M=ew(a,K.m.Xb)||{};O(a.D,K.m.Pb,void 0,4)===!1&&(e.ngs="1");wb(M,function(Q,W){W!==void 0&&((W===null&&(W=""),Q!==K.m.Ma||g.uid)?b[Q]!==W&&(f[(qb(W)?"upn.":"up.")+String(Q)]=String(W),b[Q]=W):g.uid=String(W))});if(yk()&&!yo()){var T=R(a,P.A.Kf);T?e._gsid=T:
e.njid="1"}var ea=PM(a);Kg.call(this,{qa:e,Qd:g,Vi:f},ea.url,ea.endpoint,zy(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};va(WM,Kg);
var XM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},YM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(F(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},ZM=function(a,b,c,d,e){var f=0,g=new x.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
YA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},aN=function(a,b,c){var d;return d=aB($A(new ZA(function(e,f){var g=XM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");vm(a,g,void 0,cB(d,f),h)}),function(e,f){var g=XM(e,b),h=f.dedupe_key;h&&Am(a,g,h)}),function(e,
f){var g=XM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?$M(a,g,void 0,d,h,cB(d,f)):wm(a,g,void 0,h,void 0,cB(d,f))})},bN=function(a,b,c,d,e){pm(a,2,b);var f=aN(a,d,e);$M(a,b,c,f)},$M=function(a,b,c,d,e,f){Zc()?XA(a,b,c,d,e,void 0,f):ZM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},cN=function(a,b,c){var d=el(b),e=YM(d),f=eB(d);!F(132)||Cc("; wv")||
Cc("FBAN")||Cc("FBAV")||Ec()?bN(a,f,c,e):$y(f,c,e,function(g){bN(a,f,c,e,g)})};var dN={AW:En.Z.Im,G:En.Z.Tn,DC:En.Z.Rn};function eN(a){var b=oj(a);return""+ps(b.map(function(c){return c.value}).join("!"))}function fN(a){var b=Sp(a);return b&&dN[b.prefix]}function gN(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};
var hN=function(a,b,c,d){var e=a+"?"+b;d?um(c,e,d):tm(c,e)},jN=function(a,b,c,d,e){var f=b,g=bd();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;iN&&(d=!Jb(h,bA())&&!Jb(h,aA()));if(d&&!bM)cN(e,h,c);else{var m=b;Zc()?wm(e,a+"?"+m,c,{Eh:!0})||hN(a,m,e,c):hN(a,m,e,c)}},kN=function(a,b){function c(y){q.push(y+"="+encodeURIComponent(""+a.qa[y]))}var d=b.qq,e=b.tq,f=b.sq,g=b.rq,h=b.op,m=b.Jp,n=b.Ip,p=b.cp;if(d||e||f||g){var q=[];a.qa._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Qd.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Qd.uid));c("dma");a.qa.dma_cps!=null&&c("dma_cps");a.qa.gcs!=null&&c("gcs");c("gcd");a.qa.npa!=null&&c("npa");a.qa.frm!=null&&c("frm");d&&(dA()&&q.push("tag_exp="+dA()),eA()&&q.push("ptag_exp="+eA()),hN("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),ip({targetId:String(a.qa.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},Na:b.Na}));if(e&&(dA()&&q.push("tag_exp="+dA()),eA()&&q.push("ptag_exp="+eA()),q.push("z="+tb()),!m)){var r=h&&Jb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");vm({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);ip({targetId:String(a.qa.tid),request:{url:t,parameterEncoding:2,endpoint:47},Na:b.Na})}}if(f){var u="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");a.qa._geo&&c("_geo");hN(u,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});ip({targetId:String(a.qa.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Na:b.Na})}if(g)if(q=[],q.push("v=2"),c("_gsid"),c("gtm"),a.qa._geo&&c("_geo"),F(224)){var v="https://{ga4CollectionSubdomain.}google-analytics.com/g/s/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");hN(v,q.join("&"),{destinationId:a.destinationId||
"",endpoint:62,eventId:a.eventId,priorityId:a.priorityId});ip({targetId:String(a.qa.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:62},Na:b.Na})}else{var w="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q.push("t=g");hN(w,q.join("&"),{destinationId:a.destinationId||"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});ip({targetId:String(a.qa.tid),request:{url:w+"?"+q.join("&"),parameterEncoding:2,endpoint:16},
Na:b.Na})}}},iN=!1;var lN=function(){this.N=1;this.P={};this.H=-1;this.C=new Dg};k=lN.prototype;k.Lb=function(a,b){var c=this,d=new WM(a,this.P,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=cM(a),g,h;f&&this.C.R(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=x,p=n.setTimeout,q;zy(a)?mN?(mN=!1,q=nN):q=oN:q=5E3;this.H=p.call(n,function(){c.flush()},
q)}}else{var r=Gg(d,this.N++),t=r.params,u=r.body;g=t;h=u;jN(d.baseUrl,t,u,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=R(a,P.A.ug),w=R(a,P.A.Sd),y=R(a,P.A.wg),z=R(a,P.A.vg),B=O(a.D,K.m.lb)!==!1,D=Jr(a.D),G={qq:v,tq:w,sq:y,rq:z,op:Ao(),xr:B,wr:D,Jp:wo(),Ip:R(a,P.A.ke),Na:e,D:a.D,cp:yo()};kN(d,G)}MA(a.D.eventId);jp(function(){if(m){var I=Gg(d),M=I.body;g=I.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+
g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},Na:e,isBatched:!1}})};k.add=function(a){if(F(100)){var b=R(a,P.A.Oh);if(b){U(a,K.m.uc,R(a,P.A.Il));U(a,K.m.Rg,"1");this.Lb(a,b);return}}var c=yy(a);if(F(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=fN(e);if(h){var m=eN(g);f=(In(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:void 0;if(d&&d+6E4>Db())c=void 0,U(a,K.m.uc);else{var p=c,q=a.target.destinationId,r=fN(q);if(r){var t=eN(p),u=In(r)||{},v=u[t];if(v)v.timestamp=Db(),v.sentTo=
v.sentTo||{},v.sentTo[q]=Db(),v.pending=!0;else{var w={};u[t]={pending:!0,timestamp:Db(),sentTo:(w[q]=Db(),w)}}gN(u,t);Hn(r,u)}}}!c||bM||F(125)&&!F(93)?this.Lb(a):this.uq(a)};k.flush=function(){if(this.C.events.length){var a=Ig(this.C,this.N++);jN(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,eventId:this.C.ia,priorityId:this.C.la});this.C=new Dg;this.H>=0&&(x.clearTimeout(this.H),this.H=-1)}};k.Sl=function(a,b){var c=ew(a,K.m.uc);U(a,K.m.uc);
b.then(function(d){var e={},f=(e[P.A.Oh]=d,e[P.A.Il]=c,e),g=cx(a.target.destinationId,K.m.Xd,a.D.C);fx(g,a.D.eventId,{eventMetadata:f})})};k.uq=function(a){var b=this,c=yy(a);if(Mj(c)){var d=Bj(c,F(93));d?F(100)?(this.Sl(a,d),this.Lb(a)):d.then(function(g){b.Lb(a,g)},function(){b.Lb(a)}):this.Lb(a)}else{var e=Lj(c);if(F(93)){var f=xj(e);f?F(100)?(this.Sl(a,f),this.Lb(a)):f.then(function(g){b.Lb(a,g)},function(){b.Lb(a,e)}):this.Lb(a,e)}else this.Lb(a,e)}};var nN=yg(Ri(24,''),
500),oN=yg(Ri(56,''),5E3),mN=!0;
var pN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;pN(a+"."+f,b[f],c)}else c[a]=b;return c},qN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!wp(e)}return b},sN=function(a,b){var c=rN.filter(function(e){return!wp(e)});if(c.length){var d=qN(c);xp(c,function(){for(var e=qN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){S(b,P.A.Gf,!0);var n=f.map(function(p){return Mo[p]}).join(".");n&&wy(b,"gcut",n);a(b)}})}},tN=function(a){zy(a)&&wy(a,"navt",cd())},uN=function(a){zy(a)&&wy(a,"lpc",gu())},vN=function(a){if(F(152)&&zy(a)){var b=O(a.D,K.m.Vb),c;b===!0&&(c="1");b===!1&&(c="0");c&&wy(a,"rdp",c)}},wN=function(a){F(147)&&zy(a)&&O(a.D,K.m.Re,!0)===!1&&U(a,K.m.Re,0)},xN=function(a,b){if(zy(b)){var c=R(b,P.A.Hf);(b.eventName==="page_view"||c)&&sN(a,b)}},yN=function(a){if(zy(a)&&a.eventName===K.m.Xd&&
R(a,P.A.Gf)){var b=ew(a,K.m.Qh);b&&(wy(a,"gcut",b),wy(a,"syn",1))}},zN=function(a){zy(a)&&S(a,P.A.za,!1)},AN=function(a){zy(a)&&(R(a,P.A.za)&&wy(a,"sp",1),R(a,P.A.Yn)&&wy(a,"syn",1),R(a,P.A.Le)&&(wy(a,"em_event",1),wy(a,"sp",1)))},BN=function(a){if(zy(a)){var b=rk;b&&wy(a,"tft",Number(b))}},CN=function(a){function b(e){var f=pN(K.m.cb,e);wb(f,function(g,h){U(a,g,h)})}if(zy(a)){var c=yw(a,"ccd_add_1p_data",!1)?1:0;wy(a,"ude",c);var d=O(a.D,K.m.cb);d!==void 0?(b(d),U(a,K.m.uc,"c")):b(R(a,P.A.hb));S(a,
P.A.hb)}},DN=function(a){if(zy(a)){var b=bw();b&&wy(a,"us_privacy",b);var c=Cr();c&&wy(a,"gdpr",c);var d=Br();d&&wy(a,"gdpr_consent",d);var e=Pv.gppString;e&&wy(a,"gpp",e);var f=Pv.C;f&&wy(a,"gpp_sid",f)}},EN=function(a){zy(a)&&pn()&&O(a.D,K.m.Ba)&&wy(a,"adr",1)},FN=function(a){if(zy(a)){var b=F(90)?yo():"";b&&wy(a,"gcsub",b)}},GN=function(a){if(zy(a)){O(a.D,K.m.Pb,void 0,4)===!1&&wy(a,"ngs",1);wo()&&wy(a,"ga_rd",1);qK()||wy(a,"ngst",1);var b=Ao();b&&wy(a,"etld",b)}},HN=function(a){},IN=function(a){zy(a)&&pn()&&wy(a,"rnd",Cv())},rN=[K.m.U,K.m.V];
var JN=function(a,b){var c;a:{var d=zM(a);if(d){if(xM(d,a)){c=d;break a}N(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:oM(a,b),tb:e}},KN=function(a,b,c,d,e){var f=Ro(O(a.D,K.m.Rb));if(O(a.D,K.m.Ic)&&O(a.D,K.m.Hc))f?mM(a,f,1):(N(127),a.isAborted=!0);else{var g=f?1:8;S(a,P.A.eh,!1);f||(f=pM(a),g=3);f||(f=b,g=5);if(!f){var h=wp(K.m.ja),m=iM();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=Rs(),g=7,S(a,P.A.If,!0),S(a,P.A.eh,!0));mM(a,f,g)}var n=R(a,P.A.eb),p=Math.floor(n/1E3),q=void 0;R(a,P.A.eh)||
(q=yM(a)||c);var r=yb(O(a.D,K.m.rf,30));r=Math.min(475,r);r=Math.max(5,r);var t=yb(O(a.D,K.m.hi,1E4)),u=rM(q);S(a,P.A.If,!1);S(a,P.A.oe,!1);S(a,P.A.Lf,0);u&&u.j&&S(a,P.A.Lf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){S(a,P.A.If,!0);v=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)S(a,P.A.oe,!0),d.xp(a);else if(d.mp()>t||a.eventName===K.m.Wc)u.g=!0;R(a,P.A.ke)?O(a.D,K.m.Ma)?u.l=!0:(u.l&&!F(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var y=u.h;if(R(a,P.A.ke)||zy(a)){var z=O(a.D,K.m.Jg),B=z?1:8;z||(z=y,B=4);z||(z=Qs(),B=7);var D=z.toString(),G=B,I=R(a,P.A.Wj);if(I===void 0||G<=I)U(a,K.m.Jg,D),S(a,P.A.Wj,G)}e?(a.copyToHitData(K.m.Wb,u.s),a.copyToHitData(K.m.Ug,u.o),a.copyToHitData(K.m.Tg,u.g?1:0)):(U(a,K.m.Wb,u.s),U(a,K.m.Ug,u.o),U(a,K.m.Tg,u.g?1:0));S(a,K.m.Zh,u.l?1:0);yk()&&S(a,P.A.Kf,u.d||Sb())};var MN=function(a){for(var b={},c=String(LN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].replace(/^\s*|\s*$/g,"");if(f&&a(f)){var g=e.slice(1).join("=").replace(/^\s*|\s*$/g,"");g&&(g=decodeURIComponent(g));var h=void 0,m=void 0;((h=b)[m=f]||(h[m]=[])).push(g)}}return b},NN=function(){return MN(function(a){return a==="AMP_TOKEN"}).AMP_TOKEN||[]};var ON=window,LN=document,PN=function(a){var b=ON._gaUserPrefs;if(b&&b.ioo&&b.ioo()||LN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&ON["ga-disable-"+a]===!0)return!0;try{var c=ON.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=NN(),e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return LN.getElementById("__gaOptOutExtension")?!0:!1};
var RN=function(a){return!a||QN.test(a)||Eo.hasOwnProperty(a)},SN=function(a){var b=K.m.Kc,c;c||(c=function(){});ew(a,b)!==void 0&&U(a,b,c(ew(a,b)))},TN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Yk(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},UN=function(a){O(a.D,K.m.Fb)&&(wp(K.m.ja)||O(a.D,K.m.Rb)||U(a,K.m.kl,!0));var b;var c;c=c===void 0?3:c;var d=x.location.href;if(d){var e=el(d).search.replace("?",""),f=Wk(e,"_gl",!1,!0)||"";b=f?lt(f,c)!==void 0:!1}else b=!1;b&&zy(a)&&
wy(a,"glv",1);if(a.eventName!==K.m.ra)return{};O(a.D,K.m.Fb)&&fv(["aw","dc"]);hv(["aw","dc"]);var g=GM(a),h=EM(a);return Object.keys(g).length?g:h},VN=function(a){var b=Nb(a.D.getMergedValues(K.m.na,1,Po(Sq.C[K.m.na])),".");b&&U(a,K.m.Ub,b);var c=Nb(a.D.getMergedValues(K.m.na,2),".");c&&U(a,K.m.Tb,c)},WN={Zo:Ri(31,'')},XN={},YN=(XN[K.m.Se]=1,XN[K.m.Te]=1,XN[K.m.Ue]=1,XN[K.m.Ve]=1,XN[K.m.Xe]=1,XN[K.m.Ye]=1,XN),QN=/^(_|ga_|google_|gtag\.|firebase_).*$/,
ZN=[xw,uw,gw,zw,VN,Xw],$N=function(a){this.N=a;this.C=this.tb=this.clientId=void 0;this.Ea=this.R=!1;this.pb=0;this.P=!1;this.Sa=!0;this.ia={ej:!1};this.la=new lN;this.H=new eM};k=$N.prototype;k.bq=function(a,b,c){var d=this,e=Sp(this.N);if(e)if(c.eventMetadata[P.A.yd]&&a.charAt(0)==="_")c.onFailure();else{a!==K.m.ra&&a!==K.m.Ob&&RN(a)&&N(58);aO(c.C);var f=new aI(e,a,c);S(f,P.A.eb,b);var g=[K.m.ja],h=zy(f);S(f,P.A.fh,h);if(yw(f,K.m.he,O(f.D,K.m.he))||h)g.push(K.m.U),g.push(K.m.V);jz(function(){zp(function(){d.cq(f)},
g)});F(88)&&a===K.m.ra&&yw(f,"ga4_ads_linked",!1)&&Bn(Dn(cn.X.Ga),function(){d.Zp(a,c,f)})}else c.onFailure()};k.Zp=function(a,b,c){function d(){for(var h=l(ZN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}R(f,P.A.za)||f.isAborted||lA(f)}var e=Sp(this.N),f=new aI(e,a,b);S(f,P.A.ba,L.J.Ia);S(f,P.A.za,!0);S(f,P.A.fh,R(c,P.A.fh));var g=[K.m.U,K.m.V];zp(function(){d();wp(g)||yp(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;S(f,P.A.fa,!0);S(f,P.A.Ie,m);S(f,P.A.Je,
n);d()},g)},g)};k.cq=function(a){var b=this;try{xw(a);if(a.isAborted){hM();return}F(165)||(this.C=a);bO(a);cO(a);dO(a);eO(a);F(138)&&(a.isAborted=!0);ow(a);var c={};LM(a,c);if(a.isAborted){a.D.onFailure();hM();return}F(165)&&(this.C=a);var d=c.Do;c.Qo===0&&fM(25);d===0&&fM(26);zw(a);S(a,P.A.Rf,cn.X.Cc);fO(a);gO(a);this.bo(a);this.H.Dq(a);hO(a);iO(a);jO(a);kO(a);this.tm(UN(a));var e=a.eventName===K.m.ra;e&&(this.P=!0);lO(a);e&&!a.isAborted&&this.pb++>0&&fM(17);mO(a);nO(a);KN(a,this.clientId,this.tb,
this.H,!this.Ea);oO(a);pO(a);qO(a);rO(a,this.ia);this.Sa=sO(a,this.Sa);tO(a);uO(a);vO(a);wO(a);xO(a);HM(a);NM(a);IN(a);HN(a);GN(a);FN(a);EN(a);DN(a);BN(a);AN(a);yN(a);wN(a);vN(a);uN(a);tN(a);IM(a);JM(a);yO(a);zO(a);AO(a);qw(a);pw(a);ww(a);BO(a);CO(a);Xw(a);DO(a);CN(a);zN(a);EO(a);!this.P&&R(a,P.A.Le)&&fM(18);gM(a);if(R(a,P.A.za)||a.isAborted){a.D.onFailure();hM();return}this.tm(JN(a,this.clientId));this.Ea=!0;this.Aq(a);FO(a);xN(function(f){b.Jl(f)},a);this.H.yj();GO(a);vw(a);if(a.isAborted){a.D.onFailure();
hM();return}this.Jl(a);a.D.onSuccess()}catch(f){a.D.onFailure()}hM()};k.Jl=function(a){this.la.add(a)};k.tm=function(a){var b=a.clientId,c=a.tb;b&&c&&(this.clientId=b,this.tb=c)};k.flush=function(){this.la.flush()};k.Aq=function(a){var b=this;if(!this.R){var c=wp(K.m.V),d=wp(K.m.ja),e=[K.m.V,K.m.ja];F(213)&&e.push(K.m.U);xp(e,function(){var f=wp(K.m.V),g=wp(K.m.ja),h=!1,m={},n={};if(d!==g&&b.C&&b.tb&&b.clientId){var p=b.clientId,q;var r=rM(b.tb);q=r?r.h:void 0;if(g){var t=pM(b.C);if(t){b.clientId=
t;var u=yM(b.C);u&&(b.tb=vM(u,b.tb,b.C))}else nM(b.clientId,b.C),kM(b.clientId,!0);xM(b.tb,b.C);h=!0;m[K.m.di]=p;F(69)&&q&&(m[K.m.En]=q)}else b.tb=void 0,b.clientId=void 0,x.gaGlobal={}}f&&!c&&(h=!0,n[P.A.Gf]=!0,m[K.m.Qh]=Mo[K.m.V]);if(h){var v=cx(b.N,K.m.Xd,m);fx(v,a.D.eventId,{eventMetadata:n})}d=g;c=f;b.ia.ej=!0});this.R=!0}};k.bo=function(a){a.eventName!==K.m.Ob&&this.H.ao(a)};var dO=function(a){var b=A.location.protocol;b!=="http:"&&b!=="https:"&&(N(29),a.isAborted=!0)},eO=function(a){xc&&xc.loadPurpose===
"preview"&&(N(30),a.isAborted=!0)},fO=function(a){var b={prefix:String(O(a.D,K.m.nb,"")),path:String(O(a.D,K.m.Sb,"/")),flags:String(O(a.D,K.m.Db,"")),domain:String(O(a.D,K.m.yb,"auto")),Qc:Number(O(a.D,K.m.zb,63072E3))};S(a,P.A.wa,b)},hO=function(a){R(a,P.A.zd)?S(a,P.A.ke,!1):yw(a,"ccd_add_ec_stitching",!1)&&S(a,P.A.ke,!0)},iO=function(a){if(yw(a,"ccd_add_1p_data",!1)){var b=a.D.H[K.m.Vg];if(Rk(b)){var c=O(a.D,K.m.cb);if(c===null)S(a,P.A.ve,null);else if(b.enable_code&&od(c)&&S(a,P.A.ve,c),od(b.selectors)&&
!R(a,P.A.nh)){var d={};S(a,P.A.nh,Pk(b.selectors,d));F(60)&&a.mergeHitDataForKey(K.m.rc,{ec_data_layer:Lk(d)})}}}},jO=function(a){if(F(91)&&!F(88)&&yw(a,"ga4_ads_linked",!1)&&a.eventName===K.m.ra){var b=O(a.D,K.m.Va)!==!1;if(b){var c=cw(a);c.Qc&&(c.Qc=Math.min(c.Qc,7776E3));dw({xe:b,Ce:Po(O(a.D,K.m.Wa)),He:!!O(a.D,K.m.Fb),Oc:c})}}},kO=function(a){var b=Jr(a.D);O(a.D,K.m.Vb)===!0&&(b=!1);S(a,P.A.Ih,b)},yO=function(a){if(!fz(x))N(87);else if(kz!==void 0){N(85);var b=dz(x);b?O(a.D,K.m.Sg)&&!zy(a)||iz(b,
a):N(86)}},lO=function(a){a.eventName===K.m.ra&&(O(a.D,K.m.ob,!0)?(a.D.C[K.m.na]&&(a.D.N[K.m.na]=a.D.C[K.m.na],a.D.C[K.m.na]=void 0,U(a,K.m.na)),a.eventName=K.m.Wc):a.isAborted=!0)},gO=function(a){function b(c,d){Co[c]||d===void 0||U(a,c,d)}wb(a.D.N,b);wb(a.D.C,b)},oO=function(a){var b=jq(a.D),c=function(d,e){YN[d]&&U(a,d,e)};od(b[K.m.We])?wb(b[K.m.We],function(d,e){c((K.m.We+"_"+d).toLowerCase(),e)}):wb(b,c)},mO=VN,FO=function(a){if(F(132)&&zy(a)&&!(Cc("; wv")||Cc("FBAN")||Cc("FBAV")||Ec())&&wp(K.m.ja)){S(a,
P.A.nl,!0);zy(a)&&wy(a,"sw_exp",1);a:{if(!F(132)||!zy(a))break a;var b=jl(ml(a.D),"/_/service_worker");Xy(b);}}},BO=function(a){if(a.eventName===K.m.Ob){var b=O(a.D,K.m.Fc),c=O(a.D,K.m.jd),d;d=ew(a,b);c(d||O(a.D,b));a.isAborted=!0}},pO=function(a){if(!O(a.D,K.m.Hc)||!O(a.D,K.m.Ic)){var b=a.copyToHitData,c=K.m.Ca,d="",e=A.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||
"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Pb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,TN);var p=a.copyToHitData,q=K.m.Xa,r;a:{var t=ys("_opt_expid",void 0,void 0,K.m.ja)[0];if(t){var u=Yk(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var w=Hp.ga4_referrer_override;if(w!==void 0)r=w;else{var y=Ek("gtm.gtagReferrer."+a.target.destinationId),
z=A.referrer;r=y?""+y:z}}p.call(a,q,r||void 0,TN);a.copyToHitData(K.m.Eb,A.title);a.copyToHitData(K.m.Ab,(xc.language||"").toLowerCase());var B=nx();a.copyToHitData(K.m.Kc,B.width+"x"+B.height);F(145)&&a.copyToHitData(K.m.qf,void 0,TN);F(87)&&Fv()&&a.copyToHitData(K.m.kd,"1")}},rO=function(a,b){F(213)&&b.ej&&(S(a,P.A.fa,!0),b.ej=!1,yk()&&S(a,P.A.Kf,Sb()))},sO=function(a,b){var c=R(a,P.A.Lf);c=c||0;var d=wp(K.m.U),e=!b&&d,f;f=F(213)?!!R(a,P.A.fa):e||!!R(a,P.A.Gf)||!!ew(a,K.m.di);var g=c===0||f;S(a,
P.A.Ci,g);g&&S(a,P.A.Lf,60);return d},tO=function(a){S(a,P.A.ug,!1);S(a,P.A.Sd,!1);if(!zy(a)&&!R(a,P.A.zd)&&O(a.D,K.m.Pb)!==!1&&qK()&&wp([K.m.U,K.m.ja])){var b=Ay(a);(R(a,P.A.oe)||O(a.D,K.m.di))&&S(a,P.A.ug,!!b);b&&R(a,P.A.Ci)&&R(a,P.A.jl)&&S(a,P.A.Sd,!0)}},uO=function(a){S(a,P.A.vg,!1);S(a,P.A.wg,!1);if(!yo()&&yk()&&!zy(a)&&!R(a,P.A.zd)&&R(a,P.A.Ci)){var b=R(a,P.A.Sd);R(a,P.A.Kf)&&(b?S(a,P.A.wg,!0):S(a,P.A.vg,!0))}},xO=function(a){a.copyToHitData(K.m.ki);for(var b=O(a.D,K.m.ei)||[],c=0;c<b.length;c++){var d=
b[c];if(d.rule_result){a.copyToHitData(K.m.ki,d.traffic_type);fM(3);break}}},GO=function(a){a.copyToHitData(K.m.Ak);O(a.D,K.m.Sg)&&(U(a,K.m.Sg,!0),zy(a)||SN(a))},CO=function(a){a.copyToHitData(K.m.Ma);a.copyToHitData(K.m.Xb)},qO=function(a){yw(a,"google_ng")&&!wo()?a.copyToHitData(K.m.fe,1):rw(a)},EO=function(a){var b=O(a.D,K.m.Ic);b&&fM(12);R(a,P.A.Le)&&fM(14);var c=Rm(Gm());(b||$m(c)||c&&c.parent&&c.context&&c.context.source===5)&&fM(19)},bO=function(a){if(PN(a.target.destinationId))N(28),a.isAborted=
!0;else if(F(144)){var b=Qm();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(PN(b.destinations[c])){N(125);a.isAborted=!0;break}}},zO=function(a){Xl("attribution-reporting")&&U(a,K.m.Zc,"1")},cO=function(a){if(WN.Zo.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=xy(a);b&&b.blacklisted&&(a.isAborted=!0)}},vO=function(a){var b=function(c){return!!c&&c.conversion};S(a,P.A.Hf,b(xy(a)));R(a,P.A.If)&&S(a,P.A.il,b(xy(a,"first_visit")));R(a,
P.A.oe)&&S(a,P.A.ml,b(xy(a,"session_start")))},wO=function(a){Go.hasOwnProperty(a.eventName)&&(S(a,P.A.fl,!0),a.copyToHitData(K.m.xa),a.copyToHitData(K.m.ab))},DO=function(a){if(!zy(a)&&R(a,P.A.Hf)&&wp(K.m.U)&&yw(a,"ga4_ads_linked",!1)){var b=cw(a),c=wu(b.prefix),d=Yv(c);U(a,K.m.Yd,d.th);U(a,K.m.ae,d.wh);U(a,K.m.Zd,d.uh)}},AO=function(a){if(F(122)){var b=yo();b&&S(a,P.A.Sn,b)}},nO=function(a){S(a,P.A.jl,Ay(a)&&O(a.D,K.m.Pb)!==!1&&qK()&&!wo())};
function aO(a){wb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Xb]||{};wb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var IO=function(a){if(!HO(a)){var b=!1,c=function(){!b&&HO(a)&&(b=!0,Pc(A,"visibilitychange",c),F(5)&&Pc(A,"prerenderingchange",c),N(55))};Oc(A,"visibilitychange",c);F(5)&&Oc(A,"prerenderingchange",c);N(54)}},HO=function(a){if(F(5)&&"prerendering"in A?A.prerendering:A.visibilityState==="prerender")return!1;a();return!0};function JO(a,b){IO(function(){var c=Sp(a);if(c){var d=KO(c,b);Rq(a,d,cn.X.Cc)}});}function KO(a,b){var c=function(){};var d=new $N(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[P.A.zd]=!0);d.bq(g,h,m)};LO(a,d,b);return c}
function LO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[P.A.Ij]=!0,e),deferrable:!0};d.iq(function(){bM=!0;Sq.flush();d.xh()>=1E3&&xc.sendBeacon!==void 0&&Tq(K.m.Xd,{},a.id,f);b.flush();d.vm(function(){bM=!1;d.vm()})});};var MO=KO;var NO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function OO(a,b,c){var d=this;}OO.M="internal.gtagConfig";
function QO(a,b){}
QO.publicName="gtagSet";function RO(){var a={};return a};function SO(a){}SO.M="internal.initializeServiceWorker";function TO(a,b){}TO.publicName="injectHiddenIframe";var UO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function VO(a,b,c,d,e){}VO.M="internal.injectHtml";var ZO={};
function aP(a,b,c,d){}var bP={dl:1,id:1},cP={};
function dP(a,b,c,d){}F(160)?dP.publicName="injectScript":aP.publicName="injectScript";dP.M="internal.injectScript";function eP(){return zo()}eP.M="internal.isAutoPiiEligible";function fP(a){var b=!0;return b}fP.publicName="isConsentGranted";function gP(a){var b=!1;return b}gP.M="internal.isDebugMode";function hP(){return xo()}hP.M="internal.isDmaRegion";function iP(a){var b=!1;return b}iP.M="internal.isEntityInfrastructure";function jP(a){var b=!1;if(!uh(a))throw H(this.getName(),["number"],[a]);b=F(a);return b}jP.M="internal.isFeatureEnabled";function kP(){var a=!1;return a}kP.M="internal.isFpfe";function lP(){var a=!1;return a}lP.M="internal.isGcpConversion";function mP(){var a=!1;return a}mP.M="internal.isLandingPage";function nP(){var a=!1;return a}nP.M="internal.isOgt";function oP(){var a;return a}oP.M="internal.isSafariPcmEligibleBrowser";function pP(){var a=Rh(function(b){JF(this).log("error",b)});a.publicName="JSON";return a};function qP(a){var b=void 0;return Ed(b)}qP.M="internal.legacyParseUrl";function rP(){return!1}
var sP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function tP(){}tP.publicName="logToConsole";function uP(a,b){}uP.M="internal.mergeRemoteConfig";function vP(a,b,c){c=c===void 0?!0:c;var d=[];return Ed(d)}vP.M="internal.parseCookieValuesFromString";function wP(a){var b=void 0;if(typeof a!=="string")return;a&&Jb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Ed({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=el(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Yk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Ed(n);
return b}wP.publicName="parseUrl";function xP(a){}xP.M="internal.processAsNewEvent";function yP(a,b,c){var d;return d}yP.M="internal.pushToDataLayer";function zP(a){var b=Ca.apply(1,arguments),c=!1;if(!ph(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(C(f.value,this.K,1));try{J.apply(null,d),c=!0}catch(g){return!1}return c}zP.publicName="queryPermission";function AP(a){var b=this;}AP.M="internal.queueAdsTransmission";function BP(a){var b=void 0;return b}BP.publicName="readAnalyticsStorage";function CP(){var a="";return a}CP.publicName="readCharacterSet";function DP(){return gk}DP.M="internal.readDataLayerName";function EP(){var a="";return a}EP.publicName="readTitle";function FP(a,b){var c=this;if(!ph(a)||!lh(b))throw H(this.getName(),["string","function"],arguments);Yw(a,function(d){b.invoke(c.K,Ed(d,c.K,1))});}FP.M="internal.registerCcdCallback";function GP(a,b){return!0}GP.M="internal.registerDestination";var HP=["config","event","get","set"];function IP(a,b,c){}IP.M="internal.registerGtagCommandListener";function JP(a,b){var c=!1;return c}JP.M="internal.removeDataLayerEventListener";function KP(a,b){}
KP.M="internal.removeFormData";function LP(){}LP.publicName="resetDataLayer";function MP(a,b,c){var d=void 0;return d}MP.M="internal.scrubUrlParams";function NP(a){}NP.M="internal.sendAdsHit";function OP(a,b,c,d){if(arguments.length<2||!jh(d)||!jh(c))throw H(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?C(c):{},f=C(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?C(d):{},m=JF(this);h.originatingEntity=yG(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};pd(e,q);var r={};pd(h,r);var t=cx(p,b,q);fx(t,h.eventId||m.eventId,r)}}}OP.M="internal.sendGtagEvent";function PP(a,b,c){}PP.publicName="sendPixel";function QP(a,b){}QP.M="internal.setAnchorHref";function RP(a){}RP.M="internal.setContainerConsentDefaults";function SP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}SP.publicName="setCookie";function TP(a){}TP.M="internal.setCorePlatformServices";function UP(a,b){}UP.M="internal.setDataLayerValue";function VP(a){}VP.publicName="setDefaultConsentState";function WP(a,b){}WP.M="internal.setDelegatedConsentType";function XP(a,b){}XP.M="internal.setFormAction";function YP(a,b,c){c=c===void 0?!1:c;}YP.M="internal.setInCrossContainerData";function ZP(a,b,c){return!1}ZP.publicName="setInWindow";function $P(a,b,c){}$P.M="internal.setProductSettingsParameter";function aQ(a,b,c){if(!ph(a)||!ph(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Vq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!od(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=C(c,this.K,1);}aQ.M="internal.setRemoteConfigParameter";function bQ(a,b){}bQ.M="internal.setTransmissionMode";function cQ(a,b,c,d){var e=this;}cQ.publicName="sha256";function dQ(a,b,c){}
dQ.M="internal.sortRemoteConfigParameters";function eQ(a){}eQ.M="internal.storeAdsBraidLabels";function fQ(a,b){var c=void 0;return c}fQ.M="internal.subscribeToCrossContainerData";var gQ={},hQ={};gQ.getItem=function(a){var b=null;J(this,"access_template_storage");var c=JF(this).Ib();hQ[c]&&(b=hQ[c].hasOwnProperty("gtm."+a)?hQ[c]["gtm."+a]:null);return b};gQ.setItem=function(a,b){J(this,"access_template_storage");var c=JF(this).Ib();hQ[c]=hQ[c]||{};hQ[c]["gtm."+a]=b;};
gQ.removeItem=function(a){J(this,"access_template_storage");var b=JF(this).Ib();if(!hQ[b]||!hQ[b].hasOwnProperty("gtm."+a))return;delete hQ[b]["gtm."+a];};gQ.clear=function(){J(this,"access_template_storage"),delete hQ[JF(this).Ib()];};gQ.publicName="templateStorage";function iQ(a,b){var c=!1;return c}iQ.M="internal.testRegex";function jQ(a){var b;return b};function kQ(a,b){var c;return c}kQ.M="internal.unsubscribeFromCrossContainerData";function lQ(a){}lQ.publicName="updateConsentState";function mQ(a){var b=!1;return b}mQ.M="internal.userDataNeedsEncryption";var nQ;function oQ(a,b,c){nQ=nQ||new bi;nQ.add(a,b,c)}function pQ(a,b){var c=nQ=nQ||new bi;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=ob(b)?xh(a,b):yh(a,b)}
function qQ(){return function(a){var b;var c=nQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Ib();if(g){Eh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function rQ(){var a=function(c){return void pQ(c.M,c)},b=function(c){return void oQ(c.publicName,c)};b(DF);b(KF);b(YG);b($G);b(aH);b(hH);b(jH);b(eI);b(pP());b(gI);b(uL);b(vL);b(RL);b(SL);b(TL);b(ZL);b(QO);b(TO);b(fP);b(tP);b(wP);b(zP);b(CP);b(EP);b(PP);b(SP);b(VP);b(ZP);b(cQ);b(gQ);b(lQ);oQ("Math",Ch());oQ("Object",$h);oQ("TestHelper",di());oQ("assertApi",zh);oQ("assertThat",Ah);oQ("decodeUri",Fh);oQ("decodeUriComponent",Gh);oQ("encodeUri",Hh);oQ("encodeUriComponent",Ih);oQ("fail",Nh);oQ("generateRandom",
Oh);oQ("getTimestamp",Ph);oQ("getTimestampMillis",Ph);oQ("getType",Qh);oQ("makeInteger",Sh);oQ("makeNumber",Th);oQ("makeString",Uh);oQ("makeTableMap",Vh);oQ("mock",Yh);oQ("mockObject",Zh);oQ("fromBase64",nL,!("atob"in x));oQ("localStorage",sP,!rP());oQ("toBase64",jQ,!("btoa"in x));a(CF);a(GF);a($F);a(lG);a(sG);a(xG);a(NG);a(WG);a(ZG);a(bH);a(cH);a(dH);a(eH);a(fH);a(gH);a(iH);a(kH);a(dI);a(fI);a(hI);a(iI);a(jI);a(kI);a(lI);a(mI);a(rI);a(zI);a(AI);a(LI);a(QI);a(VI);a(dJ);a(iJ);a(vJ);a(xJ);a(LJ);a(MJ);
a(OJ);a(lL);a(mL);a(oL);a(pL);a(qL);a(rL);a(sL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(KL);a(LL);a(ML);a(NL);a(OL);a(PL);a(QL);a(UL);a(VL);a(WL);a(XL);a(YL);a(aM);a(OO);a(SO);a(VO);a(dP);a(eP);a(gP);a(hP);a(iP);a(jP);a(kP);a(lP);a(mP);a(nP);a(oP);a(qP);a(LG);a(uP);a(vP);a(xP);a(yP);a(AP);a(DP);a(FP);a(GP);a(IP);a(JP);a(KP);a(MP);a(NP);a(OP);a(QP);a(RP);a(TP);a(UP);a(WP);a(XP);a(YP);a($P);a(aQ);a(bQ);a(dQ);a(eQ);a(fQ);a(iQ);a(kQ);a(mQ);pQ("internal.IframingStateSchema",
RO());
F(104)&&a(wL);F(160)?b(dP):b(aP);F(177)&&b(BP);return qQ()};var AF;
function sQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;AF=new $e;tQ();Hf=zF();var e=AF,f=rQ(),g=new xd("require",f);g.Ta();e.C.C.set("require",g);Xa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&bg(n,d[m]);try{AF.execute(n),F(120)&&tl&&n[0]===50&&h.push(n[1])}catch(r){}}F(120)&&(Uf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");uk[q]=["sandboxedScripts"]}uQ(b)}function tQ(){AF.Tc(function(a,b,c){Hp.SANDBOXED_JS_SEMAPHORE=Hp.SANDBOXED_JS_SEMAPHORE||0;Hp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Hp.SANDBOXED_JS_SEMAPHORE--}})}function uQ(a){a&&wb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");uk[e]=uk[e]||[];uk[e].push(b)}})};function vQ(a){fx($w("developer_id."+a,!0),0,{})};var wQ=Array.isArray;function xQ(a,b){return pd(a,b||null)}function V(a){return window.encodeURIComponent(a)}function yQ(a,b,c){Nc(a,b,c)}
function zQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Zk(el(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function AQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function BQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=AQ(b,"parameter","parameterValue");e&&(c=xQ(e,c))}return c}function CQ(a,b,c){return a===void 0||a===c?b:a}function DQ(a,b,c){return Jc(a,b,c,void 0)}function EQ(){return x.location.href}function FQ(a,b){return Ek(a,b||2)}function GQ(a,b){x[a]=b}function HQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var IQ={};var Z={securityGroups:{}};
Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},T:function(){return{}}}},Z.__access_template_storage.F="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage["5"]=!1;

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!pb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Ng(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.F="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events["5"]=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},T:a}})}();



Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.F="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();


Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.F="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},T:a}})}();
Z.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_form_submit_events=b;Z.__detect_form_submit_events.F="detect_form_submit_events";Z.__detect_form_submit_events.isVendorTemplate=!0;Z.__detect_form_submit_events.priorityOverride=0;Z.__detect_form_submit_events.isInfrastructure=!1;Z.__detect_form_submit_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&
f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},T:a}})}();
Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data["5"]=!1;
Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.F="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!pb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},T:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!pb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!pb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();




Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.F="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct["5"]=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[K.m.rf]=d);c[K.m.Kg]=b.vtp_eventSettings;c[K.m.ik]=b.vtp_dynamicEventSettings;c[K.m.he]=b.vtp_googleSignals===1;c[K.m.Bk]=b.vtp_foreignTld;c[K.m.zk]=b.vtp_restrictDomain===
1;c[K.m.ei]=b.vtp_internalTrafficResults;var e=K.m.Wa,f=b.vtp_linker;f&&f[K.m.oa]&&(f[K.m.oa]=a(f[K.m.oa]));c[e]=f;var g=K.m.fi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Yq(b.vtp_trackingId,c);JO(b.vtp_trackingId,b.vtp_gtmEventId);Qc(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=cx(String(b.streamId),d,c);fx(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;
Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.F="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();


Z.securityGroups.zone=[],function(){var a={},b=function(d){for(var e=0;e<d.length;e++)if(!d[e])return!1;return!0},c=function(d){var e=b(d.vtp_boundaries||[]);if(d.vtp_gtmTagId in a)BG(a[d.vtp_gtmTagId],d.vtp_gtmEventId,e);else if(e){var f=d.vtp_childContainers.map(function(n){return n.publicId}),g=d.vtp_enableTypeRestrictions?d.vtp_whitelistedTypes.map(function(n){return n.typeId}):null,h={};var m=DG(d.vtp_gtmEventId,f,g,h,fC(1,d.vtp_gtmEntityIndex,d.vtp_gtmEntityName),!!d.vtp_inheritParentConfig);a[d.vtp_gtmTagId]=m}Qc(d.vtp_gtmOnSuccess)};Z.__zone=c;Z.__zone.F="zone";Z.__zone.isVendorTemplate=!0;Z.__zone.priorityOverride=0;Z.__zone.isInfrastructure=
!1;Z.__zone["5"]=!0}();

Z.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_form_interaction_events=b;Z.__detect_form_interaction_events.F="detect_form_interaction_events";Z.__detect_form_interaction_events.isVendorTemplate=!0;Z.__detect_form_interaction_events.priorityOverride=0;Z.__detect_form_interaction_events.isInfrastructure=!1;Z.__detect_form_interaction_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();
var Kp={dataLayer:Fk,callback:function(a){tk.hasOwnProperty(a)&&ob(tk[a])&&tk[a]();delete tk[a]},bootstrap:0};
function JQ(){Jp();Um();aC();Hb(uk,Z.securityGroups);var a=Rm(Gm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;gp(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||N(142);Tf={Jo:hg}}var KQ=!1;F(218)&&(KQ=Ni(47,KQ));
function ro(){try{if(KQ||!an()){ck();Zj.P=Oi(18,"");
Zj.pb=Ri(4,'ad_storage|analytics_storage|ad_user_data|ad_personalization');Zj.Sa=Ri(5,'ad_storage|analytics_storage|ad_user_data');Zj.Ea=Ri(11,'57f0');Zj.Ea=Ri(10,'57f0');
if(F(109)){}Sa[7]=!0;var a=Ip("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});np(a);Gp();nF();wr();Mp();if(Vm()){IG();RC().removeExternalRestrictions(Om());}else{lz();
Rf();Nf=Z;Of=YE;jg=new qg;sQ();JQ();WE();po||(oo=to());Cp();eE();sD();MD=!1;A.readyState==="complete"?OD():Oc(x,"load",OD);mD();tl&&(zq(Nq),x.setInterval(Mq,864E5),zq(oF),zq(EC),zq(sA),zq(Qq),zq(wF),zq(PC),F(120)&&(zq(JC),zq(KC),zq(LC)),pF={},qF={},zq(sF),zq(tF),Si());ul&&(bo(),fq(),gE(),nE(),lE(),Un("bt",String(Zj.C?2:Zj.N?1:0)),Un("ct",String(Zj.C?0:Zj.N?1:3)),jE());NE();mo(1);JG();sE();sk=Db();Kp.bootstrap=sk;Zj.la&&dE();F(109)&&OA();F(134)&&(typeof x.name==="string"&&Jb(x.name,"web-pixel-sandbox-CUSTOM")&&ed()?vQ("dMDg0Yz"):x.Shopify&&(vQ("dN2ZkMj"),ed()&&vQ("dNTU0Yz")))}}}catch(b){mo(4),Jq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Uo(n)&&(m=h.Wk)}function c(){m&&Ac?g(m):a()}if(!x[Oi(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=el(A.referrer);d=al(e,"host")===Oi(38,"cct.google")}if(!d){var f=ys(Oi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Oi(37,"__TAGGY_INSTALLED")]=!0,Jc(Oi(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";nk&&(v="OGT",w="GTAG");
var y=Oi(23,"google.tagmanager.debugui2.queue"),z=x[y];z||(z=[],x[y]=z,Jc("https://"+dk.xg+"/debug/bootstrap?id="+ng.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Yr()));var B={messageType:"CONTAINER_STARTING",data:{scriptSource:Ac,containerProduct:v,debug:!1,id:ng.ctid,targetRef:{ctid:ng.ctid,isDestination:Mm()},aliases:Pm(),destinations:Nm()}};B.data.resume=function(){a()};dk.Nm&&(B.data.initialPublish=!0);z.push(B)},h={Wn:1,Zk:2,sl:3,Uj:4,Wk:5};h[h.Wn]="GTM_DEBUG_LEGACY_PARAM";h[h.Zk]="GTM_DEBUG_PARAM";h[h.sl]="REFERRER";
h[h.Uj]="COOKIE";h[h.Wk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Zk(x.location,"query",!1,void 0,"gtm_debug");Uo(p)&&(m=h.Zk);if(!m&&A.referrer){var q=el(A.referrer);al(q,"host")===Oi(24,"tagassistant.google.com")&&(m=h.sl)}if(!m){var r=ys("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Uj)}m||b();if(!m&&To(n)){var t=!1;Oc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){F(83)&&KQ&&!to()["0"]?qo():ro()});

})()


/**
 * 🌸 LOTUS GLASS - CẬP NHẬT LOGIC CATEGORIES CHO CODE-GAS.GS V5.0
 * File này chứa logic cập nhật để xử lý categories với cấu trúc cây
 */

// ===================================================================================
// ENHANCED CATEGORIES LOADING - HỖ TRỢ CẤU TRÚC CÂY
// ===================================================================================

/**
 * Cập nhật hàm loadCategories để sử dụng API mới
 * THAY THẾ HÀM loadCategories CŨ TRONG BLOGTHEMEN.XML
 */
async function loadCategories() {
  try {
    console.log('📂 Loading categories tree...');
    
    const response = await LotusAPIClient.getCategories();
    
    if (!response.success || !response.data) {
      console.warn('⚠️ No categories data received');
      return;
    }
    
    // Lưu categories vào global variable để sử dụng
    window.allCategories = response.data;
    
    // Render categories tree
    renderCategoriesTree(response.data);
    
    // Render featured categories slider
    renderFeaturedCategoriesSlider(response.data);
    
    console.log(`✅ Loaded ${response.data.length} categories`);
    
  } catch (error) {
    console.error('🚨 Error loading categories:', error);
  }
}

/**
 * Render categories tree cho sidebar/filter
 * THAY THẾ HÀM renderCategories CŨ TRONG BLOGTHEMEN.XML
 */
function renderCategoriesTree(categories) {
  const categoriesBar = document.getElementById('categoriesBar');
  if (!categoriesBar) return;
  
  // Build categories tree HTML
  const categoriesHTML = buildCategoriesTreeHTML(categories);
  
  categoriesBar.innerHTML = `
    <div class="categories-tree">
      <div class="category-item all-categories ${state.category === '' ? 'active' : ''}" 
           data-category="" onclick="selectCategory('')">
        <span class="category-icon">📦</span>
        <span class="category-name">Tất cả sản phẩm</span>
      </div>
      ${categoriesHTML}
    </div>
  `;
  
  console.log('🌳 Categories tree rendered');
}

/**
 * Build HTML cho categories tree với cấu trúc đệ quy
 */
function buildCategoriesTreeHTML(categories, level = 0) {
  return categories.map(category => {
    const hasChildren = category.children && category.children.length > 0;
    const isActive = state.category === category.category_id;
    const indent = level * 20;
    
    let html = `
      <div class="category-item ${isActive ? 'active' : ''}" 
           data-category="${category.category_id}" 
           onclick="selectCategory('${category.category_id}')"
           style="padding-left: ${indent}px">
        <span class="category-icon">${category.icon || '📁'}</span>
        <span class="category-name">${category.name}</span>
        <span class="category-count">(${category.product_count || 0})</span>
        ${hasChildren ? '<span class="category-toggle" onclick="toggleCategoryTree(event, this)">▼</span>' : ''}
      </div>
    `;
    
    if (hasChildren) {
      html += `
        <div class="category-children" style="display: none;">
          ${buildCategoriesTreeHTML(category.children, level + 1)}
        </div>
      `;
    }
    
    return html;
  }).join('');
}

/**
 * Toggle category tree expansion
 */
function toggleCategoryTree(event, toggleElement) {
  event.stopPropagation(); // Prevent category selection
  
  const categoryItem = toggleElement.closest('.category-item');
  const childrenContainer = categoryItem.nextElementSibling;
  
  if (childrenContainer && childrenContainer.classList.contains('category-children')) {
    const isExpanded = childrenContainer.style.display !== 'none';
    
    if (isExpanded) {
      childrenContainer.style.display = 'none';
      toggleElement.textContent = '▶';
    } else {
      childrenContainer.style.display = 'block';
      toggleElement.textContent = '▼';
    }
  }
}

/**
 * Select category và load products
 */
function selectCategory(categoryId) {
  // Update state
  state.category = categoryId;
  state.page = 1; // Reset to first page
  
  // Update active category in UI
  document.querySelectorAll('.category-item').forEach(item => {
    item.classList.remove('active');
  });
  
  const selectedItem = document.querySelector(`[data-category="${categoryId}"]`);
  if (selectedItem) {
    selectedItem.classList.add('active');
  }
  
  // Load products for selected category
  fetchProducts();
  
  // Update URL
  updateURL();
  
  console.log(`📂 Selected category: ${categoryId || 'All'}`);
}

/**
 * Render featured categories slider cho homepage
 */
function renderFeaturedCategoriesSlider(categories) {
  const slider = document.getElementById('featuredCategoriesSlider');
  if (!slider) return;
  
  // Lấy categories có featured = true hoặc top categories
  const featuredCategories = categories.filter(cat => 
    cat.featured || cat.product_count > 10
  ).slice(0, 8);
  
  if (featuredCategories.length === 0) return;
  
  const sliderHTML = featuredCategories.map(category => {
    const image = category.image || 'https://dummyimage.com/250x180/f7931e/ffffff&amp;text=' + encodeURIComponent(category.name);
    
    return `
      <div class="featured-category-card" onclick="selectCategory('${category.category_id}')">
        <div class="featured-category-image">
          <img src="${image}" alt="${category.name}" loading="lazy"/>
        </div>
        <div class="featured-category-info">
          <h3>${category.name}</h3>
          <p>${category.product_count || 0} sản phẩm</p>
        </div>
      </div>
    `;
  }).join('');
  
  slider.innerHTML = `
    <div class="featured-categories-header">
      <h2>Danh mục nổi bật</h2>
      <p>Khám phá các danh mục sản phẩm phổ biến</p>
    </div>
    <div class="featured-categories-grid">
      ${sliderHTML}
    </div>
  `;
  
  console.log(`🌟 Featured categories slider rendered with ${featuredCategories.length} categories`);
}

/**
 * Get category by ID từ categories tree
 */
function getCategoryById(categoryId, categories = null) {
  if (!categories) categories = window.allCategories || [];
  
  for (const category of categories) {
    if (category.category_id === categoryId) {
      return category;
    }
    
    if (category.children && category.children.length > 0) {
      const found = getCategoryById(categoryId, category.children);
      if (found) return found;
    }
  }
  
  return null;
}

/**
 * Get category path (breadcrumb) cho một category
 */
function getCategoryPath(categoryId, categories = null) {
  if (!categoryId) return [];
  
  function findPath(categories, targetId, currentPath = []) {
    for (const category of categories) {
      const newPath = [...currentPath, category];
      
      if (category.category_id === targetId) {
        return newPath;
      }
      
      if (category.children && category.children.length > 0) {
        const found = findPath(category.children, targetId, newPath);
        if (found) return found;
      }
    }
    return null;
  }
  
  return findPath(categories || window.allCategories || [], categoryId) || [];
}

/**
 * Render breadcrumb cho category hiện tại
 */
function renderCategoryBreadcrumb() {
  const breadcrumbContainer = document.getElementById('categoryBreadcrumb');
  if (!breadcrumbContainer || !state.category) return;
  
  const path = getCategoryPath(state.category);
  
  if (path.length === 0) {
    breadcrumbContainer.innerHTML = '';
    return;
  }
  
  const breadcrumbHTML = path.map((category, index) => {
    const isLast = index === path.length - 1;
    
    if (isLast) {
      return `<span class="breadcrumb-current">${category.name}</span>`;
    } else {
      return `<a href="#" class="breadcrumb-link" onclick="selectCategory('${category.category_id}')">${category.name}</a>`;
    }
  }).join(' <span class="breadcrumb-separator">></span> ');
  
  breadcrumbContainer.innerHTML = `
    <div class="breadcrumb">
      <a href="#" class="breadcrumb-link" onclick="selectCategory('')">Trang chủ</a>
      <span class="breadcrumb-separator">></span>
      ${breadcrumbHTML}
    </div>
  `;
}

/**
 * Enhanced search trong categories
 */
function searchCategories(query) {
  if (!query || !window.allCategories) return [];
  
  const results = [];
  
  function searchInCategories(categories, query) {
    for (const category of categories) {
      if (category.name.toLowerCase().includes(query.toLowerCase())) {
        results.push(category);
      }
      
      if (category.children && category.children.length > 0) {
        searchInCategories(category.children, query);
      }
    }
  }
  
  searchInCategories(window.allCategories, query);
  return results;
}

/**
 * Initialize categories khi page load
 */
function initializeCategories() {
  // Load categories from API
  loadCategories();
  
  // Setup category search if exists
  const categorySearch = document.getElementById('categorySearch');
  if (categorySearch) {
    categorySearch.addEventListener('input', function(e) {
      const query = e.target.value.trim();
      
      if (query.length >= 2) {
        const results = searchCategories(query);
        renderCategorySearchResults(results);
      } else {
        renderCategoriesTree(window.allCategories || []);
      }
    });
  }
}

/**
 * Render category search results
 */
function renderCategorySearchResults(results) {
  const categoriesBar = document.getElementById('categoriesBar');
  if (!categoriesBar) return;
  
  if (results.length === 0) {
    categoriesBar.innerHTML = '<div class="no-categories">Không tìm thấy danh mục nào</div>';
    return;
  }
  
  const resultsHTML = results.map(category => {
    const path = getCategoryPath(category.category_id);
    const pathText = path.map(c => c.name).join(' > ');
    
    return `
      <div class="category-search-result" onclick="selectCategory('${category.category_id}')">
        <div class="category-name">${category.name}</div>
        <div class="category-path">${pathText}</div>
        <div class="category-count">${category.product_count || 0} sản phẩm</div>
      </div>
    `;
  }).join('');
  
  categoriesBar.innerHTML = `
    <div class="category-search-results">
      <div class="search-results-header">Kết quả tìm kiếm (${results.length})</div>
      ${resultsHTML}
    </div>
  `;
}

// Initialize categories when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Delay để đảm bảo API client đã load
  setTimeout(initializeCategories, 100);
});

console.log('🌸 Lotus Glass Categories Update V5.0 loaded successfully!');

<!-- ============================================================================
     LOTUS GLASS - COMPLETE CHECKOUT MODAL HTML
     File: lotus-checkout-html.html
     
     Thêm vào cuối body, sau cart sidebar
     ============================================================================ -->

<!-- Checkout Modal -->
<div id="checkout-modal" class="checkout-modal" role="dialog" aria-labelledby="checkout-title" aria-hidden="true">
  <div class="checkout-container">
    <!-- Checkout Header -->
    <div class="checkout-header">
      <h2 id="checkout-title" class="checkout-title"><PERSON><PERSON> <PERSON><PERSON> đơn hàng</h2>
      <button id="checkout-close" class="checkout-close" aria-label="Đóng checkout">
        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
    </div>

    <!-- Progress Indicator -->
    <div class="checkout-progress">
      <div class="progress-step active" data-step="1">
        <div class="step-number">1</div>
        <span>Thông tin</span>
      </div>
      <div class="progress-line"></div>
      <div class="progress-step" data-step="2">
        <div class="step-number">2</div>
        <span>Thanh toán</span>
      </div>
      <div class="progress-line"></div>
      <div class="progress-step" data-step="3">
        <div class="step-number">3</div>
        <span>Xác nhận</span>
      </div>
    </div>

    <!-- Checkout Body -->
    <div class="checkout-body">
      <!-- Form Section -->
      <div class="checkout-form-section">
        <form id="checkout-form" class="checkout-form" novalidate>
          
          <!-- Customer Information Section -->
          <div class="form-section" id="customer-info-section">
            <h3 class="section-title">
              <svg class="section-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
              Thông tin khách hàng
            </h3>
            
            <div class="form-grid two-column">
              <div class="form-group">
                <label for="customer-name" class="form-label">
                  Họ và tên <span class="required">*</span>
                </label>
                <input 
                  type="text" 
                  id="customer-name" 
                  name="customerName" 
                  class="form-input" 
                  placeholder="Nhập họ và tên"
                  required
                  autocomplete="name"
                />
                <div class="form-error" id="customer-name-error"></div>
              </div>
              
              <div class="form-group">
                <label for="customer-phone" class="form-label">
                  Số điện thoại <span class="required">*</span>
                </label>
                <input 
                  type="tel" 
                  id="customer-phone" 
                  name="customerPhone" 
                  class="form-input" 
                  placeholder="0987 654 321"
                  required
                  autocomplete="tel"
                />
                <div class="form-error" id="customer-phone-error"></div>
                <div class="form-help">Để liên hệ xác nhận đơn hàng</div>
              </div>
            </div>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="customer-email" class="form-label">
                  Email
                </label>
                <input 
                  type="email" 
                  id="customer-email" 
                  name="customerEmail" 
                  class="form-input" 
                  placeholder="<EMAIL>"
                  autocomplete="email"
                />
                <div class="form-error" id="customer-email-error"></div>
                <div class="form-help">Để nhận xác nhận đơn hàng qua email</div>
              </div>
            </div>
          </div>

          <!-- Company Information Section (Optional) -->
          <div class="form-section" id="company-info-section">
            <h3 class="section-title">
              <svg class="section-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
              </svg>
              Thông tin công ty (tùy chọn)
            </h3>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="company-name" class="form-label">
                  Tên công ty/đơn vị
                </label>
                <input 
                  type="text" 
                  id="company-name" 
                  name="companyName" 
                  class="form-input" 
                  placeholder="Tên công ty"
                  autocomplete="organization"
                />
              </div>
              
              <div class="form-group">
                <label for="tax-id" class="form-label">
                  Mã số thuế
                </label>
                <input 
                  type="text" 
                  id="tax-id" 
                  name="taxId" 
                  class="form-input" 
                  placeholder="0123456789"
                />
              </div>
            </div>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="company-address" class="form-label">
                  Địa chỉ công ty
                </label>
                <textarea 
                  id="company-address" 
                  name="companyAddress" 
                  class="form-textarea" 
                  rows="2"
                  placeholder="Địa chỉ công ty để xuất hóa đơn"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Shipping Address Section -->
          <div class="form-section" id="shipping-address-section">
            <h3 class="section-title">
              <svg class="section-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
              </svg>
              Địa chỉ giao hàng
            </h3>
            
            <div class="form-grid two-column">
              <div class="form-group">
                <label for="shipping-province" class="form-label">
                  Tỉnh/Thành phố <span class="required">*</span>
                </label>
                <select 
                  id="shipping-province" 
                  name="shippingProvince" 
                  class="form-select" 
                  required
                >
                  <option value="">Chọn Tỉnh/Thành phố</option>
                  <option value="Hồ Chí Minh">TP. Hồ Chí Minh</option>
                  <option value="Hà Nội">Hà Nội</option>
                  <option value="Đà Nẵng">Đà Nẵng</option>
                  <option value="Cần Thơ">Cần Thơ</option>
                  <option value="An Giang">An Giang</option>
                  <option value="Bà Rịa - Vũng Tàu">Bà Rịa - Vũng Tàu</option>
                  <option value="Bạc Liêu">Bạc Liêu</option>
                  <option value="Bắc Giang">Bắc Giang</option>
                  <option value="Bắc Kạn">Bắc Kạn</option>
                  <option value="Bắc Ninh">Bắc Ninh</option>
                  <option value="Bến Tre">Bến Tre</option>
                  <option value="Bình Dương">Bình Dương</option>
                  <option value="Bình Định">Bình Định</option>
                  <option value="Bình Phước">Bình Phước</option>
                  <option value="Bình Thuận">Bình Thuận</option>
                  <option value="Cà Mau">Cà Mau</option>
                  <option value="Cao Bằng">Cao Bằng</option>
                  <option value="Đắk Lắk">Đắk Lắk</option>
                  <option value="Đắk Nông">Đắk Nông</option>
                  <option value="Điện Biên">Điện Biên</option>
                  <option value="Đồng Nai">Đồng Nai</option>
                  <option value="Đồng Tháp">Đồng Tháp</option>
                  <option value="Gia Lai">Gia Lai</option>
                  <option value="Hà Giang">Hà Giang</option>
                  <option value="Hà Nam">Hà Nam</option>
                  <option value="Hà Tĩnh">Hà Tĩnh</option>
                  <option value="Hải Dương">Hải Dương</option>
                  <option value="Hải Phòng">Hải Phòng</option>
                  <option value="Hậu Giang">Hậu Giang</option>
                  <option value="Hòa Bình">Hòa Bình</option>
                  <option value="Hưng Yên">Hưng Yên</option>
                  <option value="Khánh Hòa">Khánh Hòa</option>
                  <option value="Kiên Giang">Kiên Giang</option>
                  <option value="Kon Tum">Kon Tum</option>
                  <option value="Lai Châu">Lai Châu</option>
                  <option value="Lâm Đồng">Lâm Đồng</option>
                  <option value="Lạng Sơn">Lạng Sơn</option>
                  <option value="Lào Cai">Lào Cai</option>
                  <option value="Long An">Long An</option>
                  <option value="Nam Định">Nam Định</option>
                  <option value="Nghệ An">Nghệ An</option>
                  <option value="Ninh Bình">Ninh Bình</option>
                  <option value="Ninh Thuận">Ninh Thuận</option>
                  <option value="Phú Thọ">Phú Thọ</option>
                  <option value="Phú Yên">Phú Yên</option>
                  <option value="Quảng Bình">Quảng Bình</option>
                  <option value="Quảng Nam">Quảng Nam</option>
                  <option value="Quảng Ngãi">Quảng Ngãi</option>
                  <option value="Quảng Ninh">Quảng Ninh</option>
                  <option value="Quảng Trị">Quảng Trị</option>
                  <option value="Sóc Trăng">Sóc Trăng</option>
                  <option value="Sơn La">Sơn La</option>
                  <option value="Tây Ninh">Tây Ninh</option>
                  <option value="Thái Bình">Thái Bình</option>
                  <option value="Thái Nguyên">Thái Nguyên</option>
                  <option value="Thanh Hóa">Thanh Hóa</option>
                  <option value="Thừa Thiên Huế">Thừa Thiên Huế</option>
                  <option value="Tiền Giang">Tiền Giang</option>
                  <option value="Trà Vinh">Trà Vinh</option>
                  <option value="Tuyên Quang">Tuyên Quang</option>
                  <option value="Vĩnh Long">Vĩnh Long</option>
                  <option value="Vĩnh Phúc">Vĩnh Phúc</option>
                  <option value="Yên Bái">Yên Bái</option>
                </select>
                <div class="form-error" id="shipping-province-error"></div>
              </div>
              
              <div class="form-group">
                <label for="shipping-district" class="form-label">
                  Quận/Huyện <span class="required">*</span>
                </label>
                <select 
                  id="shipping-district" 
                  name="shippingDistrict" 
                  class="form-select" 
                  required
                  disabled
                >
                  <option value="">Chọn Quận/Huyện</option>
                </select>
                <div class="form-error" id="shipping-district-error"></div>
              </div>
            </div>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="shipping-address" class="form-label">
                  Địa chỉ cụ thể <span class="required">*</span>
                </label>
                <textarea 
                  id="shipping-address" 
                  name="shippingAddress" 
                  class="form-textarea" 
                  rows="2"
                  placeholder="Số nhà, tên đường, phường/xã..."
                  required
                ></textarea>
                <div class="form-error" id="shipping-address-error"></div>
              </div>
            </div>
          </div>

          <!-- Payment Method Section -->
          <div class="form-section" id="payment-method-section">
            <h3 class="section-title">
              <svg class="section-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
              </svg>
              Phương thức thanh toán
            </h3>
            
            <div class="payment-methods">
              <!-- VNPay Option -->
              <div class="payment-option" data-method="vnpay">
                <input 
                  type="radio" 
                  id="payment-vnpay" 
                  name="paymentMethod" 
                  value="vnpay" 
                  class="payment-radio"
                />
                <div class="payment-icon">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzAwNTVBQSIvPgo8cGF0aCBkPSJNMTAgMTJIMzBWMjhIMTBWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSIyMCIgeT0iMjIiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSIjMDA1NUFBIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5WTlBBWTwvdGV4dD4KPC9zdmc+" alt="VNPay" />
                </div>
                <div class="payment-info">
                  <div class="payment-name">VNPay</div>
                  <div class="payment-description">Thanh toán qua ví điện tử, thẻ ATM, Internet Banking</div>
                </div>
                <div class="payment-fee">Miễn phí</div>
              </div>

              <!-- Bank Transfer Option -->
              <div class="payment-option" data-method="bank_transfer">
                <input 
                  type="radio" 
                  id="payment-bank" 
                  name="paymentMethod" 
                  value="bank_transfer" 
                  class="payment-radio"
                />
                <div class="payment-icon">
                  <svg viewBox="0 0 40 40" fill="none">
                    <rect width="40" height="40" rx="8" fill="#2E8B57"/>
                    <path d="M8 12H32L20 8L8 12Z" fill="white"/>
                    <path d="M10 14H30V28H10V14Z" fill="white"/>
                    <path d="M12 16H14V26H12V16Z" fill="#2E8B57"/>
                    <path d="M18 16H20V26H18V16Z" fill="#2E8B57"/>
                    <path d="M24 16H26V26H24V16Z" fill="#2E8B57"/>
                    <path d="M8 30H32V32H8V30Z" fill="white"/>
                  </svg>
                </div>
                <div class="payment-info">
                  <div class="payment-name">Chuyển khoản ngân hàng</div>
                  <div class="payment-description">Chuyển khoản trực tiếp qua tài khoản ngân hàng</div>
                </div>
                <div class="payment-fee">Miễn phí</div>
              </div>

              <!-- COD Option -->
              <div class="payment-option" data-method="cod">
                <input 
                  type="radio" 
                  id="payment-cod" 
                  name="paymentMethod" 
                  value="cod" 
                  class="payment-radio"
                />
                <div class="payment-icon">
                  <svg viewBox="0 0 40 40" fill="none">
                    <rect width="40" height="40" rx="8" fill="#FF6B35"/>
                    <path d="M12 10H28V30H12V10Z" fill="white"/>
                    <path d="M14 14H26V16H14V14Z" fill="#FF6B35"/>
                    <path d="M14 18H26V20H14V18Z" fill="#FF6B35"/>
                    <path d="M14 22H22V24H14V22Z" fill="#FF6B35"/>
                    <circle cx="26" cy="24" r="2" fill="#FF6B35"/>
                  </svg>
                </div>
                <div class="payment-info">
                  <div class="payment-name">Thanh toán khi nhận hàng (COD)</div>
                  <div class="payment-description">Thanh toán bằng tiền mặt khi nhận hàng</div>
                </div>
                <div class="payment-fee" id="cod-fee">Phí COD: 0₫</div>
              </div>
            </div>
            
            <div class="form-error" id="payment-method-error"></div>
          </div>

          <!-- Order Notes Section -->
          <div class="form-section" id="order-notes-section">
            <h3 class="section-title">
              <svg class="section-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,10H19.5L14,4.5V10M5,3H15L21,9V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3M9,12H16V14H9V12M9,16H14V18H9V16Z"/>
              </svg>
              Ghi chú đơn hàng (tùy chọn)
            </h3>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="order-notes" class="form-label">
                  Ghi chú
                </label>
                <textarea 
                  id="order-notes" 
                  name="orderNotes" 
                  class="form-textarea" 
                  rows="3"
                  placeholder="Ghi chú thêm về đơn hàng, yêu cầu đặc biệt..."
                  maxlength="500"
                ></textarea>
                <div class="form-help">Tối đa 500 ký tự</div>
              </div>
            </div>
          </div>

        </form>
      </div>

      <!-- Order Summary Section -->
      <div class="checkout-summary-section">
        <div class="order-summary">
          <div class="summary-title">
            Đơn hàng của bạn
            <span class="item-count" id="summary-item-count">0</span>
          </div>
          
          <div class="summary-items" id="summary-items">
            <!-- Order items will be populated by JavaScript -->
          </div>
          
          <div class="summary-divider"></div>
          
          <div class="summary-totals">
            <div class="total-row subtotal">
              <span>Tạm tính:</span>
              <span class="total-value" id="summary-subtotal">0₫</span>
            </div>
            
            <div class="total-row shipping">
              <span>Phí vận chuyển:</span>
              <span class="total-value" id="summary-shipping">Miễn phí</span>
            </div>
            
            <div class="total-row discount" id="summary-discount-row" style="display: none;">
              <span>Giảm giá:</span>
              <span class="total-value" id="summary-discount">-0₫</span>
            </div>
            
            <div class="total-row final">
              <span>Tổng cộng:</span>
              <span class="total-value final" id="summary-total">0₫</span>
            </div>
          </div>
        </div>

        <!-- Promotion Code Section -->
        <div class="order-summary">
          <div class="summary-title">Mã giảm giá</div>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="promotion-code" class="form-label">
                Nhập mã giảm giá
              </label>
              <div style="display: flex; gap: 8px;">
                <input 
                  type="text" 
                  id="promotion-code" 
                  name="promotionCode" 
                  class="form-input" 
                  placeholder="Mã giảm giá"
                  style="flex: 1;"
                />
                <button 
                  type="button" 
                  id="apply-promotion" 
                  class="checkout-btn checkout-btn-secondary"
                  style="flex: none; padding: 12px 16px; font-size: 14px;"
                >
                  Áp dụng
                </button>
              </div>
              <div class="form-error" id="promotion-code-error"></div>
              <div class="form-success" id="promotion-code-success"></div>
            </div>
          </div>
        </div>

        <!-- Order Security Info -->
        <div class="order-summary">
          <div class="summary-title">🔒 Thông tin bảo mật</div>
          <div style="font-size: 13px; color: var(--cart-secondary); line-height: 1.5;">
            <p style="margin: 0 0 8px 0;">✅ Thông tin của bạn được mã hóa và bảo mật</p>
            <p style="margin: 0 0 8px 0;">✅ Không lưu trữ thông tin thanh toán</p>
            <p style="margin: 0;">✅ Tuân thủ tiêu chuẩn bảo mật quốc tế</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Checkout Actions -->
    <div class="checkout-actions">
      <button 
        type="button" 
        id="checkout-back" 
        class="checkout-btn checkout-btn-secondary"
      >
        <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        Quay lại giỏ hàng
      </button>
      
      <button 
        type="submit" 
        form="checkout-form"
        id="place-order" 
        class="checkout-btn checkout-btn-primary"
        disabled
      >
        <span id="place-order-text">Đặt hàng ngay</span>
        <div id="place-order-spinner" class="checkout-spinner" style="display: none;"></div>
      </button>
    </div>
  </div>
</div>

<!-- Order Success Modal -->
<div id="order-success-modal" class="checkout-modal" role="dialog" aria-labelledby="success-title" aria-hidden="true">
  <div class="checkout-container" style="max-width: 600px;">
    <div class="checkout-header" style="background: linear-gradient(135deg, #28a745 0%, #20c947 100%);">
      <h2 id="success-title" class="checkout-title">
        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24" style="margin-right: 8px;">
          <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
        </svg>
        Đặt hàng thành công!
      </h2>
      <button id="success-close" class="checkout-close" aria-label="Đóng">
        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
    </div>
    
    <div class="checkout-body" style="flex-direction: column; padding: 32px; text-align: center;">
      <div style="margin-bottom: 24px;">
        <div style="font-size: 64px; margin-bottom: 16px;">🎉</div>
        <h3 style="margin: 0 0 8px 0; color: var(--cart-dark); font-size: 20px;">
          Cảm ơn bạn đã đặt hàng!
        </h3>
        <p style="margin: 0; color: var(--cart-secondary); font-size: 15px;">
          Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.
        </p>
      </div>
      
      <div id="order-details" style="background: var(--cart-light); padding: 24px; border-radius: var(--cart-radius); margin-bottom: 24px; text-align: left;">
        <!-- Order details will be populated by JavaScript -->
      </div>
      
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="track-order" class="checkout-btn checkout-btn-secondary">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          Theo dõi đơn hàng
        </button>
        
        <button id="continue-shopping" class="checkout-btn checkout-btn-primary">
          <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
            <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm4 10a1 1 0 0 1-2 0v-3a1 1 0 0 1 2 0v3z"/>
          </svg>
          Tiếp tục mua sắm
        </button>
      </div>
    </div>
  </div>
</div>

<!-- District Data Template (Hidden) -->
<script type="application/json" id="districts-data">
{
  "Hồ Chí Minh": [
    "Quận 1", "Quận 2", "Quận 3", "Quận 4", "Quận 5", "Quận 6", "Quận 7", "Quận 8", "Quận 9", "Quận 10", "Quận 11", "Quận 12",
    "Quận Bình Tân", "Quận Bình Thạnh", "Quận Gò Vấp", "Quận Phú Nhuận", "Quận Tân Bình", "Quận Tân Phú", "Quận Thủ Đức",
    "Huyện Bình Chánh", "Huyện Cần Giờ", "Huyện Củ Chi", "Huyện Hóc Môn", "Huyện Nhà Bè"
  ],
  "Hà Nội": [
    "Quận Ba Đình", "Quận Cầu Giấy", "Quận Đống Đa", "Quận Hà Đông", "Quận Hai Bà Trưng", "Quận Hoàn Kiếm", "Quận Hoàng Mai", 
    "Quận Long Biên", "Quận Nam Từ Liêm", "Quận Bắc Từ Liêm", "Quận Tây Hồ", "Quận Thanh Xuân",
    "Huyện Ba Vì", "Huyện Chương Mỹ", "Huyện Đan Phượng", "Huyện Đông Anh", "Huyện Gia Lâm", "Huyện Hoài Đức", 
    "Huyện Mê Linh", "Huyện Mỹ Đức", "Huyện Phú Xuyên", "Huyện Phúc Thọ", "Huyện Quốc Oai", "Huyện Sóc Sơn", 
    "Huyện Thạch Thất", "Huyện Thanh Oai", "Huyện Thanh Trì", "Huyện Thường Tín", "Huyện Ứng Hòa", "Thị xã Sơn Tây"
  ],
  "Đà Nẵng": [
    "Quận Hải Châu", "Quận Thanh Khê", "Quận Sơn Trà", "Quận Ngũ Hành Sơn", "Quận Liên Chiểu", "Quận Cẩm Lệ",
    "Huyện Hòa Vang", "Huyện Hoàng Sa"
  ]
}
</script>
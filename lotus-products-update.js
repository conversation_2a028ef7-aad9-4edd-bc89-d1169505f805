/**
 * 🌸 LOTUS GLASS - CẬP NHẬT LOGIC LOAD SẢN PHẨM CHO CODE-GAS.GS V5.0
 * File này chứa logic cập nhật để xử lý sản phẩm với API mới
 */

// ===================================================================================
// ENHANCED PRODUCT LOADING - TƯƠNG THÍCH VỚI GROUPED PRODUCTS
// ===================================================================================

/**
 * Cập nhật hàm fetchData để sử dụng LotusAPIClient mới
 * THAY THẾ HÀM fetchData CŨ TRONG BLOGTHEMEN.XML
 */
async function fetchData(params) {
  try {
    const action = params.action;
    
    switch (action) {
      case 'getCategories':
        const categoriesResponse = await LotusAPIClient.getCategories();
        return categoriesResponse.success ? categoriesResponse : null;
        
      case 'getProducts':
        const productsResponse = await LotusAPIClient.getProducts({
          page: params.page,
          pageSize: params.pageSize,
          category: params.category,
          sort: params.sort,
          query: params.query,
          featured: params.featured,
          new: params.new
        });
        return productsResponse.success ? productsResponse : null;
        
      case 'getProductDetail':
        const detailResponse = await LotusAPIClient.getProductDetail(params.product_name);
        return detailResponse.success ? detailResponse : null;
        
      default:
        console.warn(`Unknown action: ${action}`);
        return null;
    }
  } catch (error) {
    console.error('🚨 fetchData error:', error);
    return null;
  }
}

/**
 * Cập nhật hàm renderProducts để xử lý format mới từ code-gas.gs
 * THAY THẾ HÀM renderProducts CŨ TRONG BLOGTHEMEN.XML
 */
function renderProducts(products, meta, append = false) {
  const listEl = document.getElementById('productsList');
  const loadingEl = document.getElementById('loadingBox');
  
  // Check if elements exist before manipulating them
  if (!listEl) {
    console.warn('⚠️ productsList element not found');
    return;
  }
  
  // Hide loading
  if (loadingEl) {
    loadingEl.style.display = 'none';
  }
  
  if (!products || products.length === 0) {
    if (!append) {
      listEl.innerHTML = '<div class="no-products">Không tìm thấy sản phẩm nào.</div>';
    }
    return;
  }
  
  // Convert new API format to old format for compatibility
  const convertedProducts = products.map(product => ({
    // Mapping từ format mới sang format cũ
    sku: product.product_name.replace(/\s/g, '_'), // Tạo SKU tạm từ tên
    product_name: product.product_name,
    name_product: product.product_name,
    category_id: product.category_id,
    group: product.group,
    price_case_TSG: product.selling_price,
    price_case: product.list_price,
    image_url: product.thumbnail,
    is_featured: product.is_featured,
    is_new: product.is_new,
    hasPromotion: product.hasPromotion,
    // Tạo variants array để tương thích
    variants: [{
      sku: product.product_name.replace(/\s/g, '_'),
      price: product.selling_price,
      images: [product.thumbnail]
    }]
  }));
  
  // Render products using existing logic
  const productsHTML = convertedProducts.map(product => {
    const firstVariant = product.variants[0];
    const price = firstVariant.price || product.price_case_TSG;
    const image = firstVariant.images[0] || product.image_url || '/images/placeholder.jpg';
    
    // Determine badges
    let badges = '';
    if (product.is_new) {
      badges += '<span class="product-badge new-badge">Mới</span>';
    }
    if (product.is_featured) {
      badges += '<span class="product-badge featured-badge">Nổi bật</span>';
    }
    if (product.hasPromotion) {
      badges += '<span class="product-badge promo-badge">Khuyến mãi</span>';
    }
    
    return `
      <div class="product-card" data-sku="${product.sku}" data-product-name="${product.product_name}">
        <div class="product-image">
          <img src="${image}" alt="${product.product_name}" loading="lazy" onerror="this.src='/images/placeholder.jpg'"/>
          <div class="product-badges">${badges}</div>
        </div>
        <div class="product-info info">
          <h3 class="product-name">${product.product_name}</h3>
          <div class="product-price">${formatPrice(price)}</div>
          <button class="product-detail-btn" onclick="showProductDetail('${product.product_name}')">
            Xem chi tiết
          </button>
        </div>
      </div>
    `;
  }).join('');
  
  if (append) {
    listEl.innerHTML += productsHTML;
  } else {
    listEl.innerHTML = productsHTML;
  }
  
  // Update pagination if meta is provided
  if (meta && meta.pagination) {
    updatePagination(meta.pagination);
  }
  
  // Add cart buttons to newly loaded products
  if (window.LotusCart) {
    setTimeout(() => {
      window.LotusCart.setupProductButtons();
    }, 100);
  }
  
  console.log(`📦 Rendered ${products.length} products (append: ${append})`);
}

/**
 * Cập nhật hàm loadFeaturedProducts để sử dụng API mới
 * THAY THẾ HÀM loadFeaturedProducts CŨ TRONG BLOGTHEMEN.XML
 */
async function loadFeaturedProducts() {
  const featuredGrid = document.getElementById('featuredProductsGrid');
  if (!featuredGrid) return;
  
  try {
    console.log('🌟 Loading featured products...');
    
    // Load featured products using new API
    const response = await LotusAPIClient.getProducts({
      featured: true,
      pageSize: 6
    });
    
    if (!response.success || !response.data || response.data.length === 0) {
      console.log('⚠️ No featured products found, loading regular products...');
      
      // Fallback to regular products
      const fallbackResponse = await LotusAPIClient.getProducts({
        pageSize: 6
      });
      
      if (fallbackResponse.success && fallbackResponse.data) {
        renderFeaturedProducts(fallbackResponse.data, 'Nổi bật');
      }
      return;
    }
    
    renderFeaturedProducts(response.data, 'Nổi bật');
    
  } catch (error) {
    console.error('🚨 Error loading featured products:', error);
    featuredGrid.innerHTML = '<p style="text-align: center; color: #6c757d;">Không thể tải sản phẩm nổi bật</p>';
  }
}

/**
 * Cập nhật hàm loadNewProducts để sử dụng API mới
 * THAY THẾ HÀM loadNewProducts CŨ TRONG BLOGTHEMEN.XML
 */
async function loadNewProducts() {
  const newGrid = document.getElementById('newProductsGrid');
  if (!newGrid) return;
  
  try {
    console.log('🆕 Loading new products...');
    
    // Load new products using new API
    const response = await LotusAPIClient.getProducts({
      new: true,
      pageSize: 4
    });
    
    if (!response.success || !response.data || response.data.length === 0) {
      newGrid.innerHTML = '<p style="text-align: center; color: #6c757d; grid-column: 1/-1;">Hiện tại chưa có sản phẩm mới</p>';
      return;
    }
    
    renderFeaturedProducts(response.data, 'Mới', newGrid);
    
  } catch (error) {
    console.error('🚨 Error loading new products:', error);
    newGrid.innerHTML = '<p style="text-align: center; color: #6c757d; grid-column: 1/-1;">Không thể tải sản phẩm mới</p>';
  }
}

/**
 * Helper function để render featured/new products
 */
function renderFeaturedProducts(products, badgeText, container = null) {
  const targetContainer = container || document.getElementById('featuredProductsGrid');
  if (!targetContainer) return;
  
  const productsHTML = products.map(product => {
    const image = product.thumbnail || '/images/placeholder.jpg';
    const badgeClass = badgeText === 'Mới' ? 'new-badge' : 'featured-badge';
    
    return `
      <div class="featured-product-card product-card" data-sku="${product.product_name.replace(/\s/g, '_')}" data-product-name="${product.product_name}">
        <div class="featured-product-image">
          <img src="${image}" alt="${product.product_name}" loading="lazy" onerror="this.src='/images/placeholder.jpg'"/>
          <div class="featured-product-badge ${badgeClass}">${badgeText}</div>
        </div>
        <div class="featured-product-info info">
          <h3 class="featured-product-title product-name">${product.product_name}</h3>
          <div class="featured-product-price product-price">${formatPrice(product.selling_price)}</div>
          <p class="featured-product-description">Sản phẩm thủy tinh cao cấp với thiết kế tinh tế và chất lượng vượt trội.</p>
          <button class="product-detail-btn" onclick="showProductDetail('${product.product_name}')">
            Xem chi tiết
          </button>
        </div>
      </div>
    `;
  }).join('');
  
  targetContainer.innerHTML = productsHTML;
  
  // Add cart buttons to featured products
  if (window.LotusCart) {
    setTimeout(() => {
      window.LotusCart.setupProductButtons();
    }, 100);
  }
  
  console.log(`✨ Rendered ${products.length} ${badgeText.toLowerCase()} products`);
}

/**
 * Enhanced showProductDetail function để sử dụng API mới
 * THAY THẾ HÀM showProductDetail CŨ TRONG BLOGTHEMEN.XML
 */
async function showProductDetail(productName) {
  try {
    console.log(`🔍 Loading product detail: ${productName}`);
    
    // Show loading state
    const modal = document.getElementById('productModal');
    if (modal) {
      modal.style.display = 'block';
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h2>Đang tải...</h2>
            <span class="close" onclick="closeProductModal()">&times;</span>
          </div>
          <div class="modal-body">
            <div class="loading-spinner">Đang tải thông tin sản phẩm...</div>
          </div>
        </div>
      `;
    }
    
    // Load product detail using new API
    const response = await LotusAPIClient.getProductDetail(productName);
    
    if (!response.success || !response.data) {
      throw new Error('Không thể tải thông tin sản phẩm');
    }
    
    const product = response.data;
    
    // Render product detail modal
    renderProductDetailModal(product);
    
  } catch (error) {
    console.error('🚨 Error loading product detail:', error);
    
    const modal = document.getElementById('productModal');
    if (modal) {
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h2>Lỗi</h2>
            <span class="close" onclick="closeProductModal()">&times;</span>
          </div>
          <div class="modal-body">
            <p>Không thể tải thông tin sản phẩm. Vui lòng thử lại sau.</p>
          </div>
        </div>
      `;
    }
  }
}

/**
 * Render product detail modal với format mới
 */
function renderProductDetailModal(product) {
  const modal = document.getElementById('productModal');
  if (!modal) return;
  
  // Build variants HTML
  const variantsHTML = product.variants.map(variant => {
    const images = variant.images.map(img => `<img src="${img}" alt="${variant.name_product}" loading="lazy"/>`).join('');
    const promotions = variant.promotions.map(promo => 
      `<div class="promotion-item">
        <strong>${promo.name}</strong>
        <p>${promo.description}</p>
        ${promo.rules.map(rule => `<div class="promotion-rule">${rule}</div>`).join('')}
      </div>`
    ).join('');
    
    return `
      <div class="variant-item" data-sku="${variant.sku}">
        <div class="variant-images">${images}</div>
        <div class="variant-info">
          <h4>${variant.name_product}</h4>
          <p><strong>SKU:</strong> ${variant.sku}</p>
          <p><strong>Giá:</strong> ${formatPrice(variant.price_case_TSG)}</p>
          <p><strong>Kích thước:</strong> ${variant.height}mm x ${variant.diameter}mm</p>
          <p><strong>Trọng lượng:</strong> ${variant.weight}g</p>
          <p><strong>Thể tích:</strong> ${variant.volume}ml</p>
          ${promotions ? `<div class="promotions">${promotions}</div>` : ''}
        </div>
      </div>
    `;
  }).join('');
  
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h2>${product.product_name}</h2>
        <span class="close" onclick="closeProductModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="product-detail-content">
          <div class="product-description">
            <p>${product.description || 'Sản phẩm thủy tinh cao cấp'}</p>
            <p><strong>Danh mục:</strong> ${product.category.name}</p>
            ${product.category.parent ? `<p><strong>Danh mục cha:</strong> ${product.category.parent.name}</p>` : ''}
            <p><strong>Giá từ:</strong> ${formatPrice(product.selling_price_min)}</p>
          </div>
          <div class="product-variants">
            <h3>Các phiên bản sản phẩm:</h3>
            ${variantsHTML}
          </div>
        </div>
      </div>
    </div>
  `;
  
  modal.style.display = 'block';
}

/**
 * Close product modal
 */
function closeProductModal() {
  const modal = document.getElementById('productModal');
  if (modal) {
    modal.style.display = 'none';
  }
}

console.log('🌸 Lotus Glass Products Update V5.0 loaded successfully!');

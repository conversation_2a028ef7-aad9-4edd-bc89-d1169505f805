/**
 * @fileoverview API Backend cho trang sản phẩm Blogger, được cung cấp bởi Google Apps Script.
 * @version 3.2.0
 * @description Hỗ trợ lọc danh mục đa cấp (cha-con).
 */

// --- CẤU HÌNH CHÍNH ---
const SPREADSHEET_ID = '1G5I-mZymVNxODCe7q0yQIJkcVOFgoYUHuPDOSmc59wY'; 

const SHEETS = {
  CATEGORIES: 'Categories',
  PRODUCTS: 'Products',
  PROMOTIONS: 'Promotions',
  APPLIED_PRODUCTS: 'AppliedProducts',
  PROMOTION_RULES: 'PromotionRules'
};

// --- CẤU HÌNH BỘ ĐỆM (CACHE) ---
const CACHE = CacheService.getScriptCache();
const CACHE_KEY_PREFIX = 'SHEET_DATA_V3.2_'; // Thay đổi phiên bản nếu cấu trúc Sheet thay đổi
const CACHE_EXPIRATION_SECONDS = 300; // Cache dữ liệu trong 5 phút

/**
 * =================================================================
 * BỘ ĐỊNH TUYẾN CHÍNH (API ROUTER)
 * =================================================================
 */
function doGet(e) {
  try {
    const action = e.parameter.action;
    const data = loadAllDataFromCacheOrSheets();
    let response;

    switch (action) {
      case 'getCategories':
        response = apiResponse(getActiveCategories(data.categoriesData));
        break;
      case 'getProducts':
        response = getProductsPaginated(e, data);
        break;
      case 'getProductDetail':
        if (!e.parameter.product_name) {
          throw { code: 400, message: "Tham số 'product_name' là bắt buộc." };
        }
        response = apiResponse(getProductDetail(e.parameter.product_name, data));
        break;
      default:
        throw { code: 400, message: "Hành động không hợp lệ." };
    }
    
    return createJsonResponse(response);

  } catch (err) {
    const status = err.code || 500;
    const message = err.message || (err.toString ? err.toString() : "Lỗi không xác định");
    const errorResp = { success: false, error: true, code: status, message };
    return createJsonResponse(errorResp);
  }
}

/**
 * =================================================================
 * CÁC HÀM API CHÍNH (CORE API FUNCTIONS)
 * =================================================================
 */

// MỚI: Hàm đệ quy để tìm tất cả ID của danh mục con cháu
function getAllDescendantIds(parentId, allCategories) {
  let descendantIds = [];
  const children = allCategories.filter(cat => cat.DanhMucChaID === parentId);
  
  for (const child of children) {
    descendantIds.push(child.CategoryID);
    // Gọi đệ quy để tìm con của cấp tiếp theo
    descendantIds = descendantIds.concat(getAllDescendantIds(child.CategoryID, allCategories));
  }
  
  return descendantIds;
}

function getProductsPaginated(e, data) {
  const groupedProducts = data.productsData
    .filter(p => parseBoolean(p.is_active))
    .reduce((acc, p) => {
      const groupName = p.product_name;
      if (!groupName) return acc;
      if (!acc[groupName]) {
        acc[groupName] = {
          product_name: groupName,
          category_id: p.category_id,
          group: p.group,
          thumbnail: (p.image_url || '').split(',')[0].trim(),
          list_price: Infinity,
          selling_price: Infinity,
          // Lưu trữ các thuộc tính bổ sung để hiển thị trên thẻ sản phẩm
          volume: null,
          height: null,
          diameter: null,
          hasPromotion: false,
          is_featured: false,
          is_new: false
        };
      }
      // Cập nhật giá tối thiểu từ cột price_case và price_case_TSG
      acc[groupName].list_price = Math.min(acc[groupName].list_price, parseFloat(p.price_case) || Infinity);
      acc[groupName].selling_price = Math.min(acc[groupName].selling_price, parseFloat(p.price_case_TSG) || Infinity);
      // Lưu trữ dung tích, chiều cao, đường kính từ biến thể đầu tiên có dữ liệu
      if (!acc[groupName].volume && p.Volume) acc[groupName].volume = p.Volume;
      if (!acc[groupName].height && p.Height_mm) acc[groupName].height = p.Height_mm;
      if (!acc[groupName].diameter && p.Diameter_mm) acc[groupName].diameter = p.Diameter_mm;
      // Kiểm tra cột is_featured / is_new nếu có
      const featVal = (p.is_featured || p.Featured || p.IsFeatured || p.featured);
      if (!acc[groupName].is_featured && parseBoolean(featVal)) acc[groupName].is_featured = true;
      const newVal = (p.is_new || p.New || p.IsNew || p.new);
      if (!acc[groupName].is_new && parseBoolean(newVal)) acc[groupName].is_new = true;
      // Kiểm tra khuyến mãi cho SKU của sản phẩm này; nếu một biến thể có khuyến mãi thì đánh dấu
      if (!acc[groupName].hasPromotion) {
        const promos = getPromotionsForSku(p.sku, data);
        if (promos && promos.length > 0) acc[groupName].hasPromotion = true;
      }
      return acc;
    }, {});

  let products = Object.values(groupedProducts);

  // CẬP NHẬT: Logic lọc theo danh mục
  if (e.parameter.category) {
    const selectedCategoryId = e.parameter.category;
    // Lấy ID của danh mục được chọn và tất cả các ID con cháu của nó
    const targetCategoryIds = [selectedCategoryId, ...getAllDescendantIds(selectedCategoryId, data.categoriesData)];
    // Lọc sản phẩm có category_id nằm trong danh sách đã lấy
    products = products.filter(p => targetCategoryIds.includes(p.category_id));
  }

  // Lọc theo sản phẩm nổi bật hoặc sản phẩm mới nếu có tham số
  if (e.parameter.featured && e.parameter.featured.toString().toLowerCase() === 'true') {
    products = products.filter(p => p.is_featured);
  }
  if (e.parameter.new && e.parameter.new.toString().toLowerCase() === 'true') {
    products = products.filter(p => p.is_new);
  }

  if (e.parameter.query) {
    const kw = removeVietnameseDiacritics(e.parameter.query.trim());
    products = products.filter(p => removeVietnameseDiacritics(p.product_name).includes(kw));
  }
  
  const sort = e.parameter.sort;
  if (sort === 'price_asc') {
    products.sort((a, b) => a.selling_price - b.selling_price);
  } else if (sort === 'price_desc') {
    products.sort((a, b) => b.selling_price - a.selling_price);
  }

  const page = Math.max(1, parseInt(e.parameter.page) || 1);
  const pageSize = Math.max(1, parseInt(e.parameter.pageSize) || 20);
  const total = products.length;
  const totalPages = Math.ceil(total / pageSize);
  const from = (page - 1) * pageSize;
  const items = products.slice(from, from + pageSize);

  return apiResponse(items, { page, pageSize, total, totalPages });
}

// ... (Các hàm còn lại từ getProductDetail đến cuối giữ nguyên như phiên bản trước)

function getProductDetail(product_name, data) {
  const productVariants = data.productsData.filter(p => p.product_name === product_name && parseBoolean(p.is_active));
  if (productVariants.length === 0) {
    throw { code: 404, message: `Không tìm thấy sản phẩm với tên: ${product_name}` };
  }
  
  const firstVariant = productVariants[0];
  const category = data.categoriesData.find(c => c.CategoryID === firstVariant.category_id);
  const parentCategory = category ? data.categoriesData.find(c => c.CategoryID === category.DanhMucChaID) : null;

  // Tính giá tối thiểu trong các biến thể để hiển thị trong modal
  let minList = Infinity;
  let minSell = Infinity;
  productVariants.forEach(p => {
    const listVal = parseFloat(p.price_case) || Infinity;
    if (listVal < minList) minList = listVal;
    const sellVal = parseFloat(p.price_case_TSG) || Infinity;
    if (sellVal < minSell) minSell = sellVal;
  });
  return {
    product_name: firstVariant.product_name,
    description: firstVariant.description,
    list_price_min: (minList !== Infinity ? minList : null),
    selling_price_min: (minSell !== Infinity ? minSell : null),
    category: {
      id: category ? category.CategoryID : null,
      name: category ? category.TenDanhMuc : null,
      parent: parentCategory ? { id: parentCategory.CategoryID, name: parentCategory.TenDanhMuc } : null
    },
    variants: productVariants.map(p => ({
      sku: p.sku,
      name_product: p.name_product,
      images: (p.image_url || '').split(',').map(url => url.trim()).filter(url => url),
      weight: p.Weight,
      volume: p.Volume,
      height: p.Height_mm,
      diameter: p.Diameter_mm,
      price_piece_TSG: parseFloat(p.price_piece_TSG) || null,
      price_box_TSG: parseFloat(p.price_box_TSG) || null,
      price_pack_TSG: parseFloat(p.price_pack_TSG) || null,
      price_case_TSG: parseFloat(p.price_case_TSG) || null,
      promotions: getPromotionsForSku(p.sku, data)
    }))
  };
}

function getCachedSheetData(sheetName) {
  const cacheKey = CACHE_KEY_PREFIX + sheetName;
  let cached = CACHE.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }

  const data = sheetAPIToObjects(sheetName);
  if (data.length > 0) {
    try {
      CACHE.put(cacheKey, JSON.stringify(data), CACHE_EXPIRATION_SECONDS);
    } catch (e) {
      Logger.log(`Lỗi khi lưu cache cho sheet '${sheetName}': ${e.toString()}`);
    }
  }
  return data;
}

function loadAllDataFromCacheOrSheets() {
  const data = {
    categoriesData: getCachedSheetData(SHEETS.CATEGORIES),
    productsData: getCachedSheetData(SHEETS.PRODUCTS),
    promotionsData: getCachedSheetData(SHEETS.PROMOTIONS),
    appliedProductsData: getCachedSheetData(SHEETS.APPLIED_PRODUCTS),
    promotionRulesData: getCachedSheetData(SHEETS.PROMOTION_RULES)
  };
  return data;
}

function sheetAPIToObjects(sheetName) {
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(sheetName);
    if (!sheet) throw new Error(`Sheet "${sheetName}" không tồn tại.`);

    const data = sheet.getDataRange().getValues();
    if (!data || data.length < 2) return [];
    
    const headers = data[0].map(h => h ? h.toString().trim() : '');
    return data.slice(1).map(row => {
      const obj = {};
      headers.forEach((header, i) => { 
          if(header) {
              obj[header] = row[i] !== undefined ? row[i] : ""; 
          }
      });
      return obj;
    });
  } catch (e) {
    Logger.log(`Lỗi khi đọc sheet: ${sheetName}. Chi tiết: ${e.toString()}`);
    throw new Error(`Không thể tải dữ liệu từ sheet '${sheetName}'.`);
  }
}

function createJsonResponse(data) {
  return ContentService.createTextOutput(JSON.stringify(data)).setMimeType(ContentService.MimeType.JSON);
}
function apiResponse(data, meta = {}) { 
  return { success: true, data, meta }; 
}
function parseBoolean(val) { 
  if (typeof val === 'boolean') return val; 
  if (typeof val === 'string') return val.trim().toLowerCase() === 'true'; 
  return false; 
}
function getActiveCategories(categoriesData) { 
  return categoriesData.filter(cat => parseBoolean(cat.TrangThai)); 
}
function removeVietnameseDiacritics(str) { 
  if (!str) return ''; 
  return str.toString().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/đ/g, 'd').replace(/Đ/g, 'D').toLowerCase(); 
}
function parseDate(dateString) { 
  if (!dateString || typeof dateString !== 'string') return null; 
  const parts = dateString.split('/'); 
  if (parts.length !== 3) return dateString;
  return `${parts[1]}/${parts[0]}/${parts[2]}`;
}

function getPromotionsForSku(sku, data) { 
  const appliedPromos = data.appliedProductsData.filter(ap => ap.sku === sku); 
  const today = new Date(); 
  return appliedPromos.map(ap => { 
    const promotion = data.promotionsData.find(p => p.promo_id === ap.promo_id); 
    if (!promotion || !parseBoolean(promotion.is_active)) return null; 
    const startDate = new Date(parseDate(promotion.start_date)); 
    const endDate = new Date(parseDate(promotion.end_date)); 
    if (today < startDate || today > endDate) return null; 
    const rules = data.promotionRulesData.filter(r => r.promo_id === promotion.promo_id); 
    return { id: promotion.promo_id, name: promotion.promo_name, description: promotion.description, rules: rules.map(rule => formatPromotionRule(rule, data.productsData)) }; 
  }).filter(p => p !== null); 
}

function formatPromotionRule(rule, productsData) { 
  switch (rule.rule_type) { 
    case 'BUY_X_GET_Y': 
      if (rule.discount_amount > 0) return `Mua ${rule.required_qty} ${rule.unit}: Giảm trực tiếp ${Number(rule.discount_amount).toLocaleString('vi-VN')}đ.`; 
      if (rule.discount_percent > 0) return `Mua ${rule.required_qty} ${rule.unit}: Giảm ${rule.discount_percent}%.`; 
      if (rule.free_sku && rule.free_qty > 0) { 
        const freeProduct = productsData.find(p => p.sku === rule.free_sku); 
        const freeProductName = freeProduct ? (freeProduct.name_product || freeProduct.product_name) : 'sản phẩm tặng'; 
        return `Mua ${rule.required_qty} ${rule.unit}: Tặng ${rule.free_qty} ${rule.free_unit} ${freeProductName}.`; 
      } 
      return 'Khuyến mãi mua X tặng Y.'; 
    case 'SPECIAL_PRICE_ON_QUANTITY': 
      if (rule.special_price > 0) return `Mua từ ${rule.required_qty} ${rule.unit}: Giá đặc biệt chỉ ${Number(rule.special_price).toLocaleString('vi-VN')}đ/${rule.unit}.`; 
      return 'Khuyến mãi giá đặc biệt.'; 
    default: return 'Chương trình khuyến mãi khác.';
  }
}

/**
 * ============================================================================
 * PHASE 2 - ORDER MANAGEMENT SYSTEM
 * ============================================================================
 */

// ============================================================================
// ORDER MANAGEMENT CONFIGURATION
// ============================================================================

const ORDER_CONFIG = {
  // Order ID generation
  ORDER_ID_PREFIX: 'LG',
  ORDER_ID_LENGTH: 8,

  // Order statuses
  STATUS: {
    PENDING_PAYMENT: 'pending_payment',
    PAID: 'paid',
    CONFIRMED: 'confirmed',
    PROCESSING: 'processing',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded'
  },

  // Payment methods
  PAYMENT_METHODS: {
    VNPAY: 'vnpay',
    BANK_TRANSFER: 'bank_transfer',
    COD: 'cod'
  },

  // Email settings
  EMAIL: {
    FROM_NAME: 'Lotus Glass Vietnam',
    FROM_EMAIL: '<EMAIL>',
    REPLY_TO: '<EMAIL>',
    BCC: '<EMAIL>'
  },

  // VNPay settings (sandbox - replace với production)
  VNPAY: {
    TMN_CODE: 'YOUR_TMN_CODE',
    HASH_SECRET: 'YOUR_HASH_SECRET',
    URL: 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
    RETURN_URL: 'https://lotusglassvietnam.blogspot.com/payment-return'
  },

  // Database sheets
  SHEETS: {
    ORDERS: 'Orders',
    ORDER_DETAILS: 'OrderDetails',
    CUSTOMERS: 'Customers'
  }
};

// ============================================================================
// ORDER API HANDLER
// ============================================================================

/**
 * Handle order-related API requests
 */
function handleOrderAPI(e) {
  try {
    const action = e.parameter.action;

    switch (action) {
      case 'create_order':
        return createOrder(e);
      case 'get_order_status':
        return getOrderStatus(e);
      case 'vnpay_create_payment':
        return createVNPayPayment(e);
      case 'vnpay_callback':
        return handleVNPayCallback(e);
      default:
        return createErrorResponse('Invalid order action: ' + action);
    }

  } catch (error) {
    Logger.log('Order API error:', error);
    return createErrorResponse('Order processing error: ' + error.message);
  }
}

/**
 * Create new order
 */
function createOrder(e) {
  try {
    const orderDataParam = e.parameter.orderData;
    if (!orderDataParam) {
      throw new Error('Order data is required');
    }

    const orderData = JSON.parse(orderDataParam);

    // Validate order data
    validateOrderData(orderData);

    // Generate order ID
    const orderId = generateOrderId();

    // Create order record
    const order = {
      orderId: orderId,
      customer: orderData.customer,
      shipping: orderData.shipping,
      payment: orderData.payment,
      items: orderData.items,
      notes: orderData.notes || '',
      status: ORDER_CONFIG.STATUS.PENDING_PAYMENT,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to database
    const savedOrder = saveOrderToDatabase(order);

    // Handle payment method specific logic
    let response = {
      success: true,
      order: savedOrder,
      message: 'Order created successfully'
    };

    if (orderData.payment.method === ORDER_CONFIG.PAYMENT_METHODS.VNPAY) {
      // Create VNPay payment URL
      const paymentUrl = createVNPayPaymentUrl(savedOrder);
      response.paymentUrl = paymentUrl;
    }

    // Send confirmation email
    sendOrderConfirmationEmail(savedOrder);

    return createSuccessResponse(response);

  } catch (error) {
    Logger.log('Create order error:', error);
    return createErrorResponse('Failed to create order: ' + error.message);
  }
}

/**
 * Validate order data
 */
function validateOrderData(orderData) {
  if (!orderData.customer || !orderData.customer.name || !orderData.customer.phone) {
    throw new Error('Customer information is required');
  }

  if (!orderData.shipping || !orderData.shipping.address) {
    throw new Error('Shipping address is required');
  }

  if (!orderData.payment || !orderData.payment.method) {
    throw new Error('Payment method is required');
  }

  if (!orderData.items || orderData.items.length === 0) {
    throw new Error('Order must contain at least one item');
  }

  // Validate phone number format
  const phoneRegex = /^(\+84|0)[1-9]\d{8}$/;
  if (!phoneRegex.test(orderData.customer.phone)) {
    throw new Error('Invalid phone number format');
  }

  // Validate email if provided
  if (orderData.customer.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(orderData.customer.email)) {
      throw new Error('Invalid email format');
    }
  }
}

/**
 * Generate unique order ID
 */
function generateOrderId() {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return ORDER_CONFIG.ORDER_ID_PREFIX + timestamp.slice(-6) + random;
}

/**
 * Save order to database
 */
function saveOrderToDatabase(order) {
  try {
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);

    // Save main order
    const ordersSheet = getOrCreateSheet(spreadsheet, ORDER_CONFIG.SHEETS.ORDERS);
    const orderRow = [
      order.orderId,
      order.customer.name,
      order.customer.phone,
      order.customer.email || '',
      order.shipping.address,
      order.shipping.ward || '',
      order.shipping.district || '',
      order.shipping.city || '',
      order.shipping.postal || '',
      order.payment.method,
      order.payment.subtotal,
      order.payment.shipping,
      order.payment.codFee || 0,
      order.payment.total,
      order.status,
      order.notes,
      order.createdAt,
      order.updatedAt
    ];

    ordersSheet.appendRow(orderRow);

    // Save order details
    const orderDetailsSheet = getOrCreateSheet(spreadsheet, ORDER_CONFIG.SHEETS.ORDER_DETAILS);
    order.items.forEach(item => {
      const detailRow = [
        order.orderId,
        item.sku,
        item.name,
        item.variant || '',
        item.price,
        item.quantity,
        item.price * item.quantity
      ];
      orderDetailsSheet.appendRow(detailRow);
    });

    // Save/update customer
    saveCustomerToDatabase(order.customer, spreadsheet);

    return order;

  } catch (error) {
    Logger.log('Database save error:', error);
    throw new Error('Failed to save order to database: ' + error.message);
  }
}

/**
 * Save customer to database
 */
function saveCustomerToDatabase(customer, spreadsheet) {
  try {
    const customersSheet = getOrCreateSheet(spreadsheet, ORDER_CONFIG.SHEETS.CUSTOMERS);

    // Check if customer exists
    const data = customersSheet.getDataRange().getValues();
    const existingRowIndex = data.findIndex(row => row[1] === customer.phone); // Phone is unique identifier

    const customerRow = [
      customer.phone, // Use phone as primary key
      customer.name,
      customer.email || '',
      new Date(), // Last order date
      1 // Order count - will be updated if existing
    ];

    if (existingRowIndex > 0) {
      // Update existing customer
      customerRow[4] = (data[existingRowIndex][4] || 0) + 1; // Increment order count
      customersSheet.getRange(existingRowIndex + 1, 1, 1, customerRow.length).setValues([customerRow]);
    } else {
      // Add new customer
      customersSheet.appendRow(customerRow);
    }

  } catch (error) {
    Logger.log('Customer save error:', error);
    // Don't throw error here as it's not critical for order processing
  }
}

/**
 * Get or create sheet
 */
function getOrCreateSheet(spreadsheet, sheetName) {
  let sheet = spreadsheet.getSheetByName(sheetName);

  if (!sheet) {
    sheet = spreadsheet.insertSheet(sheetName);

    // Add headers based on sheet type
    if (sheetName === ORDER_CONFIG.SHEETS.ORDERS) {
      sheet.getRange(1, 1, 1, 18).setValues([[
        'Order ID', 'Customer Name', 'Phone', 'Email', 'Address', 'Ward', 'District',
        'City', 'Postal', 'Payment Method', 'Subtotal', 'Shipping', 'COD Fee',
        'Total', 'Status', 'Notes', 'Created At', 'Updated At'
      ]]);
    } else if (sheetName === ORDER_CONFIG.SHEETS.ORDER_DETAILS) {
      sheet.getRange(1, 1, 1, 7).setValues([[
        'Order ID', 'SKU', 'Product Name', 'Variant', 'Price', 'Quantity', 'Total'
      ]]);
    } else if (sheetName === ORDER_CONFIG.SHEETS.CUSTOMERS) {
      sheet.getRange(1, 1, 1, 5).setValues([[
        'Phone', 'Name', 'Email', 'Last Order Date', 'Order Count'
      ]]);
    }
  }

  return sheet;
}

/**
 * Create VNPay payment URL
 */
function createVNPayPaymentUrl(order) {
  try {
    const vnpParams = {
      vnp_Version: '2.1.0',
      vnp_Command: 'pay',
      vnp_TmnCode: ORDER_CONFIG.VNPAY.TMN_CODE,
      vnp_Amount: order.payment.total * 100, // VNPay expects amount in VND cents
      vnp_CurrCode: 'VND',
      vnp_TxnRef: order.orderId,
      vnp_OrderInfo: `Thanh toan don hang ${order.orderId}`,
      vnp_OrderType: 'other',
      vnp_Locale: 'vn',
      vnp_ReturnUrl: ORDER_CONFIG.VNPAY.RETURN_URL,
      vnp_IpAddr: '127.0.0.1',
      vnp_CreateDate: Utilities.formatDate(new Date(), 'GMT+7', 'yyyyMMddHHmmss')
    };

    // Sort parameters
    const sortedParams = Object.keys(vnpParams).sort().reduce((result, key) => {
      result[key] = vnpParams[key];
      return result;
    }, {});

    // Create query string
    const queryString = Object.keys(sortedParams)
      .map(key => `${key}=${encodeURIComponent(sortedParams[key])}`)
      .join('&');

    // Create secure hash
    const secureHash = Utilities.computeHmacSha512Signature(queryString, ORDER_CONFIG.VNPAY.HASH_SECRET);
    const hashHex = secureHash.map(byte => {
      const hex = (byte & 0xFF).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('');

    return `${ORDER_CONFIG.VNPAY.URL}?${queryString}&vnp_SecureHash=${hashHex}`;

  } catch (error) {
    Logger.log('VNPay URL creation error:', error);
    throw new Error('Failed to create VNPay payment URL');
  }
}

/**
 * Handle VNPay callback
 */
function handleVNPayCallback(e) {
  try {
    const responseCode = e.parameter.vnp_ResponseCode;
    const orderId = e.parameter.vnp_TxnRef;

    if (responseCode === '00') {
      // Payment successful
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.PAID);
      return createSuccessResponse({ message: 'Payment successful' });
    } else {
      // Payment failed
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.CANCELLED);
      return createErrorResponse('Payment failed');
    }

  } catch (error) {
    Logger.log('VNPay callback error:', error);
    return createErrorResponse('VNPay callback processing error');
  }
}

/**
 * Update order status
 */
function updateOrderStatus(orderId, newStatus) {
  try {
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    const ordersSheet = spreadsheet.getSheetByName(ORDER_CONFIG.SHEETS.ORDERS);

    const data = ordersSheet.getDataRange().getValues();
    const orderRowIndex = data.findIndex(row => row[0] === orderId);

    if (orderRowIndex > 0) {
      ordersSheet.getRange(orderRowIndex + 1, 15).setValue(newStatus); // Status column
      ordersSheet.getRange(orderRowIndex + 1, 18).setValue(new Date()); // Updated At column
    }

  } catch (error) {
    Logger.log('Update order status error:', error);
    throw new Error('Failed to update order status');
  }
}

/**
 * Get order status
 */
function getOrderStatus(e) {
  try {
    const orderId = e.parameter.orderId;
    if (!orderId) {
      throw new Error('Order ID is required');
    }

    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    const ordersSheet = spreadsheet.getSheetByName(ORDER_CONFIG.SHEETS.ORDERS);

    const data = ordersSheet.getDataRange().getValues();
    const orderRow = data.find(row => row[0] === orderId);

    if (!orderRow) {
      throw new Error('Order not found');
    }

    return createSuccessResponse({
      orderId: orderRow[0],
      status: orderRow[14],
      createdAt: orderRow[16],
      updatedAt: orderRow[17]
    });

  } catch (error) {
    Logger.log('Get order status error:', error);
    return createErrorResponse('Failed to get order status: ' + error.message);
  }
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail(order) {
  try {
    const subject = `Xác nhận đơn hàng #${order.orderId} - Lotus Glass Vietnam`;

    const htmlBody = createOrderEmailTemplate(order);

    const emailOptions = {
      htmlBody: htmlBody,
      name: ORDER_CONFIG.EMAIL.FROM_NAME,
      replyTo: ORDER_CONFIG.EMAIL.REPLY_TO,
      bcc: ORDER_CONFIG.EMAIL.BCC
    };

    // Send to customer
    if (order.customer.email) {
      GmailApp.sendEmail(order.customer.email, subject, '', emailOptions);
    }

    // Send to admin
    GmailApp.sendEmail(ORDER_CONFIG.EMAIL.BCC, subject, '', emailOptions);

  } catch (error) {
    Logger.log('Email sending error:', error);
    // Don't throw error as email failure shouldn't break order processing
  }
}

/**
 * Create order email template
 */
function createOrderEmailTemplate(order) {
  const itemsHtml = order.items.map(item => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">
        ${item.name}${item.variant ? ` (${item.variant})` : ''}
      </td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">
        ${item.quantity}
      </td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">
        ${formatCurrency(item.price)}
      </td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">
        ${formatCurrency(item.price * item.quantity)}
      </td>
    </tr>
  `).join('');

  const paymentMethodName = {
    'vnpay': 'VNPay - Thanh toán online',
    'bank_transfer': 'Chuyển khoản ngân hàng',
    'cod': 'Thanh toán khi nhận hàng (COD)'
  }[order.payment.method] || order.payment.method;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Xác nhận đơn hàng</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: #f37021; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">Lotus Glass Vietnam</h1>
        <p style="margin: 5px 0 0 0;">Xác nhận đơn hàng #${order.orderId}</p>
      </div>

      <div style="background: white; padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px;">
        <h2 style="color: #f37021; margin-top: 0;">Cảm ơn bạn đã đặt hàng!</h2>
        <p>Chúng tôi đã nhận được đơn hàng của bạn và sẽ xử lý trong thời gian sớm nhất.</p>

        <h3 style="color: #333; border-bottom: 2px solid #f37021; padding-bottom: 5px;">Thông tin đơn hàng</h3>
        <table style="width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="padding: 5px 0;"><strong>Mã đơn hàng:</strong></td>
            <td style="padding: 5px 0;">${order.orderId}</td>
          </tr>
          <tr>
            <td style="padding: 5px 0;"><strong>Ngày đặt:</strong></td>
            <td style="padding: 5px 0;">${Utilities.formatDate(order.createdAt, 'GMT+7', 'dd/MM/yyyy HH:mm')}</td>
          </tr>
          <tr>
            <td style="padding: 5px 0;"><strong>Phương thức thanh toán:</strong></td>
            <td style="padding: 5px 0;">${paymentMethodName}</td>
          </tr>
        </table>

        <h3 style="color: #333; border-bottom: 2px solid #f37021; padding-bottom: 5px;">Thông tin khách hàng</h3>
        <table style="width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="padding: 5px 0;"><strong>Họ tên:</strong></td>
            <td style="padding: 5px 0;">${order.customer.name}</td>
          </tr>
          <tr>
            <td style="padding: 5px 0;"><strong>Điện thoại:</strong></td>
            <td style="padding: 5px 0;">${order.customer.phone}</td>
          </tr>
          ${order.customer.email ? `
          <tr>
            <td style="padding: 5px 0;"><strong>Email:</strong></td>
            <td style="padding: 5px 0;">${order.customer.email}</td>
          </tr>
          ` : ''}
        </table>

        <h3 style="color: #333; border-bottom: 2px solid #f37021; padding-bottom: 5px;">Địa chỉ giao hàng</h3>
        <p style="margin: 10px 0;">
          ${order.shipping.address}<br>
          ${order.shipping.ward}, ${order.shipping.district}<br>
          ${order.shipping.city}
        </p>

        <h3 style="color: #333; border-bottom: 2px solid #f37021; padding-bottom: 5px;">Chi tiết đơn hàng</h3>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Sản phẩm</th>
              <th style="padding: 10px; text-align: center; border-bottom: 2px solid #ddd;">SL</th>
              <th style="padding: 10px; text-align: right; border-bottom: 2px solid #ddd;">Đơn giá</th>
              <th style="padding: 10px; text-align: right; border-bottom: 2px solid #ddd;">Thành tiền</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
        </table>

        <table style="width: 100%; margin-bottom: 20px;">
          <tr>
            <td style="padding: 5px 0; text-align: right;"><strong>Tạm tính:</strong></td>
            <td style="padding: 5px 0; text-align: right; width: 120px;">${formatCurrency(order.payment.subtotal)}</td>
          </tr>
          <tr>
            <td style="padding: 5px 0; text-align: right;"><strong>Phí vận chuyển:</strong></td>
            <td style="padding: 5px 0; text-align: right;">${formatCurrency(order.payment.shipping)}</td>
          </tr>
          ${order.payment.codFee > 0 ? `
          <tr>
            <td style="padding: 5px 0; text-align: right;"><strong>Phí COD:</strong></td>
            <td style="padding: 5px 0; text-align: right;">${formatCurrency(order.payment.codFee)}</td>
          </tr>
          ` : ''}
          <tr style="border-top: 2px solid #f37021; font-size: 18px; font-weight: bold;">
            <td style="padding: 10px 0; text-align: right; color: #f37021;">Tổng cộng:</td>
            <td style="padding: 10px 0; text-align: right; color: #f37021;">${formatCurrency(order.payment.total)}</td>
          </tr>
        </table>

        ${order.notes ? `
        <h3 style="color: #333; border-bottom: 2px solid #f37021; padding-bottom: 5px;">Ghi chú</h3>
        <p style="background: #f8f9fa; padding: 10px; border-radius: 4px;">${order.notes}</p>
        ` : ''}

        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px;">
          <h4 style="margin-top: 0; color: #f37021;">Thông tin liên hệ</h4>
          <p style="margin: 5px 0;">📞 Hotline: 0123 456 789</p>
          <p style="margin: 5px 0;">📧 Email: ${ORDER_CONFIG.EMAIL.REPLY_TO}</p>
          <p style="margin: 5px 0;">🌐 Website: lotusglassvietnam.blogspot.com</p>
        </div>

        <p style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
          Cảm ơn bạn đã tin tưởng và lựa chọn Lotus Glass Vietnam!
        </p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Format currency
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN').format(amount) + '₫';
}

/**
 * Create success response
 */
function createSuccessResponse(data) {
  return ContentService
    .createTextOutput(JSON.stringify({ success: true, data: data }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Create error response
 */
function createErrorResponse(message) {
  return ContentService
    .createTextOutput(JSON.stringify({ success: false, error: message }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * ============================================================================
 * POST REQUEST HANDLER
 * ============================================================================
 */

/**
 * Handle POST requests (for order processing)
 */
function doPost(e) {
  try {
    const action = e.parameter.action;

    // Log request
    Logger.log('API Request:', action, e.parameter);

    // Phase 1 - Cart actions (if needed)
    if (['validate_cart', 'apply_promotion', 'calculate_shipping'].includes(action)) {
      return handleCartAPI(e);
    }

    // Phase 2 - Order actions
    if (['create_order', 'get_order_status', 'vnpay_callback', 'vnpay_create_payment'].includes(action)) {
      return handleOrderAPI(e);
    }

    return createErrorResponse('Invalid action: ' + action);

  } catch (error) {
    Logger.log('doPost error:', error);
    return createErrorResponse('Server error: ' + error.message);
  }
}

/**
 * Handle cart-related API requests (placeholder for Phase 1 compatibility)
 */
function handleCartAPI(e) {
  const action = e.parameter.action;

  switch (action) {
    case 'validate_cart':
      return createSuccessResponse({ valid: true, message: 'Cart is valid' });
    case 'apply_promotion':
      return createSuccessResponse({ discount: 0, message: 'No promotions applied' });
    case 'calculate_shipping':
      return createSuccessResponse({ shipping: 30000, message: 'Standard shipping' });
    default:
      return createErrorResponse('Invalid cart action: ' + action);
  }
}

/**
 * ============================================================================
 * TESTING FUNCTIONS
 * ============================================================================
 */

/**
 * Test order creation
 */
function testOrderCreation() {
  const testOrder = {
    customer: {
      name: 'Nguyễn Văn A',
      phone: '0123456789',
      email: '<EMAIL>'
    },
    shipping: {
      address: '123 Đường ABC',
      ward: 'Phường XYZ',
      district: 'Quận 1',
      city: 'TP. Hồ Chí Minh'
    },
    payment: {
      method: 'cod',
      subtotal: 500000,
      shipping: 30000,
      codFee: 15000,
      total: 545000
    },
    items: [
      {
        sku: 'TEST001',
        name: 'Bình thủy tinh test',
        variant: '1L',
        price: 250000,
        quantity: 2
      }
    ],
    notes: 'Đơn hàng test'
  };

  try {
    validateOrderData(testOrder);
    const orderId = generateOrderId();
    const order = {
      orderId: orderId,
      ...testOrder,
      status: ORDER_CONFIG.STATUS.PENDING_PAYMENT,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const savedOrder = saveOrderToDatabase(order);
    Logger.log('Test order created successfully:', savedOrder.orderId);

    return savedOrder;

  } catch (error) {
    Logger.log('Test order creation failed:', error);
    throw error;
  }
}
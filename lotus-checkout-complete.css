/* ============================================================================
   LOTUS GLASS - PHASE 2 CHECKOUT SYSTEM STYLES
   File: lotus-checkout-complete.css
   
   Thêm vào cuối lotus-cart-complete.css
   ============================================================================ */

/* === CHECKOUT MODAL STYLES === */
.checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.8);
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.checkout-modal.active {
  opacity: 1;
  visibility: visible;
}

.checkout-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  transition: transform 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.checkout-modal.active .checkout-container {
  transform: translate(-50%, -50%) scale(1);
}

.checkout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--cart-border);
  background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
  color: white;
}

.checkout-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.checkout-title::before {
  content: '🛒';
  font-size: 22px;
}

.checkout-close {
  background: rgba(255,255,255,0.2);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.checkout-close:hover {
  background: rgba(255,255,255,0.3);
  transform: rotate(90deg);
}

.checkout-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.checkout-form-section {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.checkout-summary-section {
  width: 380px;
  background: var(--cart-light);
  border-left: 1px solid var(--cart-border);
  padding: 32px 24px;
  overflow-y: auto;
}

/* === CHECKOUT FORM STYLES === */
.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background: white;
  border-radius: var(--cart-radius);
  padding: 24px;
  border: 1px solid var(--cart-border);
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-dark);
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--cart-light);
}

.section-icon {
  width: 20px;
  height: 20px;
  color: var(--cart-primary);
}

.form-grid {
  display: grid;
  gap: 16px;
}

.form-grid.two-column {
  grid-template-columns: 1fr 1fr;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-dark);
  display: flex;
  align-items: center;
  gap: 4px;
}

.required {
  color: var(--cart-danger);
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 2px solid var(--cart-border);
  border-radius: var(--cart-radius);
  font-size: 15px;
  transition: all 0.2s ease;
  background: white;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--cart-primary);
  box-shadow: 0 0 0 3px rgba(243, 112, 33, 0.1);
}

.form-input.error,
.form-select.error {
  border-color: var(--cart-danger);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-input.success,
.form-select.success {
  border-color: var(--cart-success);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.form-error {
  color: var(--cart-danger);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.form-success {
  color: var(--cart-success);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.form-help {
  color: var(--cart-secondary);
  font-size: 13px;
  font-style: italic;
  margin-top: 4px;
}

/* === PAYMENT METHOD STYLES === */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid var(--cart-border);
  border-radius: var(--cart-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.payment-option:hover {
  border-color: var(--cart-primary);
  background: rgba(243, 112, 33, 0.02);
}

.payment-option.selected {
  border-color: var(--cart-primary);
  background: rgba(243, 112, 33, 0.05);
  box-shadow: 0 0 0 1px var(--cart-primary);
}

.payment-radio {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  accent-color: var(--cart-primary);
}

.payment-icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  border-radius: var(--cart-radius);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--cart-light);
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--cart-dark);
  margin: 0 0 4px 0;
}

.payment-description {
  font-size: 13px;
  color: var(--cart-secondary);
  margin: 0;
  line-height: 1.4;
}

.payment-fee {
  font-size: 14px;
  color: var(--cart-primary);
  font-weight: 600;
}

/* === ORDER SUMMARY STYLES === */
.order-summary {
  background: white;
  border-radius: var(--cart-radius);
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid var(--cart-border);
}

.summary-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-dark);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-count {
  background: var(--cart-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.summary-item {
  display: flex;
  gap: 12px;
  align-items: center;
}

.summary-item-image {
  width: 50px;
  height: 50px;
  border-radius: var(--cart-radius);
  overflow: hidden;
  flex-shrink: 0;
  background: var(--cart-light);
}

.summary-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.summary-item-details {
  flex: 1;
  min-width: 0;
}

.summary-item-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-dark);
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.summary-item-variant {
  font-size: 12px;
  color: var(--cart-secondary);
  margin: 0 0 4px 0;
}

.summary-item-quantity {
  font-size: 13px;
  color: var(--cart-secondary);
  margin: 0;
}

.summary-item-price {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-primary);
  text-align: right;
}

.summary-divider {
  height: 1px;
  background: var(--cart-border);
  margin: 16px 0;
}

.summary-totals {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
}

.total-row.subtotal {
  color: var(--cart-dark);
}

.total-row.shipping {
  color: var(--cart-secondary);
}

.total-row.discount {
  color: var(--cart-success);
}

.total-row.final {
  font-size: 18px;
  font-weight: 700;
  color: var(--cart-dark);
  padding-top: 12px;
  border-top: 2px solid var(--cart-border);
}

.total-value {
  font-weight: 600;
}

.total-value.final {
  color: var(--cart-primary);
  font-size: 20px;
}

/* === CHECKOUT ACTIONS === */
.checkout-actions {
  display: flex;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid var(--cart-border);
  background: var(--cart-light);
}

.checkout-btn {
  flex: 1;
  padding: 16px 24px;
  border: none;
  border-radius: var(--cart-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.checkout-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  transition: all 0.6s ease;
  transform: translate(-50%, -50%);
}

.checkout-btn:hover::before {
  width: 300px;
  height: 300px;
}

.checkout-btn-secondary {
  background: white;
  color: var(--cart-primary);
  border: 2px solid var(--cart-primary);
}

.checkout-btn-secondary:hover {
  background: var(--cart-primary);
  color: white;
  transform: translateY(-1px);
}

.checkout-btn-primary {
  background: linear-gradient(135deg, var(--cart-primary) 0%, var(--cart-primary-dark) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 112, 33, 0.3);
}

.checkout-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 112, 33, 0.4);
}

.checkout-btn-primary:disabled {
  background: var(--cart-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.checkout-btn.loading {
  pointer-events: none;
}

.checkout-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: cartSpin 1s linear infinite;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .checkout-container {
    width: 95%;
    height: 95vh;
    max-height: none;
    transform: translate(-50%, -50%) scale(0.95);
  }
  
  .checkout-modal.active .checkout-container {
    transform: translate(-50%, -50%) scale(1);
  }
  
  .checkout-body {
    flex-direction: column;
  }
  
  .checkout-form-section {
    padding: 20px;
  }
  
  .checkout-summary-section {
    width: auto;
    border-left: none;
    border-top: 1px solid var(--cart-border);
    padding: 20px;
    max-height: 300px;
  }
  
  .form-section {
    padding: 20px;
  }
  
  .form-grid.two-column {
    grid-template-columns: 1fr;
  }
  
  .checkout-header {
    padding: 16px 20px;
  }
  
  .checkout-title {
    font-size: 20px;
  }
  
  .checkout-actions {
    padding: 16px 20px;
    flex-direction: column;
  }
  
  .checkout-btn {
    padding: 14px 20px;
  }
}

@media (max-width: 480px) {
  .checkout-container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }
  
  .form-section {
    padding: 16px;
  }
  
  .checkout-form-section,
  .checkout-summary-section {
    padding: 16px;
  }
  
  .order-summary {
    padding: 16px;
  }
  
  .checkout-actions {
    padding: 12px 16px;
  }
}

/* === FORM VALIDATION STATES === */
.form-group.loading .form-input,
.form-group.loading .form-select {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f37021"><circle cx="12" cy="12" r="3"><animateTransform attributeName="transform" type="rotate" dur="1s" repeatCount="indefinite" values="0 12 12;360 12 12"/></circle></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

.form-group.success .form-input,
.form-group.success .form-select {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2328a745"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

.form-group.error .form-input,
.form-group.error .form-select {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23dc3545"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

/* === STEPPER PROGRESS === */
.checkout-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 0 32px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--cart-secondary);
  font-size: 14px;
  font-weight: 500;
}

.progress-step.active {
  color: var(--cart-primary);
}

.progress-step.completed {
  color: var(--cart-success);
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: var(--cart-light);
  border: 2px solid var(--cart-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  transition: all 0.2s ease;
}

.progress-step.active .step-number {
  background: var(--cart-primary);
  border-color: var(--cart-primary);
  color: white;
}

.progress-step.completed .step-number {
  background: var(--cart-success);
  border-color: var(--cart-success);
  color: white;
}

.progress-line {
  width: 40px;
  height: 2px;
  background: var(--cart-border);
  transition: all 0.2s ease;
}

.progress-line.completed {
  background: var(--cart-success);
}

/* === ANIMATIONS === */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: slideInUp 0.3s ease;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .checkout-container,
  .checkout-btn,
  .form-input,
  .payment-option {
    transition: none;
  }
  
  .form-section {
    animation: none;
  }
  
  .checkout-spinner {
    animation: none;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .checkout-container {
    border: 3px solid var(--cart-dark);
  }
  
  .form-input:focus,
  .form-select:focus {
    outline: 3px solid var(--cart-primary);
  }
  
  .payment-option.selected {
    outline: 3px solid var(--cart-primary);
  }
}

/* === PRINT STYLES === */
@media print {
  .checkout-modal {
    position: relative;
    background: white;
    z-index: auto;
    opacity: 1;
    visibility: visible;
  }
  
  .checkout-container {
    position: relative;
    transform: none;
    width: 100%;
    max-width: none;
    box-shadow: none;
    border: 1px solid var(--cart-dark);
  }
  
  .checkout-close,
  .checkout-actions {
    display: none;
  }
}
/**
 * LOTUS GLASS - Order Management System
 * Google Apps Script Backend Extension
 * 
 * File: order-management-gas.js
 * Mở rộng từ code.gs hiện tại để thêm chức năng đặt hàng
 */

// ============================================================================
// CONFIGURATION - Thêm vào phần config hiện tại
// ============================================================================

const ORDER_CONFIG = {
  // Email settings
  ADMIN_EMAIL: '<EMAIL>',
  FROM_NAME: 'Lotus Glass',
  
  // Order settings
  ORDER_PREFIX: 'LG',
  COD_FEE_RATE: 0.02, // 2% COD fee
  FREE_SHIPPING_THRESHOLD: 500000, // Free shipping over 500k VND
  SHIPPING_FEE: 30000, // Default shipping fee
  
  // Payment methods
  PAYMENT_METHODS: {
    'vnpay': 'VNPay',
    'bank': 'Chuyển khoản ngân hàng', 
    'cod': 'Thanh toán khi nhận hàng'
  },
  
  // Order status
  ORDER_STATUS: {
    'pending_payment': 'Chờ thanh toán',
    'paid': 'Đã thanh toán',
    'confirmed': 'Đã xác nhận',
    'processing': 'Đang xử lý',
    'shipped': 'Đã giao vận chuyển',
    'delivered': 'Đã giao thành công',
    'cancelled': 'Đã hủy',
    'refunded': 'Đã hoàn tiền'
  }
};

// ============================================================================
// ORDER MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Tạo đơn hàng mới
 */
function createOrder(orderData) {
  try {
    Logger.log('Creating order:', JSON.stringify(orderData));
    
    // 1. Validate dữ liệu đầu vào
    const validation = validateOrderData(orderData);
    if (!validation.isValid) {
      return apiResponse(null, {}, validation.errors, 400);
    }
    
    // 2. Tạo Order ID
    const orderId = generateOrderId();
    
    // 3. Tính toán tổng tiền
    const totals = calculateOrderTotals(orderData.items, orderData.shippingAddress, orderData.paymentMethod);
    
    // 4. Tạo object đơn hàng
    const order = {
      orderId: orderId,
      customerPhone: orderData.customer.phone,
      customerName: orderData.customer.name,
      customerEmail: orderData.customer.email || '',
      orderDate: new Date(),
      items: orderData.items,
      subtotal: totals.subtotal,
      shippingFee: totals.shipping,
      codFee: totals.codFee,
      discount: totals.discount,
      total: totals.total,
      paymentMethod: orderData.paymentMethod,
      shippingAddress: orderData.shippingAddress,
      notes: orderData.notes || '',
      status: orderData.paymentMethod === 'cod' ? 'confirmed' : 'pending_payment'
    };
    
    // 5. Lưu vào Orders sheet
    const orderSaved = saveOrderToSheet(order);
    if (!orderSaved) {
      return apiResponse(null, {}, 'Lỗi khi lưu đơn hàng', 500);
    }
    
    // 6. Lưu chi tiết đơn hàng
    const detailsSaved = saveOrderDetails(orderId, orderData.items);
    if (!detailsSaved) {
      return apiResponse(null, {}, 'Lỗi khi lưu chi tiết đơn hàng', 500);
    }
    
    // 7. Cập nhật thông tin khách hàng
    updateCustomerInfo(orderData.customer, order);
    
    // 8. Gửi email xác nhận
    sendOrderConfirmationEmail(order);
    
    // 9. Tạo URL thanh toán (nếu cần)
    let paymentUrl = null;
    if (orderData.paymentMethod === 'vnpay') {
      paymentUrl = generateVNPayURL(order);
    } else if (orderData.paymentMethod === 'bank') {
      sendBankTransferInstructions(order);
    }
    
    // 10. Log order created
    logOrderActivity(orderId, 'created', 'Đơn hàng được tạo');
    
    return apiResponse({
      orderId: orderId,
      status: order.status,
      total: order.total,
      paymentUrl: paymentUrl,
      message: 'Đơn hàng đã được tạo thành công'
    });
    
  } catch (error) {
    Logger.log('Create order error:', error.toString());
    return apiResponse(null, {}, 'Có lỗi xảy ra khi tạo đơn hàng: ' + error.toString(), 500);
  }
}

/**
 * Validate dữ liệu đơn hàng
 */
function validateOrderData(orderData) {
  const errors = [];
  
  // Validate customer info
  if (!orderData.customer) {
    errors.push('Thông tin khách hàng là bắt buộc');
  } else {
    if (!orderData.customer.name || orderData.customer.name.trim().length < 2) {
      errors.push('Tên khách hàng phải có ít nhất 2 ký tự');
    }
    
    if (!orderData.customer.phone || !isValidPhone(orderData.customer.phone)) {
      errors.push('Số điện thoại không hợp lệ');
    }
    
    if (orderData.customer.email && !isValidEmail(orderData.customer.email)) {
      errors.push('Email không hợp lệ');
    }
  }
  
  // Validate shipping address
  if (!orderData.shippingAddress) {
    errors.push('Địa chỉ giao hàng là bắt buộc');
  } else {
    if (!orderData.shippingAddress.address || orderData.shippingAddress.address.trim().length < 5) {
      errors.push('Địa chỉ giao hàng phải có ít nhất 5 ký tự');
    }
    
    if (!orderData.shippingAddress.district) {
      errors.push('Quận/Huyện là bắt buộc');
    }
    
    if (!orderData.shippingAddress.province) {
      errors.push('Tỉnh/Thành phố là bắt buộc');
    }
  }
  
  // Validate items
  if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
    errors.push('Đơn hàng phải có ít nhất 1 sản phẩm');
  } else {
    orderData.items.forEach((item, index) => {
      if (!item.sku) {
        errors.push(`Sản phẩm ${index + 1}: SKU là bắt buộc`);
      }
      if (!item.quantity || item.quantity < 1) {
        errors.push(`Sản phẩm ${index + 1}: Số lượng phải lớn hơn 0`);
      }
      if (!item.price || item.price <= 0) {
        errors.push(`Sản phẩm ${index + 1}: Giá phải lớn hơn 0`);
      }
    });
  }
  
  // Validate payment method
  if (!orderData.paymentMethod || !ORDER_CONFIG.PAYMENT_METHODS[orderData.paymentMethod]) {
    errors.push('Phương thức thanh toán không hợp lệ');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * Tính toán tổng tiền đơn hàng
 */
function calculateOrderTotals(items, shippingAddress, paymentMethod) {
  // Tính subtotal
  const subtotal = items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
  
  // Tính phí vận chuyển
  let shippingFee = 0;
  if (subtotal < ORDER_CONFIG.FREE_SHIPPING_THRESHOLD) {
    // Có thể tính phí vận chuyển theo khu vực
    shippingFee = calculateShippingFee(shippingAddress, subtotal);
  }
  
  // Tính phí COD
  let codFee = 0;
  if (paymentMethod === 'cod') {
    codFee = Math.round(subtotal * ORDER_CONFIG.COD_FEE_RATE);
  }
  
  // Tính giảm giá (nếu có)
  const discount = 0; // TODO: Implement promotion logic
  
  // Tổng cộng
  const total = subtotal + shippingFee + codFee - discount;
  
  return {
    subtotal: subtotal,
    shipping: shippingFee,
    codFee: codFee,
    discount: discount,
    total: total
  };
}

/**
 * Tính phí vận chuyển theo khu vực
 */
function calculateShippingFee(shippingAddress, subtotal) {
  // Logic tính phí vận chuyển
  const province = shippingAddress.province.toLowerCase();
  
  // Miền Nam (gần nhà máy) - phí thấp
  const southernProvinces = ['ho chi minh', 'dong nai', 'binh duong', 'tay ninh', 'long an'];
  if (southernProvinces.some(p => province.includes(p))) {
    return 25000;
  }
  
  // Miền Bắc và Miền Trung - phí cao hơn
  return ORDER_CONFIG.SHIPPING_FEE;
}

/**
 * Tạo Order ID
 */
function generateOrderId() {
  const timestamp = new Date();
  const year = timestamp.getFullYear().toString().slice(-2);
  const month = (timestamp.getMonth() + 1).toString().padStart(2, '0');
  const day = timestamp.getDate().toString().padStart(2, '0');
  const time = timestamp.getTime().toString().slice(-5);
  
  return `${ORDER_CONFIG.ORDER_PREFIX}${year}${month}${day}${time}`;
}

/**
 * Lưu đơn hàng vào Orders sheet
 */
function saveOrderToSheet(order) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    
    const row = [
      order.orderId,                           // OrderID
      order.customerPhone,                     // SoDienThoai  
      order.customerName,                      // TenKhachHang
      order.orderDate,                         // NgayDatHang
      order.subtotal,                          // TongTienSanPham
      order.shippingFee,                       // PhiVanChuyen
      '',                                      // MaGiamGiaDaApDung
      order.discount,                          // SoTienGiamGiaTuKM
      order.total,                             // ThanhTien
      ORDER_CONFIG.PAYMENT_METHODS[order.paymentMethod], // PhuongThucThanhToan
      ORDER_CONFIG.ORDER_STATUS[order.status], // TrangThaiDonHang
      JSON.stringify({                         // GhiChu (JSON format)
        email: order.customerEmail,
        address: order.shippingAddress,
        notes: order.notes,
        codFee: order.codFee
      })
    ];
    
    ordersSheet.appendRow(row);
    return true;
    
  } catch (error) {
    Logger.log('Save order error:', error.toString());
    return false;
  }
}

/**
 * Lưu chi tiết đơn hàng vào OrderDetails sheet
 */
function saveOrderDetails(orderId, items) {
  try {
    const detailsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('OrderDetails');
    
    items.forEach(item => {
      const row = [
        orderId,                  // OrderID
        item.sku,                 // ProductID (SKU)
        item.productName,         // TenSanPham
        item.quantity,            // SoLuong
        item.price,               // DonGiaLucMua
        item.price * item.quantity, // ThanhTien
        item.variant || ''        // GhiChu (variant info)
      ];
      
      detailsSheet.appendRow(row);
    });
    
    return true;
    
  } catch (error) {
    Logger.log('Save order details error:', error.toString());
    return false;
  }
}

/**
 * Cập nhật thông tin khách hàng
 */
function updateCustomerInfo(customerData, order) {
  try {
    const customersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Customers');
    const data = customersSheet.getDataRange().getValues();
    
    // Tìm khách hàng theo số điện thoại
    let customerRowIndex = -1;
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === customerData.phone) { // SoDienThoai column
        customerRowIndex = i;
        break;
      }
    }
    
    if (customerRowIndex > -1) {
      // Cập nhật khách hàng hiện tại
      const currentData = data[customerRowIndex];
      const totalOrders = (currentData[7] || 0) + 1; // TongSoDonHang
      const totalSpent = (currentData[8] || 0) + order.total; // TongChiTieu
      const avgOrderValue = totalSpent / totalOrders; // GiaTriDonTrungBinh
      const loyaltyPoints = (currentData[5] || 0) + Math.floor(order.total / 10000); // DiemTichLuy (1 điểm/10k)
      
      // Update row
      customersSheet.getRange(customerRowIndex + 1, 2).setValue(customerData.name); // TenKhachHang
      customersSheet.getRange(customerRowIndex + 1, 3).setValue(customerData.email || ''); // Email
      customersSheet.getRange(customerRowIndex + 1, 6).setValue(loyaltyPoints); // DiemTichLuy
      customersSheet.getRange(customerRowIndex + 1, 8).setValue(totalOrders); // TongSoDonHang
      customersSheet.getRange(customerRowIndex + 1, 9).setValue(totalSpent); // TongChiTieu
      customersSheet.getRange(customerRowIndex + 1, 10).setValue(avgOrderValue); // GiaTriDonTrungBinh
      customersSheet.getRange(customerRowIndex + 1, 12).setValue(new Date()); // NgayMuaCuoiCung
      customersSheet.getRange(customerRowIndex + 1, 13).setValue(JSON.stringify(order.shippingAddress)); // DiaChiGiaoHang
      
    } else {
      // Tạo khách hàng mới
      const loyaltyPoints = Math.floor(order.total / 10000);
      const newCustomer = [
        customerData.phone,                    // SoDienThoai
        customerData.name,                     // TenKhachHang
        customerData.email || '',              // Email
        '',                                    // ViDo
        '',                                    // KinhDo
        loyaltyPoints,                         // DiemTichLuy
        loyaltyPoints >= 100 ? 'Bạc' : 'Đồng', // HangThanhVien
        1,                                     // TongSoDonHang
        order.total,                           // TongChiTieu
        order.total,                           // GiaTriDonTrungBinh
        new Date(),                            // NgayMuaDauTien
        new Date(),                            // NgayMuaCuoiCung
        JSON.stringify(order.shippingAddress), // DiaChiGiaoHang
        '',                                    // GhiChu
        '',                                    // PasswordHash
        '',                                    // Salt
        new Date(),                            // JoinedAt
        '',                                    // LastLogin
        false,                                 // IsBlocked
        '',                                    // TenDonVi
        '',                                    // MaSoThue
        '',                                    // DiaChiDonVi
        '',                                    // CCCD
        ''                                     // NgaySinh
      ];
      
      customersSheet.appendRow(newCustomer);
    }
    
  } catch (error) {
    Logger.log('Update customer error:', error.toString());
  }
}

/**
 * Gửi email xác nhận đơn hàng
 */
function sendOrderConfirmationEmail(order) {
  try {
    if (!order.customerEmail) return;
    
    const subject = `Xác nhận đơn hàng #${order.orderId} - Lotus Glass`;
    const htmlBody = generateOrderConfirmationHTML(order);
    
    GmailApp.sendEmail(
      order.customerEmail,
      subject,
      '', // Plain text fallback
      {
        htmlBody: htmlBody,
        name: ORDER_CONFIG.FROM_NAME,
        replyTo: ORDER_CONFIG.ADMIN_EMAIL
      }
    );
    
    // Log email sent
    logOrderActivity(order.orderId, 'email_sent', 'Email xác nhận đã gửi');
    
  } catch (error) {
    Logger.log('Send email error:', error.toString());
  }
}

/**
 * Tạo HTML template cho email xác nhận
 */
function generateOrderConfirmationHTML(order) {
  const itemsHTML = order.items.map(item => `
    <tr style="border-bottom: 1px solid #eee;">
      <td style="padding: 10px; text-align: left;">
        <strong>${item.productName}</strong><br>
        <small style="color: #666;">${item.variant || ''}</small>
      </td>
      <td style="padding: 10px; text-align: center;">${item.quantity}</td>
      <td style="padding: 10px; text-align: right;">${formatPrice(item.price)}</td>
      <td style="padding: 10px; text-align: right; font-weight: bold;">${formatPrice(item.price * item.quantity)}</td>
    </tr>
  `).join('');
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Xác nhận đơn hàng #${order.orderId}</title>
    </head>
    <body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f8f9fa;">
      <div style="max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: #f37021; color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Xác nhận đơn hàng</h1>
          <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">#${order.orderId}</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px;">
          <p style="font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
            Xin chào <strong>${order.customerName}</strong>,
          </p>
          
          <p style="font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
            Cảm ơn bạn đã đặt hàng tại <strong>Lotus Glass</strong>. Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.
          </p>
          
          <!-- Order Details -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 30px;">
            <h3 style="margin: 0 0 15px 0; color: #f37021;">Chi tiết đơn hàng</h3>
            
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #e9ecef;">
                  <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Sản phẩm</th>
                  <th style="padding: 10px; text-align: center; border-bottom: 2px solid #ddd;">SL</th>
                  <th style="padding: 10px; text-align: right; border-bottom: 2px solid #ddd;">Đơn giá</th>
                  <th style="padding: 10px; text-align: right; border-bottom: 2px solid #ddd;">Thành tiền</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHTML}
              </tbody>
            </table>
            
            <div style="margin-top: 20px; text-align: right;">
              <div style="margin-bottom: 8px;">
                <span>Tạm tính: </span>
                <span style="font-weight: bold;">${formatPrice(order.subtotal)}</span>
              </div>
              ${order.shippingFee > 0 ? `
                <div style="margin-bottom: 8px;">
                  <span>Phí vận chuyển: </span>
                  <span style="font-weight: bold;">${formatPrice(order.shippingFee)}</span>
                </div>
              ` : ''}
              ${order.codFee > 0 ? `
                <div style="margin-bottom: 8px;">
                  <span>Phí COD: </span>
                  <span style="font-weight: bold;">${formatPrice(order.codFee)}</span>
                </div>
              ` : ''}
              ${order.discount > 0 ? `
                <div style="margin-bottom: 8px; color: #28a745;">
                  <span>Giảm giá: </span>
                  <span style="font-weight: bold;">-${formatPrice(order.discount)}</span>
                </div>
              ` : ''}
              <div style="border-top: 2px solid #ddd; padding-top: 10px; margin-top: 10px;">
                <span style="font-size: 18px; font-weight: bold;">Tổng cộng: </span>
                <span style="font-size: 20px; font-weight: bold; color: #f37021;">${formatPrice(order.total)}</span>
              </div>
            </div>
          </div>
          
          <!-- Shipping Info -->
          <div style="margin-bottom: 30px;">
            <h3 style="margin: 0 0 10px 0; color: #f37021;">Thông tin giao hàng</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
              <p style="margin: 0 0 5px 0;"><strong>Người nhận:</strong> ${order.customerName}</p>
              <p style="margin: 0 0 5px 0;"><strong>Điện thoại:</strong> ${order.customerPhone}</p>
              <p style="margin: 0 0 5px 0;"><strong>Địa chỉ:</strong> ${order.shippingAddress.address}</p>
              <p style="margin: 0;"><strong>Khu vực:</strong> ${order.shippingAddress.district}, ${order.shippingAddress.province}</p>
            </div>
          </div>
          
          <!-- Payment Info -->
          <div style="margin-bottom: 30px;">
            <h3 style="margin: 0 0 10px 0; color: #f37021;">Phương thức thanh toán</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
              <p style="margin: 0;"><strong>${ORDER_CONFIG.PAYMENT_METHODS[order.paymentMethod]}</strong></p>
              ${order.paymentMethod === 'bank' ? `
                <p style="margin: 10px 0 0 0; color: #666; font-style: italic;">
                  Thông tin chuyển khoản sẽ được gửi trong email riêng.
                </p>
              ` : ''}
            </div>
          </div>
          
          <!-- Tracking -->
          <div style="text-align: center; margin-bottom: 30px;">
            <p style="margin: 0 0 15px 0; font-size: 16px;">
              Bạn có thể theo dõi trạng thái đơn hàng tại:
            </p>
            <a href="https://lotusglassvietnam.blogspot.com/order-tracking?id=${order.orderId}" 
               style="display: inline-block; background: #f37021; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">
              Xem chi tiết đơn hàng
            </a>
          </div>
          
          <!-- Contact -->
          <div style="border-top: 1px solid #ddd; padding-top: 20px; text-align: center;">
            <p style="margin: 0 0 10px 0; font-size: 16px; color: #666;">
              Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ:
            </p>
            <p style="margin: 0; font-size: 16px;">
              📞 <strong>Hotline:</strong> 0981 500 400<br>
              📧 <strong>Email:</strong> ${ORDER_CONFIG.ADMIN_EMAIL}
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; color: #666;">
            <p style="margin: 0; font-style: italic;">
              Trân trọng,<br>
              <strong>Đội ngũ Lotus Glass</strong>
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Gửi hướng dẫn chuyển khoản
 */
function sendBankTransferInstructions(order) {
  try {
    if (!order.customerEmail) return;
    
    const subject = `Hướng dẫn thanh toán đơn hàng #${order.orderId} - Lotus Glass`;
    
    const bankInfo = {
      bankName: 'Vietcombank',
      accountNumber: '**********',
      accountName: 'CONG TY PHA LE VIET TIEP',
      branch: 'Chi nhánh Tây Ninh'
    };
    
    const transferContent = `LOTUS ${order.orderId}`;
    
    const htmlBody = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2 style="color: #f37021;">Hướng dẫn chuyển khoản</h2>
        
        <p>Đơn hàng: <strong>#${order.orderId}</strong></p>
        <p>Số tiền: <strong style="color: #f37021; font-size: 18px;">${formatPrice(order.total)}</strong></p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Thông tin tài khoản:</h3>
          <p><strong>Ngân hàng:</strong> ${bankInfo.bankName}</p>
          <p><strong>Số tài khoản:</strong> ${bankInfo.accountNumber}</p>
          <p><strong>Tên tài khoản:</strong> ${bankInfo.accountName}</p>
          <p><strong>Chi nhánh:</strong> ${bankInfo.branch}</p>
          <p><strong>Nội dung chuyển khoản:</strong> <span style="color: #f37021; font-weight: bold;">${transferContent}</span></p>
        </div>
        
        <p><strong>Lưu ý quan trọng:</strong></p>
        <ul>
          <li>Vui lòng chuyển khoản đúng số tiền: <strong>${formatPrice(order.total)}</strong></li>
          <li>Ghi chính xác nội dung: <strong>${transferContent}</strong></li>
          <li>Sau khi chuyển khoản, vui lòng gửi ảnh chụp biên lai cho chúng tôi</li>
          <li>Đơn hàng sẽ được xử lý trong vòng 2-4 giờ sau khi nhận được thanh toán</li>
        </ul>
        
        <p>Liên hệ: ${ORDER_CONFIG.ADMIN_EMAIL} | 0981 500 400</p>
      </div>
    `;
    
    GmailApp.sendEmail(
      order.customerEmail,
      subject,
      '',
      {
        htmlBody: htmlBody,
        name: ORDER_CONFIG.FROM_NAME,
        replyTo: ORDER_CONFIG.ADMIN_EMAIL
      }
    );
    
  } catch (error) {
    Logger.log('Send bank transfer email error:', error.toString());
  }
}

/**
 * Log hoạt động đơn hàng
 */
function logOrderActivity(orderId, activity, note) {
  try {
    // Có thể tạo sheet OrderLogs để tracking
    Logger.log(`Order ${orderId}: ${activity} - ${note}`);
  } catch (error) {
    Logger.log('Log activity error:', error.toString());
  }
}

// ============================================================================
// ORDER TRACKING & STATUS UPDATE
// ============================================================================

/**
 * Lấy thông tin đơn hàng theo ID
 */
function getOrderStatus(orderId) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    
    // Tìm đơn hàng
    let orderRow = null;
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        orderRow = data[i];
        break;
      }
    }
    
    if (!orderRow) {
      return apiResponse(null, {}, 'Không tìm thấy đơn hàng', 404);
    }
    
    // Lấy chi tiết đơn hàng
    const orderDetails = getOrderDetails(orderId);
    
    // Parse additional info từ GhiChu column
    let additionalInfo = {};
    try {
      additionalInfo = JSON.parse(orderRow[11] || '{}');
    } catch (e) {
      additionalInfo = {};
    }
    
    const orderInfo = {
      orderId: orderRow[0],
      customerPhone: orderRow[1],
      customerName: orderRow[2],
      orderDate: orderRow[3],
      subtotal: orderRow[4],
      shippingFee: orderRow[5],
      discount: orderRow[7],
      total: orderRow[8],
      paymentMethod: orderRow[9],
      status: orderRow[10],
      customerEmail: additionalInfo.email || '',
      shippingAddress: additionalInfo.address || {},
      notes: additionalInfo.notes || '',
      items: orderDetails
    };
    
    return apiResponse(orderInfo);
    
  } catch (error) {
    Logger.log('Get order status error:', error.toString());
    return apiResponse(null, {}, 'Lỗi khi lấy thông tin đơn hàng', 500);
  }
}

/**
 * Lấy chi tiết sản phẩm trong đơn hàng
 */
function getOrderDetails(orderId) {
  try {
    const detailsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('OrderDetails');
    const data = detailsSheet.getDataRange().getValues();
    
    const details = [];
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        details.push({
          productId: data[i][1],
          productName: data[i][2],
          quantity: data[i][3],
          unitPrice: data[i][4],
          totalPrice: data[i][5],
          variant: data[i][6] || ''
        });
      }
    }
    
    return details;
    
  } catch (error) {
    Logger.log('Get order details error:', error.toString());
    return [];
  }
}

/**
 * Cập nhật trạng thái đơn hàng
 */
function updateOrderStatus(orderId, newStatus, note) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    
    // Tìm và cập nhật đơn hàng
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        // Cập nhật trạng thái
        ordersSheet.getRange(i + 1, 11).setValue(ORDER_CONFIG.ORDER_STATUS[newStatus] || newStatus);
        
        // Log activity
        logOrderActivity(orderId, 'status_updated', `Cập nhật trạng thái: ${newStatus}. ${note}`);
        
        // Gửi email thông báo (nếu có)
        sendStatusUpdateEmail(orderId, newStatus, note);
        
        return apiResponse({ 
          success: true, 
          message: `Đã cập nhật trạng thái đơn hàng thành "${ORDER_CONFIG.ORDER_STATUS[newStatus] || newStatus}"` 
        });
      }
    }
    
    return apiResponse(null, {}, 'Không tìm thấy đơn hàng', 404);
    
  } catch (error) {
    Logger.log('Update order status error:', error.toString());
    return apiResponse(null, {}, 'Lỗi khi cập nhật trạng thái', 500);
  }
}

/**
 * Gửi email cập nhật trạng thái
 */
function sendStatusUpdateEmail(orderId, newStatus, note) {
  try {
    const orderInfo = getOrderStatus(orderId);
    if (!orderInfo.success || !orderInfo.data.customerEmail) return;
    
    const order = orderInfo.data;
    const statusName = ORDER_CONFIG.ORDER_STATUS[newStatus] || newStatus;
    
    const subject = `Cập nhật đơn hàng #${orderId} - ${statusName}`;
    
    const htmlBody = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="background: #f37021; color: white; padding: 20px; text-align: center;">
          <h2>Cập nhật đơn hàng #${orderId}</h2>
        </div>
        
        <div style="padding: 20px;">
          <p>Xin chào ${order.customerName},</p>
          
          <p>Đơn hàng #${orderId} của bạn đã được cập nhật trạng thái:</p>
          
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #f37021; margin: 0 0 10px 0;">Trạng thái hiện tại</h3>
            <p style="font-size: 18px; font-weight: bold; margin: 0; color: #28a745;">${statusName}</p>
            ${note ? `<p style="margin: 10px 0 0 0; color: #666; font-style: italic;">${note}</p>` : ''}
          </div>
          
          <p>Bạn có thể theo dõi chi tiết tại: 
             <a href="https://lotusglassvietnam.blogspot.com/order-tracking?id=${orderId}">Xem đơn hàng</a>
          </p>
          
          <p>Cảm ơn bạn đã tin tưởng Lotus Glass!</p>
          
          <p>Trân trọng,<br>Đội ngũ Lotus Glass</p>
        </div>
      </div>
    `;
    
    GmailApp.sendEmail(
      order.customerEmail,
      subject,
      '',
      {
        htmlBody: htmlBody,
        name: ORDER_CONFIG.FROM_NAME,
        replyTo: ORDER_CONFIG.ADMIN_EMAIL
      }
    );
    
  } catch (error) {
    Logger.log('Send status email error:', error.toString());
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate số điện thoại Việt Nam
 */
function isValidPhone(phone) {
  const phoneRegex = /^(\+84|0)[1-9]\d{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * Validate email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Format giá tiền
 */
function formatPrice(price) {
  if (typeof price !== 'number' || isNaN(price)) return 'Liên hệ';
  return price.toLocaleString('vi-VN') + '₫';
}

// ============================================================================
// ENHANCED API ROUTER - Thêm vào doGet/doPost hiện tại
// ============================================================================

/**
 * Mở rộng hàm doPost để xử lý order endpoints
 */
function doPost(e) {
  try {
    const action = e.parameter.action;
    
    switch (action) {
      case 'createOrder':
        const orderData = JSON.parse(e.parameter.orderData || '{}');
        return createJsonResponse(createOrder(orderData));
        
      case 'getOrderStatus':
        const orderId = e.parameter.orderId;
        return createJsonResponse(getOrderStatus(orderId));
        
      case 'updateOrderStatus':
        const updateOrderId = e.parameter.orderId;
        const newStatus = e.parameter.status;
        const note = e.parameter.note || '';
        return createJsonResponse(updateOrderStatus(updateOrderId, newStatus, note));
        
      case 'validateCart':
        const cartData = JSON.parse(e.parameter.cartData || '[]');
        return createJsonResponse(validateCart(cartData));
        
      default:
        // Fallback to existing doGet for product APIs
        return doGet(e);
    }
    
  } catch (error) {
    Logger.log('doPost error:', error.toString());
    return createJsonResponse(apiResponse(null, {}, 'Server error: ' + error.toString(), 500));
  }
}

/**
 * Validate giỏ hàng trước khi checkout
 */
function validateCart(cartItems) {
  try {
    const data = loadAllDataFromCacheOrSheets();
    const errors = [];
    const validatedItems = [];
    
    for (const item of cartItems) {
      // Tìm sản phẩm trong database
      const product = data.productsData.find(p => p.sku === item.sku);
      
      if (!product) {
        errors.push(`Sản phẩm ${item.productName} không tồn tại`);
        continue;
      }
      
      if (!parseBoolean(product.is_active)) {
        errors.push(`Sản phẩm ${item.productName} hiện không có sẵn`);
        continue;
      }
      
      // Validate giá
      const currentPrice = parseFloat(product.price_case_TSG) || parseFloat(product.price_piece_TSG);
      if (Math.abs(currentPrice - item.price) > 0.01) {
        errors.push(`Giá sản phẩm ${item.productName} đã thay đổi`);
      }
      
      // Add validated item
      validatedItems.push({
        ...item,
        currentPrice: currentPrice,
        available: true
      });
    }
    
    return apiResponse({
      valid: errors.length === 0,
      errors: errors,
      items: validatedItems
    });
    
  } catch (error) {
    Logger.log('Validate cart error:', error.toString());
    return apiResponse(null, {}, 'Lỗi khi kiểm tra giỏ hàng', 500);
  }
}

// Log successful load
Logger.log('✅ Lotus Glass Order Management System loaded successfully!');
/**
 * @fileoverview 🌸 LOTUS GLASS - API BACKEND TÁI CẤU TRÚC VÀ NÂNG CẤP
 * @version 5.0 (Dựa trên code.gs và tích hợp module từ code-gas.gs)
 * @description
 * - Tái cấu trúc với bộ định tuyến (router) trung tâm (doGet/doPost -> handleRequest).
 * - Tích hợp CacheManager để quản lý cache hiệu quả với TTL (Time-to-Live).
 * - Chuẩn hóa JSON response để đồng bộ và dễ dàng xử lý phía client.
 * - Tích hợp module logging hoạt động vào sheet 'ChangeLog'.
 * - Thêm chức năng dự báo tồn kho thông minh.
 * - Giữ nguyên cấu trúc Sheet gốc và chỉ đề xuất thêm sheet cần thiết.
 * <AUTHOR> by Gemini
 */

// ===================================================================================
// --- I. CẤU HÌNH TRUNG TÂM (CONFIGURATION) ---
// ===================================================================================
const CONFIG = {
  // ID của Google Sheet - YÊU CẦU CẬP NHẬT
  SPREADSHEET_ID: '1G5I-mZymVNxODCe7q0yQIJkcVOFgoYUHuPDOSmc59wY', // <<< THAY ID CỦA BẠN VÀO ĐÂY

  // Tên các trang tính (Sheets) đang sử dụng
  SHEETS: {
    CATEGORIES: 'Categories',
    PRODUCTS: 'Products',
    PROMOTIONS: 'Promotions',
    APPLIED_PRODUCTS: 'AppliedProducts',
    PROMOTION_RULES: 'PromotionRules',
    ORDERS: 'Orders',
    ORDER_DETAILS: 'OrderDetails',
    CUSTOMERS: 'Customers',
    // ĐỀ XUẤT THÊM SHEET MỚI để ghi log
    CHANGE_LOG: 'ChangeLog'
  },

  // Cấu hình bộ đệm (Cache)
  CACHE: {
    ENABLED: true,
    PREFIX: 'LG_API_V5_', // Thay đổi prefix khi có cập nhật lớn
    DEFAULT_TTL_SECONDS: 300 // 5 phút
  },

  // Cấu hình phân trang
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
  },

  // Cấu hình bảo mật
  SECURITY: {
    // Trong một ứng dụng thực tế, nên có cơ chế xác thực API Key
    API_KEY_ENABLED: false,
    VALID_API_KEYS: ['YOUR_SECRET_API_KEY_HERE'] // Ví dụ
  }
};


// ===================================================================================
// --- II. BỘ ĐỊNH TUYẾN TRUNG TÂM (API ROUTER) ---
// ===================================================================================

/**
 * Xử lý các yêu cầu GET.
 * @param {Object} e - Đối tượng sự kiện từ Google Apps Script.
 * @returns {ContentService.TextOutput} - Phản hồi dạng JSON.
 */
function doGet(e) {
  return handleRequest(e);
}

/**
 * Xử lý các yêu cầu POST.
 * @param {Object} e - Đối tượng sự kiện từ Google Apps Script.
 * @returns {ContentService.TextOutput} - Phản hồi dạng JSON.
 */
function doPost(e) {
  return handleRequest(e);
}

/**
 * Hàm xử lý yêu cầu chính, được gọi bởi doGet và doPost.
 * @param {Object} e - Đối tượng sự kiện.
 * @returns {ContentService.TextOutput} - Phản hồi dạng JSON.
 */
function handleRequest(e) {
  const params = (e.postData && e.postData.contents) ? JSON.parse(e.postData.contents) : e.parameter;
  const action = params.action;

  try {
    // Ghi log mỗi khi có request
    logActivity('API_REQUEST', 'system', `Action: ${action}`, params);

    // // (Tùy chọn) Kiểm tra API Key nếu được bật
    // if (CONFIG.SECURITY.API_KEY_ENABLED && !CONFIG.SECURITY.VALID_API_KEYS.includes(params.apiKey)) {
    //   throw { code: 401, message: "Unauthorized: Invalid API Key." };
    // }

    switch (action) {
      // --- API Sản phẩm & Danh mục ---
      case 'getCategories':
        return createJsonResponse(getCategories());
      case 'getProducts':
        return createJsonResponse(getProducts(params));
      case 'getProductDetail':
        return createJsonResponse(getProductDetail(params));

      // --- API Đơn hàng (ví dụ) ---
      case 'createOrder':
        // Logic tạo đơn hàng sẽ được xử lý ở đây, cần dữ liệu từ POST
        // return createJsonResponse(createOrder(params));
        throw { code: 501, message: "Chức năng 'createOrder' chưa được triển khai." };

      // --- MODULE NÂNG CAO: DỰ BÁO TỒN KHO ---
      case 'predictInventory':
        return createJsonResponse(predictInventory(params));

      // --- API Hệ thống ---
      case 'ping':
        return createJsonResponse({ success: true, data: 'pong', timestamp: new Date().toISOString() });
      case 'clearCache':
        CacheManager.clearAll();
        return createJsonResponse({ success: true, message: 'Toàn bộ cache đã được xóa.' });

      default:
        throw { code: 400, message: `Hành động không hợp lệ: '${action}'.` };
    }
  } catch (err) {
    const status = err.code || 500;
    const message = err.message || (err.toString ? err.toString() : "Lỗi không xác định");
    logActivity('API_ERROR', 'system', `Status: ${status} - Message: ${message}`, { action, params });
    return createJsonResponse({ success: false, error: true, code: status, message: message }, status);
  }
}


// ===================================================================================
// --- III. CÁC MODULE LÕI VÀ NÂNG CAO (CORE & ADVANCED MODULES) ---
// ===================================================================================

/**
 * MODULE: Quản lý Cache (CacheManager)
 * Cung cấp các phương thức get, set, remove, clear cho cache.
 */
const CacheManager = {
  get: function(key) {
    if (!CONFIG.CACHE.ENABLED) return null;
    try {
      const cache = CacheService.getScriptCache();
      const cachedValue = cache.get(CONFIG.CACHE.PREFIX + key);
      return cachedValue ? JSON.parse(cachedValue) : null;
    } catch (e) {
      logActivity('CACHE_ERROR', 'system', `Get failed for key: ${key}`, e.toString());
      return null;
    }
  },
  set: function(key, value, ttl = CONFIG.CACHE.DEFAULT_TTL_SECONDS) {
    if (!CONFIG.CACHE.ENABLED) return;
    try {
      const cache = CacheService.getScriptCache();
      cache.put(CONFIG.CACHE.PREFIX + key, JSON.stringify(value), ttl);
    } catch (e) {
      logActivity('CACHE_ERROR', 'system', `Set failed for key: ${key}`, e.toString());
    }
  },
  remove: function(key) {
    if (!CONFIG.CACHE.ENABLED) return;
    const cache = CacheService.getScriptCache();
    cache.remove(CONFIG.CACHE.PREFIX + key);
  },
  clearAll: function() {
    if (!CONFIG.CACHE.ENABLED) return;
    const cache = CacheService.getScriptCache();
    cache.removeAll([CONFIG.CACHE.PREFIX + 'SHEET_Categories', CONFIG.CACHE.PREFIX + 'SHEET_Products']); // Ví dụ
    logActivity('CACHE_CLEAR', 'system', 'All cache cleared');
  }
};

/**
 * MODULE: Logging Hoạt động
 * Ghi lại các hoạt động quan trọng vào sheet 'ChangeLog'.
 * @param {string} action - Loại hành động (e.g., 'API_REQUEST', 'CACHE_CLEAR').
 * @param {string} user - Người thực hiện (e.g., 'system', 'user_id').
 * @param {string} details - Mô tả chi tiết.
 * @param {Object} data - Dữ liệu liên quan (dạng JSON).
 */
function logActivity(action, user, details, data = {}) {
  try {
    const sheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID).getSheetByName(CONFIG.SHEETS.CHANGE_LOG);
    // Nếu sheet không tồn tại, bỏ qua để tránh lỗi
    if (!sheet) {
      console.log(`Sheet '${CONFIG.SHEETS.CHANGE_LOG}' not found. Skipping log.`);
      return;
    }
    const timestamp = new Date();
    const dataString = JSON.stringify(data);
    sheet.appendRow([timestamp, action, user, details, dataString]);
  } catch (e) {
    console.error("Failed to log activity: " + e.toString());
  }
}


// ===================================================================================
// --- IV. CÁC HÀM API CHÍNH (CORE API FUNCTIONS) ---
// ===================================================================================

/**
 * Lấy danh sách các danh mục đang hoạt động.
 * Dữ liệu được lấy từ cache nếu có.
 * @returns {Object} - Dữ liệu trả về chuẩn hóa.
 */
function getCategories() {
  const cacheKey = 'categories_active';
  let categories = CacheManager.get(cacheKey);

  if (!categories) {
    const allCategories = getSheetData(CONFIG.SHEETS.CATEGORIES);
    categories = allCategories.filter(cat => parseBoolean(cat.TrangThai));
    CacheManager.set(cacheKey, categories);
  }

  // Cấu trúc lại dữ liệu danh mục thành cây cha-con
  const categoryTree = buildCategoryTree(categories);

  return { success: true, data: categoryTree };
}

/**
 * Lấy danh sách sản phẩm có phân trang, lọc và sắp xếp.
 * @param {Object} params - Các tham số từ request (page, pageSize, category, sort, query, featured, new).
 * @returns {Object} - Dữ liệu trả về chuẩn hóa, bao gồm cả metadata phân trang.
 */
function getProducts(params) {
  const { category, sort, query, featured, new: isNew } = params;
  const page = Math.max(1, parseInt(params.page) || 1);
  const pageSize = Math.min(CONFIG.PAGINATION.MAX_PAGE_SIZE, Math.max(1, parseInt(params.pageSize) || CONFIG.PAGINATION.DEFAULT_PAGE_SIZE));

  // Tạo cache key động dựa trên các tham số
  const cacheKey = `products_p${page}_s${pageSize}_cat${category}_sort${sort}_q${query}_feat${featured}_new${isNew}`;
  const cachedData = CacheManager.get(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // Tải dữ liệu từ các sheet liên quan
  const allProducts = getSheetData(CONFIG.SHEETS.PRODUCTS);
  const allCategories = getSheetData(CONFIG.SHEETS.CATEGORIES);
  const allPromotions = getSheetData(CONFIG.SHEETS.PROMOTIONS);
  const allAppliedProducts = getSheetData(CONFIG.SHEETS.APPLIED_PRODUCTS);

  // Nhóm các biến thể sản phẩm lại thành một sản phẩm duy nhất
  const groupedProducts = allProducts
    .filter(p => parseBoolean(p.is_active))
    .reduce((acc, p) => {
      const groupName = p.product_name;
      if (!groupName) return acc;
      if (!acc[groupName]) {
        acc[groupName] = {
          product_name: groupName,
          category_id: p.category_id,
          group: p.group,
          thumbnail: (p.image_url || '').split(',')[0].trim(),
          list_price: Infinity,
          selling_price: Infinity,
          is_featured: false,
          is_new: false,
          hasPromotion: false,
        };
      }
      // Cập nhật giá bán thấp nhất
      acc[groupName].list_price = Math.min(acc[groupName].list_price, parseFloat(p.price_case) || Infinity);
      acc[groupName].selling_price = Math.min(acc[groupName].selling_price, parseFloat(p.price_case_TSG) || Infinity);
      
      // Kiểm tra cờ nổi bật và mới
      if (!acc[groupName].is_featured && parseBoolean(p.is_featured || p.Featured)) acc[groupName].is_featured = true;
      if (!acc[groupName].is_new && parseBoolean(p.is_new || p.New)) acc[groupName].is_new = true;

      // Kiểm tra khuyến mãi
      if (!acc[groupName].hasPromotion && checkSkuHasActivePromotion(p.sku, allPromotions, allAppliedProducts)) {
        acc[groupName].hasPromotion = true;
      }
      return acc;
    }, {});

  let products = Object.values(groupedProducts);

  // Lọc theo danh mục (hỗ trợ đa cấp)
  if (category) {
    const targetCategoryIds = [category, ...getAllDescendantIds(category, allCategories)];
    products = products.filter(p => targetCategoryIds.includes(p.category_id));
  }

  // Lọc theo cờ
  if (parseBoolean(featured)) products = products.filter(p => p.is_featured);
  if (parseBoolean(isNew)) products = products.filter(p => p.is_new);

  // Lọc theo từ khóa tìm kiếm
  if (query) {
    const kw = removeVietnameseDiacritics(query.trim());
    products = products.filter(p => removeVietnameseDiacritics(p.product_name).includes(kw));
  }
  
  // Sắp xếp
  if (sort === 'price_asc') products.sort((a, b) => a.selling_price - b.selling_price);
  else if (sort === 'price_desc') products.sort((a, b) => b.selling_price - a.selling_price);
  else if (sort === 'name_asc') products.sort((a, b) => a.product_name.localeCompare(b.product_name));
  else if (sort === 'name_desc') products.sort((a, b) => b.product_name.localeCompare(a.product_name));

  // Phân trang
  const total = products.length;
  const totalPages = Math.ceil(total / pageSize);
  const from = (page - 1) * pageSize;
  const items = products.slice(from, from + pageSize);

  const response = {
    success: true,
    data: items,
    meta: {
      pagination: {
        page: page,
        pageSize: pageSize,
        totalItems: total,
        totalPages: totalPages
      }
    }
  };

  // Lưu vào cache
  CacheManager.set(cacheKey, response);

  return response;
}

/**
 * Lấy chi tiết một sản phẩm, bao gồm các biến thể và khuyến mãi.
 * @param {Object} params - Chứa product_name.
 * @returns {Object} - Dữ liệu trả về chuẩn hóa.
 */
function getProductDetail(params) {
  const { product_name } = params;
  if (!product_name) {
    throw { code: 400, message: "Tham số 'product_name' là bắt buộc." };
  }
  
  const cacheKey = `product_detail_${product_name.replace(/\s/g, '_')}`;
  const cachedData = CacheManager.get(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // Tải tất cả dữ liệu cần thiết
  const data = {
    productsData: getSheetData(CONFIG.SHEETS.PRODUCTS),
    categoriesData: getSheetData(CONFIG.SHEETS.CATEGORIES),
    promotionsData: getSheetData(CONFIG.SHEETS.PROMOTIONS),
    appliedProductsData: getSheetData(CONFIG.SHEETS.APPLIED_PRODUCTS),
    promotionRulesData: getSheetData(CONFIG.SHEETS.PROMOTION_RULES)
  };

  const productVariants = data.productsData.filter(p => p.product_name === product_name && parseBoolean(p.is_active));
  if (productVariants.length === 0) {
    throw { code: 404, message: `Không tìm thấy sản phẩm với tên: ${product_name}` };
  }
  
  const firstVariant = productVariants[0];
  const category = data.categoriesData.find(c => c.CategoryID === firstVariant.category_id);
  const parentCategory = category ? data.categoriesData.find(c => c.CategoryID === category.DanhMucChaID) : null;

  // Tính giá min/max
  const prices = productVariants.map(p => parseFloat(p.price_case_TSG) || Infinity);
  const minSellPrice = Math.min(...prices);

  const detail = {
    product_name: firstVariant.product_name,
    description: firstVariant.description,
    selling_price_min: (minSellPrice !== Infinity ? minSellPrice : null),
    category: {
      id: category ? category.CategoryID : null,
      name: category ? category.TenDanhMuc : null,
      parent: parentCategory ? { id: parentCategory.CategoryID, name: parentCategory.TenDanhMuc } : null
    },
    variants: productVariants.map(p => ({
      sku: p.sku,
      name_product: p.name_product,
      images: (p.image_url || '').split(',').map(url => url.trim()).filter(url => url),
      weight: p.Weight,
      volume: p.Volume,
      height: p.Height_mm,
      diameter: p.Diameter_mm,
      price_case_TSG: parseFloat(p.price_case_TSG) || null,
      promotions: getPromotionsForSku(p.sku, data)
    }))
  };

  const response = { success: true, data: detail };
  CacheManager.set(cacheKey, response);
  return response;
}

/**
 * MODULE NÂNG CAO: Dự báo tồn kho
 * Phân tích lịch sử bán hàng để đưa ra dự báo.
 * @param {Object} params - Có thể chứa `daysToPredict` (số ngày cần dự báo).
 * @returns {Object} - Dữ liệu dự báo.
 */
function predictInventory(params) {
  const daysToPredict = parseInt(params.daysToPredict) || 30;
  const historyDays = 90; // Phân tích dữ liệu trong 90 ngày gần nhất

  const products = getSheetData(CONFIG.SHEETS.PRODUCTS);
  const orderDetails = getSheetData(CONFIG.SHEETS.ORDER_DETAILS);
  const orders = getSheetData(CONFIG.SHEETS.ORDERS);

  // Lọc các đơn hàng trong khoảng thời gian phân tích
  const historyStartDate = new Date();
  historyStartDate.setDate(historyStartDate.getDate() - historyDays);

  const recentOrderIds = new Set(
    orders
      .filter(o => new Date(o.createdAt) >= historyStartDate)
      .map(o => o.orderId)
  );

  const recentOrderDetails = orderDetails.filter(od => recentOrderIds.has(od.orderId));

  // Tính toán doanh số bán hàng cho mỗi SKU
  const salesData = recentOrderDetails.reduce((acc, detail) => {
    const sku = detail.sku;
    const quantity = parseInt(detail.quantity) || 0;
    if (!acc[sku]) {
      acc[sku] = { totalSold: 0 };
    }
    acc[sku].totalSold += quantity;
    return acc;
  }, {});

  const predictions = products.map(p => {
    const sales = salesData[p.sku] || { totalSold: 0 };
    const dailySalesVelocity = sales.totalSold / historyDays;
    const currentStock = parseInt(p.TonKho) || 0;
    
    let daysUntilStockout = 'N/A';
    if (dailySalesVelocity > 0) {
      daysUntilStockout = Math.floor(currentStock / dailySalesVelocity);
    }

    const predictedDemand = Math.ceil(dailySalesVelocity * daysToPredict);
    const stockStatus = currentStock - predictedDemand;

    let recommendation = "Tồn kho ổn định.";
    if (daysUntilStockout !== 'N/A' && daysUntilStockout < 15) {
      recommendation = "RỦI RO CAO: Cần nhập hàng ngay!";
    } else if (daysUntilStockout !== 'N/A' && daysUntilStockout < 45) {
      recommendation = "Cảnh báo: Nên xem xét nhập hàng.";
    } else if (dailySalesVelocity === 0 && currentStock > 50) {
        recommendation = "Hàng tồn kho bán chậm, cân nhắc khuyến mãi.";
    }

    return {
      sku: p.sku,
      product_name: p.product_name,
      name_product: p.name_product,
      currentStock: currentStock,
      dailySalesVelocity: parseFloat(dailySalesVelocity.toFixed(2)),
      daysUntilStockout: daysUntilStockout,
      predictedDemandNext30Days: predictedDemand,
      projectedStockAfter30Days: stockStatus,
      recommendation: recommendation
    };
  });

  // Sắp xếp ưu tiên các sản phẩm cần chú ý nhất
  predictions.sort((a, b) => {
      const aVal = a.daysUntilStockout === 'N/A' ? 9999 : a.daysUntilStockout;
      const bVal = b.daysUntilStockout === 'N/A' ? 9999 : b.daysUntilStockout;
      return aVal - bVal;
  });

  return {
    success: true,
    data: predictions,
    meta: {
      predictionPeriodDays: daysToPredict,
      analysisPeriodDays: historyDays
    }
  };
}


// ===================================================================================
// --- V. CÁC HÀM HỖ TRỢ (HELPER FUNCTIONS) ---
// ===================================================================================

/**
 * Lấy dữ liệu từ một sheet và cache lại.
 * @param {string} sheetName - Tên của trang tính.
 * @returns {Array<Object>} - Mảng các đối tượng.
 */
function getSheetData(sheetName) {
  const cacheKey = `SHEET_${sheetName}`;
  let data = CacheManager.get(cacheKey);
  if (data) {
    return data;
  }

  try {
    const sheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID).getSheetByName(sheetName);
    if (!sheet) throw new Error(`Sheet "${sheetName}" không tồn tại.`);

    const values = sheet.getDataRange().getValues();
    if (!values || values.length < 2) return [];
    
    const headers = values[0].map(h => h ? h.toString().trim().replace(/\s+/g, '_') : ''); // Chuẩn hóa header
    data = values.slice(1).map(row => {
      const obj = {};
      headers.forEach((header, i) => { 
          if(header) {
              obj[header] = row[i] !== undefined ? row[i] : ""; 
          }
      });
      return obj;
    });

    CacheManager.set(cacheKey, data);
    return data;
  } catch (e) {
    logActivity('SHEET_ERROR', 'system', `Failed to read sheet: ${sheetName}`, e.toString());
    throw new Error(`Không thể tải dữ liệu từ sheet '${sheetName}'.`);
  }
}

/**
 * Tạo phản hồi JSON chuẩn.
 * @param {Object} data - Dữ liệu cần trả về.
 * @param {number} status - Mã trạng thái HTTP (mặc định 200).
 * @returns {ContentService.TextOutput}
 */
function createJsonResponse(data, status = 200) {
  // Mặc dù không thể set status code trực tiếp, việc có tham số này giúp logic rõ ràng hơn.
  return ContentService.createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Chuyển đổi giá trị sang boolean.
 * @param {*} val - Giá trị đầu vào.
 * @returns {boolean}
 */
function parseBoolean(val) {
  if (typeof val === 'boolean') return val;
  if (typeof val === 'string') return val.trim().toLowerCase() === 'true';
  return !!val;
}

/**
 * Xóa dấu tiếng Việt khỏi chuỗi.
 * @param {string} str - Chuỗi đầu vào.
 * @returns {string}
 */
function removeVietnameseDiacritics(str) {
  if (!str) return '';
  return str.toString().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/đ/g, 'd').replace(/Đ/g, 'D').toLowerCase();
}

/**
 * Đệ quy tìm tất cả ID của danh mục con cháu.
 * @param {string} parentId - ID của danh mục cha.
 * @param {Array<Object>} allCategories - Mảng tất cả danh mục.
 * @returns {Array<string>}
 */
function getAllDescendantIds(parentId, allCategories) {
  let descendantIds = [];
  const children = allCategories.filter(cat => cat.DanhMucChaID === parentId);
  
  for (const child of children) {
    descendantIds.push(child.CategoryID);
    descendantIds = descendantIds.concat(getAllDescendantIds(child.CategoryID, allCategories));
  }
  
  return descendantIds;
}

/**
 * Xây dựng cấu trúc cây từ danh sách danh mục phẳng.
 * @param {Array<Object>} categories - Danh sách các danh mục.
 * @returns {Array<Object>} - Cấu trúc cây danh mục.
 */
function buildCategoryTree(categories, parentId = null) {
    const tree = [];
    const directChildren = categories.filter(cat => (cat.DanhMucChaID || null) === parentId);

    for (const child of directChildren) {
        const children = buildCategoryTree(categories, child.CategoryID);
        const node = { ...child };
        if (children.length > 0) {
            node.children = children;
        }
        tree.push(node);
    }
    return tree;
}

/**
 * Lấy danh sách khuyến mãi cho một SKU cụ thể.
 * @param {string} sku - Mã SKU.
 * @param {Object} data - Dữ liệu từ các sheet.
 * @returns {Array<Object>}
 */
function getPromotionsForSku(sku, data) {
  const appliedPromos = data.appliedProductsData.filter(ap => ap.sku === sku);
  const today = new Date();
  
  return appliedPromos.map(ap => {
    const promotion = data.promotionsData.find(p => p.promo_id === ap.promo_id);
    if (!promotion || !parseBoolean(promotion.is_active)) return null;
    
    // Chuẩn hóa ngày tháng (giả sử định dạng là dd/MM/yyyy)
    const parseDate = (dateString) => {
        if (!dateString) return null;
        const parts = dateString.split('/');
        if (parts.length !== 3) return null;
        return new Date(parts[2], parts[1] - 1, parts[0]);
    };

    const startDate = parseDate(promotion.start_date);
    const endDate = parseDate(promotion.end_date);
    
    if (!startDate || !endDate || today < startDate || today > endDate) return null;
    
    const rules = data.promotionRulesData.filter(r => r.promo_id === promotion.promo_id);
    return {
      id: promotion.promo_id,
      name: promotion.promo_name,
      description: promotion.description,
      rules: rules.map(rule => formatPromotionRule(rule, data.productsData))
    };
  }).filter(p => p !== null);
}

/**
 * Kiểm tra xem một SKU có khuyến mãi đang hoạt động hay không.
 * @param {string} sku 
 * @param {Array} promotionsData 
 * @param {Array} appliedProductsData 
 * @returns {boolean}
 */
function checkSkuHasActivePromotion(sku, promotionsData, appliedProductsData) {
    const appliedPromo = appliedProductsData.find(ap => ap.sku === sku);
    if (!appliedPromo) return false;

    const promotion = promotionsData.find(p => p.promo_id === appliedPromo.promo_id);
    if (!promotion || !parseBoolean(promotion.is_active)) return false;

    const parseDate = (dateString) => {
        if (!dateString) return null;
        const parts = dateString.split('/');
        if (parts.length !== 3) return null;
        return new Date(parts[2], parts[1] - 1, parts[0]);
    };
    
    const today = new Date();
    const startDate = parseDate(promotion.start_date);
    const endDate = parseDate(promotion.end_date);

    return startDate && endDate && today >= startDate && today <= endDate;
}

/**
 * Định dạng mô tả cho một quy tắc khuyến mãi.
 * @param {Object} rule - Đối tượng quy tắc.
 * @param {Array<Object>} productsData - Dữ liệu sản phẩm.
 * @returns {string}
 */
function formatPromotionRule(rule, productsData) {
  switch (rule.rule_type) {
    case 'BUY_X_GET_Y':
      if (rule.discount_amount > 0) return `Mua ${rule.required_qty} ${rule.unit}: Giảm trực tiếp ${Number(rule.discount_amount).toLocaleString('vi-VN')}đ.`;
      if (rule.discount_percent > 0) return `Mua ${rule.required_qty} ${rule.unit}: Giảm ${rule.discount_percent}%.`;
      if (rule.free_sku && rule.free_qty > 0) {
        const freeProduct = productsData.find(p => p.sku === rule.free_sku);
        const freeProductName = freeProduct ? (freeProduct.name_product || freeProduct.product_name) : 'sản phẩm tặng';
        return `Mua ${rule.required_qty} ${rule.unit}: Tặng ${rule.free_qty} ${rule.free_unit} ${freeProductName}.`;
      }
      return 'Khuyến mãi mua X tặng Y.';
    case 'SPECIAL_PRICE_ON_QUANTITY':
      if (rule.special_price > 0) return `Mua từ ${rule.required_qty} ${rule.unit}: Giá đặc biệt chỉ ${Number(rule.special_price).toLocaleString('vi-VN')}đ/${rule.unit}.`;
      return 'Khuyến mãi giá đặc biệt.';
    default:
      return 'Chương trình khuyến mãi khác.';
  }
}

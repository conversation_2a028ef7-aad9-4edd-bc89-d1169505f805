# 🛒 SHOPPING CART INTEGRATION - STEP 1
## Lotus Glass Phase 1 Implementation

### 📋 WHAT WE'RE DOING
Integrating shopping cart functionality vào existing Lotus Glass Blogspot template để customers có thể:
- ✅ Add products to cart
- ✅ View cart with items
- ✅ Update quantities  
- ✅ Remove items
- ✅ Proceed to checkout

---

## 🔧 INTEGRATION INSTRUCTIONS

### STEP 1: Backup Current Template
1. Đi tới Blogger Dashboard → lotusglassvietnam.blogspot.com
2. Theme → Edit HTML  
3. **Download** template hiện tại làm backup
4. Save file name: `lotus-glass-backup-[date].xml`

### STEP 2: Add Shopping Cart CSS
**Vị trí**: Thêm vào section `<b:skin><![CDATA[` trước `]]></b:skin>`

```css
/* === SHOPPING CART STYLES === */
/* Cart Badge */
.cart-badge {
  position: relative;
  cursor: pointer;
  padding: 8px;
  color: var(--brand-color);
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-badge:hover {
  color: var(--brand-color-dark);
}

.cart-badge svg {
  width: 24px;
  height: 24px;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  transform: scale(0);
  transition: transform 0.2s ease;
}

.cart-count.has-items {
  transform: scale(1);
}

/* Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  max-width: 100vw;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 20px rgba(0,0,0,0.15);
  z-index: 1000;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.active {
  right: 0;
}

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

body.cart-open {
  overflow: hidden;
}

/* Cart Header */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: white;
}

.cart-header h3 {
  margin: 0;
  color: var(--brand-color);
  font-size: 18px;
}

.cart-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 5px;
  line-height: 1;
  border-radius: 4px;
}

.cart-close:hover {
  color: #333;
  background: #f5f5f5;
}

/* Cart Items */
.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.cart-empty {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.cart-empty svg {
  width: 64px;
  height: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.cart-item {
  display: flex;
  gap: 12px;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s ease;
}

.cart-item:hover {
  background: #fafafa;
}

.item-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  color: #333;
}

.item-variant {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
}

.item-price {
  margin: 0;
  font-size: 14px;
  color: var(--brand-color);
  font-weight: 500;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 2px;
}

.qty-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: var(--brand-color);
  transition: all 0.2s ease;
}

.qty-btn:hover {
  background: var(--brand-color);
  color: white;
}

.qty-input {
  width: 35px;
  height: 28px;
  text-align: center;
  border: none;
  background: transparent;
  font-size: 13px;
  font-weight: 600;
}

.item-total {
  font-size: 14px;
  font-weight: 600;
  color: var(--brand-color);
}

.remove-item {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.remove-item:hover {
  color: #dc3545;
  background: #fff5f5;
}

/* Cart Footer */
.cart-footer {
  border-top: 1px solid #eee;
  padding: 20px;
  background: white;
}

.cart-totals {
  margin-bottom: 20px;
}

.subtotal {
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
  color: var(--brand-color);
  margin-bottom: 8px;
}

.shipping-note {
  font-size: 12px;
  color: #666;
  font-style: italic;
  text-align: center;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.cart-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  display: block;
}

.cart-btn-primary {
  background: var(--brand-color);
  color: white;
}

.cart-btn-primary:hover:not(:disabled) {
  background: var(--brand-color-dark);
  transform: translateY(-1px);
}

.cart-btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.cart-btn-secondary {
  background: white;
  color: var(--brand-color);
  border: 1px solid var(--brand-color);
}

.cart-btn-secondary:hover {
  background: var(--brand-color);
  color: white;
}

/* Add to Cart Buttons */
.add-to-cart-btn {
  background: var(--brand-color);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
  width: 100%;
}

.add-to-cart-btn:hover {
  background: var(--brand-color-dark);
  transform: translateY(-1px);
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

/* Cart Notification */
.cart-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--brand-color);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  max-width: 320px;
}

.cart-notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-content svg {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
    right: -100vw;
  }
  
  .cart-sidebar.active {
    right: 0;
  }
  
  .cart-header {
    padding: 15px;
  }
  
  .cart-item {
    padding: 12px 15px;
  }
  
  .cart-footer {
    padding: 15px;
  }
}
```

### STEP 3: Add Cart Badge to Header
**Vị trí**: Tìm dòng `<nav class="custom-nav">` và thay thế bằng:

```html
<nav class="custom-nav">
  <a href="/" class="nav-link">Trang chủ</a>
  <a href="/p/san-pham.html" class="nav-link">Sản phẩm</a>
  <a href="/p/gioi-thieu.html" class="nav-link">Giới thiệu</a>
  <a href="/p/tin-tuc.html" class="nav-link">Tin tức</a>
  
  <!-- Shopping Cart Badge -->
  <div id="cart-badge" class="cart-badge">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
    </svg>
    <span id="cart-count" class="cart-count">0</span>
  </div>
</nav>
```

### STEP 4: Add Cart Sidebar HTML
**Vị trí**: Thêm vào cuối file, trước `</body>`

```html
<!-- Shopping Cart Sidebar -->
<div id="cart-sidebar" class="cart-sidebar">
  <div class="cart-header">
    <h3>Giỏ hàng của bạn</h3>
    <button id="cart-close" class="cart-close">&times;</button>
  </div>
  
  <div id="cart-items" class="cart-items">
    <div class="cart-empty">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
      </svg>
      <p>Giỏ hàng trống</p>
      <p>Hãy thêm sản phẩm để tiếp tục mua sắm</p>
    </div>
  </div>
  
  <div class="cart-footer">
    <div class="cart-totals">
      <div class="subtotal">
        <span>Tạm tính:</span>
        <span id="cart-subtotal">0₫</span>
      </div>
      <div class="shipping-note">
        * Phí vận chuyển sẽ được tính khi thanh toán
      </div>
    </div>
    
    <div class="cart-actions">
      <button id="continue-shopping" class="cart-btn cart-btn-secondary">
        Tiếp tục mua sắm
      </button>
      <button id="proceed-checkout" class="cart-btn cart-btn-primary" disabled>
        Thanh toán
      </button>
    </div>
  </div>
</div>

<div id="cart-overlay" class="cart-overlay"></div>
```

---

## ✅ TESTING STEP 1

Sau khi add CSS và HTML:

1. **Save** template
2. **Preview** website
3. **Check cart badge** xuất hiện trong header
4. **Click cart badge** → cart sidebar sẽ mở
5. **Click overlay hoặc X** → cart sidebar sẽ đóng

**Expected Result**: Cart UI hoạt động nhưng chưa có products và functionality.

---

## 🎯 NEXT STEPS

Sau khi hoàn thành Step 1, chúng ta sẽ tiếp tục:

**Step 2**: Add Shopping Cart JavaScript functionality
**Step 3**: Add "Add to Cart" buttons to product cards  
**Step 4**: Integrate with existing product data
**Step 5**: Test complete cart operations

---

## 🆘 SUPPORT

Nếu gặp issues:
1. **Check browser console** for errors
2. **Verify CSS/HTML** syntax  
3. **Test on mobile** devices
4. **Clear browser cache** và refresh

**Ready for Step 2?** Báo cho tôi kết quả testing Step 1!
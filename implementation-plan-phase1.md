# Implementation Plan - Phase 1: Complete E-commerce Core

## 🎯 Phase 1 Overview (4-6 tuần)

**<PERSON><PERSON><PERSON> tiêu**: <PERSON><PERSON><PERSON>n Lotus Glass từ product catalog thành full e-commerce platform với shopping cart, checkout, và order management.

## 📋 Detailed Task Breakdown

### WEEK 1-2: Shopping Cart Development

#### Task 1.1: Frontend Shopping Cart (Blogspot Template)
```javascript
// File: cart-functionality.js (thêm vào blogthemen.xml)

Features cần implement:
├── Cart sidebar/modal UI
├── Add to cart button cho mỗi product
├── Quantity selector
├── Remove item functionality  
├── Cart total calculation
├── Apply coupon/promotion
├── Persistent cart (localStorage)
├── Cart badge notification
└── Mobile-optimized cart view
```

**Technical Details:**
```javascript
// Cart object structure
const cartItem = {
  sku: "VTC099BNTRON3.8LHT",
  productName: "Bình Ngâm",
  variant: "3.8 lít SVQT",
  quantity: 2,
  unitPrice: 380000,
  totalPrice: 760000,
  thumbnail: "https://...",
  promotions: []
};

// Cart methods
Cart.add(sku, quantity)
Cart.remove(sku)
Cart.updateQuantity(sku, newQuantity)
Cart.getTotal()
Cart.applyPromotion(code)
Cart.clear()
```

#### Task 1.2: Backend Cart API (Google Apps Script)
```javascript
// File: cart-api.gs (mở rộng code.gs hiện tại)

New API endpoints:
├── POST /validateCart - Validate cart items & prices
├── POST /calculateShipping - Shipping cost calculation
├── POST /applyPromotion - Apply promotion code
├── GET /getPromotions - Get available promotions
└── POST /reserveStock - Reserve items for checkout
```

**API Implementation:**
```javascript
// Thêm vào doGet/doPost functions
function doPost(e) {
  const action = e.parameter.action;
  
  switch(action) {
    case 'validateCart':
      return validateCartItems(e.parameter.cartData);
    case 'calculateShipping':
      return calculateShippingCost(e.parameter);
    case 'applyPromotion':
      return applyPromotionCode(e.parameter);
    case 'createOrder':
      return createNewOrder(e.parameter);
    default:
      return errorResponse('Invalid action');
  }
}
```

### WEEK 2-3: Checkout Process

#### Task 2.1: Checkout Form UI
```html
<!-- Checkout form layout -->
<div id="checkoutModal" class="checkout-modal">
  <form id="checkoutForm">
    <!-- Customer Information -->
    <section class="customer-info">
      <h3>Thông tin khách hàng</h3>
      <input type="text" name="customerName" placeholder="Họ và tên" required>
      <input type="tel" name="phoneNumber" placeholder="Số điện thoại" required>
      <input type="email" name="email" placeholder="Email">
    </section>
    
    <!-- Shipping Address -->
    <section class="shipping-address">
      <h3>Địa chỉ giao hàng</h3>
      <select name="province" required>
        <option value="">Chọn Tỉnh/Thành phố</option>
      </select>
      <select name="district" required>
        <option value="">Chọn Quận/Huyện</option>
      </select>
      <input type="text" name="address" placeholder="Địa chỉ cụ thể" required>
    </section>
    
    <!-- Payment Method -->
    <section class="payment-method">
      <h3>Phương thức thanh toán</h3>
      <label><input type="radio" name="payment" value="vnpay"> VNPay</label>
      <label><input type="radio" name="payment" value="bank"> Chuyển khoản</label>
      <label><input type="radio" name="payment" value="cod"> COD</label>
    </section>
    
    <!-- Order Summary -->
    <section class="order-summary">
      <h3>Tóm tắt đơn hàng</h3>
      <div id="cartSummary"></div>
      <div class="total-section">
        <div class="subtotal">Tạm tính: <span id="subtotal"></span></div>
        <div class="shipping">Phí vận chuyển: <span id="shipping"></span></div>
        <div class="discount">Giảm giá: <span id="discount"></span></div>
        <div class="total">Tổng cộng: <span id="total"></span></div>
      </div>
    </section>
    
    <button type="submit" class="place-order-btn">Đặt hàng</button>
  </form>
</div>
```

#### Task 2.2: Form Validation & UX
```javascript
// Checkout validation logic
const CheckoutValidator = {
  validatePhone: (phone) => /^(\+84|0)[1-9]\d{8}$/.test(phone),
  validateEmail: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
  validateRequired: (value) => value && value.trim().length > 0,
  
  validateForm: (formData) => {
    const errors = [];
    
    if (!this.validateRequired(formData.customerName)) {
      errors.push('Vui lòng nhập họ tên');
    }
    
    if (!this.validatePhone(formData.phoneNumber)) {
      errors.push('Số điện thoại không hợp lệ');
    }
    
    if (formData.email && !this.validateEmail(formData.email)) {
      errors.push('Email không hợp lệ');
    }
    
    // More validations...
    return errors;
  }
};
```

### WEEK 3-4: Order Management System

#### Task 3.1: Order Creation API
```javascript
// order-management.gs
function createNewOrder(orderData) {
  try {
    // 1. Validate order data
    const validation = validateOrderData(orderData);
    if (!validation.isValid) {
      return errorResponse(validation.errors);
    }
    
    // 2. Generate order ID
    const orderId = generateOrderId();
    
    // 3. Calculate totals
    const totals = calculateOrderTotals(orderData.items, orderData.promotions);
    
    // 4. Create order record
    const order = {
      orderId: orderId,
      customerPhone: orderData.customer.phone,
      customerName: orderData.customer.name,
      orderDate: new Date(),
      status: 'pending_payment',
      items: orderData.items,
      subtotal: totals.subtotal,
      shipping: totals.shipping,
      discount: totals.discount,
      total: totals.total,
      paymentMethod: orderData.paymentMethod,
      shippingAddress: orderData.shippingAddress
    };
    
    // 5. Save to Orders sheet
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    ordersSheet.appendRow([
      order.orderId,
      order.customerPhone,
      order.customerName,
      order.orderDate,
      order.subtotal,
      order.shipping,
      order.discount,
      order.total,
      order.paymentMethod,
      order.status,
      JSON.stringify(order.shippingAddress)
    ]);
    
    // 6. Save order details
    saveOrderDetails(orderId, orderData.items);
    
    // 7. Update customer record
    updateCustomerRecord(orderData.customer);
    
    // 8. Update inventory
    updateInventory(orderData.items);
    
    // 9. Send confirmation email
    sendOrderConfirmationEmail(order);
    
    return successResponse({
      orderId: orderId,
      status: 'created',
      paymentUrl: generatePaymentUrl(order)
    });
    
  } catch (error) {
    Logger.log('Create order error:', error);
    return errorResponse('Có lỗi xảy ra khi tạo đơn hàng');
  }
}
```

#### Task 3.2: Order Tracking & Status Updates
```javascript
// order-tracking.gs
function getOrderStatus(orderId) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    
    // Find order
    const orderRow = data.find(row => row[0] === orderId);
    if (!orderRow) {
      return errorResponse('Không tìm thấy đơn hàng');
    }
    
    // Get order details
    const orderDetails = getOrderDetails(orderId);
    
    return successResponse({
      orderId: orderRow[0],
      customerName: orderRow[2],
      orderDate: orderRow[3],
      status: orderRow[9],
      total: orderRow[7],
      items: orderDetails,
      timeline: getOrderTimeline(orderId)
    });
    
  } catch (error) {
    return errorResponse('Lỗi khi lấy thông tin đơn hàng');
  }
}

function updateOrderStatus(orderId, newStatus, note = '') {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    
    // Find and update order
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        ordersSheet.getRange(i + 1, 10).setValue(newStatus); // Status column
        
        // Log status change
        logOrderStatusChange(orderId, newStatus, note);
        
        // Send notification email
        sendStatusUpdateEmail(orderId, newStatus);
        
        return successResponse('Cập nhật trạng thái thành công');
      }
    }
    
    return errorResponse('Không tìm thấy đơn hàng');
    
  } catch (error) {
    return errorResponse('Lỗi khi cập nhật trạng thái');
  }
}
```

### WEEK 4: Payment Integration

#### Task 4.1: VNPay Integration
```javascript
// vnpay-integration.gs
function generateVNPayURL(orderData) {
  const vnp_TmnCode = "YOUR_TMN_CODE";
  const vnp_HashSecret = "YOUR_HASH_SECRET";
  const vnp_Url = "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html";
  const vnp_ReturnUrl = "https://lotusglassvietnam.blogspot.com/payment-result";
  
  const vnp_Params = {
    'vnp_Version': '2.1.0',
    'vnp_Command': 'pay',
    'vnp_TmnCode': vnp_TmnCode,
    'vnp_Locale': 'vn',
    'vnp_CurrCode': 'VND',
    'vnp_TxnRef': orderData.orderId,
    'vnp_OrderInfo': `Thanh toan don hang ${orderData.orderId}`,
    'vnp_OrderType': 'other',
    'vnp_Amount': orderData.total * 100, // VNPay requires amount in VND minor unit
    'vnp_ReturnUrl': vnp_ReturnUrl,
    'vnp_IpAddr': '127.0.0.1',
    'vnp_CreateDate': formatDate(new Date(), 'yyyyMMddHHmmss')
  };
  
  // Sort parameters
  const sortedParams = Object.keys(vnp_Params).sort().reduce((result, key) => {
    result[key] = vnp_Params[key];
    return result;
  }, {});
  
  // Create signature
  const signData = Object.entries(sortedParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  const hmac = Utilities.computeHmacSha512Signature(signData, vnp_HashSecret);
  const vnp_SecureHash = hmac.map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2)).join('');
  
  // Build final URL
  const finalUrl = `${vnp_Url}?${signData}&vnp_SecureHash=${vnp_SecureHash}`;
  
  return finalUrl;
}

function handleVNPayCallback(params) {
  // Verify signature
  const isValidSignature = verifyVNPaySignature(params);
  
  if (!isValidSignature) {
    return errorResponse('Invalid signature');
  }
  
  const orderId = params.vnp_TxnRef;
  const responseCode = params.vnp_ResponseCode;
  
  if (responseCode === '00') {
    // Payment successful
    updateOrderStatus(orderId, 'paid', 'Thanh toán VNPay thành công');
    return successResponse('Payment successful');
  } else {
    // Payment failed
    updateOrderStatus(orderId, 'payment_failed', `VNPay error: ${responseCode}`);
    return errorResponse('Payment failed');
  }
}
```

#### Task 4.2: Bank Transfer & COD
```javascript
// payment-methods.gs
function processBankTransfer(orderData) {
  // Generate bank transfer instructions
  const instructions = {
    bankName: 'Vietcombank',
    accountNumber: '**********',
    accountName: 'CONG TY PHA LE VIET TIEP',
    amount: orderData.total,
    transferContent: `LOTUS ${orderData.orderId}`,
    qrCode: generateQRCode(orderData)
  };
  
  // Update order status
  updateOrderStatus(orderData.orderId, 'pending_payment', 'Chờ chuyển khoản');
  
  // Send bank transfer email
  sendBankTransferEmail(orderData, instructions);
  
  return successResponse(instructions);
}

function processCOD(orderData) {
  // Update order status
  updateOrderStatus(orderData.orderId, 'confirmed', 'COD - Chờ giao hàng');
  
  // Calculate COD fee
  const codFee = calculateCODFee(orderData.total);
  
  return successResponse({
    message: 'Đơn hàng COD đã được xác nhận',
    codFee: codFee,
    estimatedDelivery: calculateDeliveryDate(orderData.shippingAddress)
  });
}
```

### WEEK 5: Email System

#### Task 5.1: Email Templates
```javascript
// email-templates.gs
function getOrderConfirmationTemplate(orderData) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        .email-container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
        .header { background: #f37021; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .order-details { background: #f8f9fa; padding: 15px; margin: 20px 0; }
        .item-row { border-bottom: 1px solid #eee; padding: 10px 0; }
        .total-row { font-weight: bold; font-size: 18px; }
      </style>
    </head>
    <body>
      <div class="email-container">
        <div class="header">
          <h1>Xác nhận đơn hàng #${orderData.orderId}</h1>
        </div>
        <div class="content">
          <p>Xin chào ${orderData.customerName},</p>
          <p>Cảm ơn bạn đã đặt hàng tại Lotus Glass. Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.</p>
          
          <div class="order-details">
            <h3>Chi tiết đơn hàng:</h3>
            ${orderData.items.map(item => `
              <div class="item-row">
                <strong>${item.productName}</strong> - ${item.variant}<br>
                Số lượng: ${item.quantity} | Đơn giá: ${formatPrice(item.unitPrice)}<br>
                Thành tiền: ${formatPrice(item.totalPrice)}
              </div>
            `).join('')}
            
            <div class="item-row total-row">
              Tổng cộng: ${formatPrice(orderData.total)}
            </div>
          </div>
          
          <p><strong>Địa chỉ giao hàng:</strong><br>
          ${orderData.shippingAddress.address}<br>
          ${orderData.shippingAddress.district}, ${orderData.shippingAddress.province}</p>
          
          <p><strong>Phương thức thanh toán:</strong> ${getPaymentMethodName(orderData.paymentMethod)}</p>
          
          <p>Bạn có thể theo dõi trạng thái đơn hàng tại: 
          <a href="https://lotusglassvietnam.blogspot.com/order-tracking?id=${orderData.orderId}">
            Xem chi tiết đơn hàng
          </a></p>
          
          <p>Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ:</p>
          <p>📞 Hotline: 0981 500 400<br>
          📧 Email: <EMAIL></p>
          
          <p>Trân trọng,<br>
          Đội ngũ Lotus Glass</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
```

#### Task 5.2: Email Automation
```javascript
// email-automation.gs
function sendOrderConfirmationEmail(orderData) {
  try {
    const template = getOrderConfirmationTemplate(orderData);
    const subject = `Xác nhận đơn hàng #${orderData.orderId} - Lotus Glass`;
    
    GmailApp.sendEmail(
      orderData.customer.email,
      subject,
      '', // Plain text version
      {
        htmlBody: template,
        name: 'Lotus Glass',
        replyTo: '<EMAIL>'
      }
    );
    
    // Log email sent
    logEmailSent(orderData.orderId, 'order_confirmation', orderData.customer.email);
    
    return true;
  } catch (error) {
    Logger.log('Email send error:', error);
    return false;
  }
}

function sendStatusUpdateEmail(orderId, newStatus) {
  try {
    const orderData = getOrderById(orderId);
    if (!orderData || !orderData.customer.email) return false;
    
    const statusMessages = {
      'confirmed': 'Đơn hàng đã được xác nhận',
      'processing': 'Đơn hàng đang được xử lý',
      'shipped': 'Đơn hàng đã được giao cho đơn vị vận chuyển',
      'delivered': 'Đơn hàng đã được giao thành công',
      'cancelled': 'Đơn hàng đã bị hủy'
    };
    
    const template = getStatusUpdateTemplate(orderData, newStatus, statusMessages[newStatus]);
    const subject = `Cập nhật đơn hàng #${orderId} - ${statusMessages[newStatus]}`;
    
    GmailApp.sendEmail(
      orderData.customer.email,
      subject,
      '',
      { htmlBody: template, name: 'Lotus Glass' }
    );
    
    return true;
  } catch (error) {
    Logger.log('Status email error:', error);
    return false;
  }
}
```

### WEEK 6: Testing & Optimization

#### Task 6.1: End-to-End Testing
```javascript
// Test scenarios to cover:
const testScenarios = [
  {
    name: 'Complete purchase flow',
    steps: [
      'Browse products',
      'Add items to cart',
      'Update quantities',
      'Proceed to checkout',
      'Fill customer information',
      'Select payment method',
      'Place order',
      'Receive confirmation email',
      'Track order status'
    ]
  },
  {
    name: 'Payment method testing',
    variations: [
      'VNPay payment',
      'Bank transfer',
      'COD order'
    ]
  },
  {
    name: 'Error handling',
    scenarios: [
      'Invalid form data',
      'Out of stock items',
      'Payment failures',
      'Network errors'
    ]
  },
  {
    name: 'Mobile experience',
    devices: [
      'iPhone Safari',
      'Android Chrome',
      'iPad Safari'
    ]
  }
];
```

#### Task 6.2: Performance Optimization
```javascript
// Performance improvements:
const optimizations = [
  {
    area: 'Frontend',
    tasks: [
      'Minify CSS/JS',
      'Optimize images',
      'Implement lazy loading',
      'Cache API responses',
      'Reduce DOM manipulations'
    ]
  },
  {
    area: 'Backend',
    tasks: [
      'Optimize Sheets queries',
      'Implement caching',
      'Batch operations',
      'Error handling',
      'Rate limiting'
    ]
  },
  {
    area: 'User Experience',
    tasks: [
      'Loading indicators',
      'Error messages',
      'Success feedback',
      'Mobile optimization',
      'Accessibility'
    ]
  }
];
```

## 📊 Success Metrics

### Technical KPIs
- ✅ **Page Load Time**: < 3 seconds
- ✅ **API Response Time**: < 2 seconds  
- ✅ **Mobile Performance**: 90+ Google PageSpeed score
- ✅ **Error Rate**: < 1% of transactions
- ✅ **Uptime**: 99.9%

### Business KPIs
- 📈 **Conversion Rate**: 2-5% (từ 0% hiện tại)
- 💰 **Average Order Value**: $50+ USD
- 📱 **Mobile Orders**: 60%+ of total
- 📧 **Email Open Rate**: 25%+
- ⭐ **Customer Satisfaction**: 4.5/5 stars

## 🚀 Go-Live Checklist

### Pre-Launch (1 tuần trước)
- [ ] Complete all features development
- [ ] Pass all test scenarios
- [ ] Performance benchmarks met
- [ ] Email templates finalized
- [ ] Payment gateways tested
- [ ] Mobile experience verified
- [ ] Admin training completed
- [ ] Backup systems in place

### Launch Day
- [ ] Deploy to production
- [ ] Monitor system performance
- [ ] Test first transactions
- [ ] Verify email delivery
- [ ] Check mobile experience
- [ ] Monitor error logs
- [ ] Customer support ready

### Post-Launch (1 tuần sau)
- [ ] Daily performance monitoring
- [ ] Customer feedback collection
- [ ] Bug fixes & optimizations
- [ ] Analytics setup verification
- [ ] Sales data analysis
- [ ] Team feedback session
- [ ] Phase 2 planning kickoff

## 💼 Resource Requirements

### Development Team
- **1 Full-stack Developer** (JavaScript/GAS expert)
- **0.5 UI/UX Designer** (part-time cho refinements)
- **0.5 QA Tester** (part-time cho testing)

### External Services
- **VNPay Account** (business registration required)
- **Domain Name** (optional, $12/year)
- **SMS Service** (optional, for notifications)

### Time Investment
- **Development**: 120-150 hours total
- **Testing**: 30-40 hours
- **Documentation**: 10-15 hours
- **Training**: 5-10 hours

## 🎯 Success Criteria

Phase 1 sẽ được coi là thành công khi:

1. ✅ **Functional**: Khách hàng có thể đặt hàng từ A-Z
2. ✅ **Reliable**: Hệ thống hoạt động ổn định 24/7
3. ✅ **User-friendly**: Mobile experience mượt mà
4. ✅ **Automated**: Email & order processing tự động
5. ✅ **Scalable**: Có thể handle 100+ orders/day
6. ✅ **Profitable**: ROI positive từ tháng đầu tiên

**Recommended**: Bắt đầu triển khai ngay để có thể launch vào cuối tháng này và thu được revenue từ tháng tới!
/**
 * ============================================================================
 * LOTUS GLASS - PHASE 2 ORDER MANAGEMENT BACKEND
 * File: lotus-order-management.gs
 * 
 * Thêm vào existing code.gs để handle checkout & order operations
 * ============================================================================
 */

// ============================================================================
// ORDER MANAGEMENT CONFIGURATION
// ============================================================================

const ORDER_CONFIG = {
  // Order ID generation
  ORDER_ID_PREFIX: 'LG',
  ORDER_ID_LENGTH: 8,
  
  // Order statuses
  STATUS: {
    PENDING_PAYMENT: 'pending_payment',
    PAID: 'paid',
    CONFIRMED: 'confirmed', 
    PROCESSING: 'processing',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded'
  },
  
  // Payment methods
  PAYMENT_METHODS: {
    VNPAY: 'vnpay',
    BANK_TRANSFER: 'bank_transfer',
    COD: 'cod'
  },
  
  // Email settings
  EMAIL: {
    FROM_NAME: 'Lotus Glass Vietnam',
    FROM_EMAIL: '<EMAIL>',
    REPLY_TO: '<EMAIL>',
    BCC: '<EMAIL>'
  },
  
  // VNPay settings (sandbox - replace với production)
  VNPAY: {
    TMN_CODE: 'YOUR_TMN_CODE',
    HASH_SECRET: 'YOUR_HASH_SECRET',
    URL: 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
    RETURN_URL: 'https://lotusglassvietnam.blogspot.com/payment-result',
    VERSION: '2.1.0',
    COMMAND: 'pay',
    LOCALE: 'vn',
    CURRENCY: 'VND'
  },
  
  // Shipping settings
  SHIPPING: {
    FREE_THRESHOLD: 500000, // 500k VND
    DEFAULT_RATE: 50000,
    MAJOR_CITIES: {
      'Hồ Chí Minh': 30000,
      'Hà Nội': 35000,
      'Đà Nẵng': 40000,
      'Cần Thơ': 35000
    },
    DELIVERY_DAYS: {
      'Hồ Chí Minh': '1-2',
      'Hà Nội': '2-3',
      'Đà Nẵng': '2-3',
      'default': '3-5'
    }
  },
  
  // COD settings
  COD: {
    FEE_RATE: 0.02, // 2%
    MIN_FEE: 15000,
    MAX_FEE: 50000
  }
};

// ============================================================================
// ORDER CREATION FUNCTIONS
// ============================================================================

/**
 * Create new order from checkout data
 * @param {Object} orderData - Order information from checkout
 * @return {Object} Order creation result
 */
function createNewOrder(orderData) {
  try {
    Logger.log('Creating new order:', orderData);
    
    // 1. Validate order data
    const validation = validateOrderData(orderData);
    if (!validation.isValid) {
      return createErrorResponse(validation.errors.join(', '));
    }
    
    // 2. Generate unique order ID
    const orderId = generateOrderId();
    
    // 3. Calculate final totals
    const totals = calculateOrderTotals(orderData);
    
    // 4. Create order object
    const order = {
      orderId: orderId,
      
      // Customer information
      customerName: orderData.customer.name,
      customerPhone: orderData.customer.phone,
      customerEmail: orderData.customer.email,
      
      // Company information (optional)
      companyName: orderData.company?.name || '',
      taxId: orderData.company?.taxId || '',
      companyAddress: orderData.company?.address || '',
      
      // Shipping address
      shippingProvince: orderData.shippingAddress.province,
      shippingDistrict: orderData.shippingAddress.district,
      shippingAddress: orderData.shippingAddress.address,
      
      // Order details
      orderDate: new Date(),
      items: orderData.items,
      itemCount: orderData.items.length,
      
      // Financial details
      subtotal: totals.subtotal,
      shippingCost: totals.shipping,
      discountAmount: totals.discount,
      total: totals.total,
      
      // Payment information
      paymentMethod: orderData.paymentMethod,
      paymentStatus: ORDER_CONFIG.PAYMENT_METHODS.COD === orderData.paymentMethod ? 'pending' : 'pending',
      
      // Order status
      status: ORDER_CONFIG.STATUS.PENDING_PAYMENT,
      
      // Additional information
      notes: orderData.notes || '',
      appliedPromotion: orderData.appliedPromotion ? JSON.stringify(orderData.appliedPromotion) : '',
      
      // Metadata
      source: 'website',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // 5. Save to Orders sheet
    const orderRowResult = saveOrderToSheet(order);
    if (!orderRowResult.success) {
      throw new Error('Failed to save order: ' + orderRowResult.error);
    }
    
    // 6. Save order details
    const detailsResult = saveOrderDetails(orderId, orderData.items);
    if (!detailsResult.success) {
      throw new Error('Failed to save order details: ' + detailsResult.error);
    }
    
    // 7. Update customer record
    updateCustomerRecord(orderData.customer, order);
    
    // 8. Update inventory if needed
    // updateInventory(orderData.items);
    
    // 9. Send confirmation email
    if (orderData.customer.email) {
      sendOrderConfirmationEmail(order);
    }
    
    // 10. Generate payment URL if needed
    let paymentUrl = null;
    if (orderData.paymentMethod === ORDER_CONFIG.PAYMENT_METHODS.VNPAY) {
      paymentUrl = generateVNPayURL(order);
    } else if (orderData.paymentMethod === ORDER_CONFIG.PAYMENT_METHODS.BANK_TRANSFER) {
      // Bank transfer instructions will be in the email
    }
    
    // 11. Update order status based on payment method
    if (orderData.paymentMethod === ORDER_CONFIG.PAYMENT_METHODS.COD) {
      updateOrderStatus(orderId, ORDER_CONFIG.STATUS.CONFIRMED, 'COD order confirmed automatically');
    }
    
    Logger.log('Order created successfully:', orderId);
    
    return createSuccessResponse({
      orderId: orderId,
      status: 'created',
      paymentUrl: paymentUrl,
      total: totals.total,
      paymentMethod: orderData.paymentMethod,
      estimatedDelivery: getEstimatedDeliveryDate(orderData.shippingAddress.province),
      message: 'Đơn hàng đã được tạo thành công'
    });
    
  } catch (error) {
    Logger.log('Create order error:', error);
    return createErrorResponse('Có lỗi xảy ra khi tạo đơn hàng: ' + error.message);
  }
}

/**
 * Validate order data before creation
 * @param {Object} orderData - Order data to validate
 * @return {Object} Validation result
 */
function validateOrderData(orderData) {
  const errors = [];
  
  // Check required customer information
  if (!orderData.customer?.name) {
    errors.push('Thiếu tên khách hàng');
  }
  
  if (!orderData.customer?.phone) {
    errors.push('Thiếu số điện thoại khách hàng');
  } else if (!/^(\+84|0)[1-9]\d{8}$/.test(orderData.customer.phone)) {
    errors.push('Số điện thoại không hợp lệ');
  }
  
  // Check shipping address
  if (!orderData.shippingAddress?.province) {
    errors.push('Thiếu tỉnh/thành phố giao hàng');
  }
  
  if (!orderData.shippingAddress?.district) {
    errors.push('Thiếu quận/huyện giao hàng');
  }
  
  if (!orderData.shippingAddress?.address) {
    errors.push('Thiếu địa chỉ giao hàng cụ thể');
  }
  
  // Check payment method
  const validPaymentMethods = Object.values(ORDER_CONFIG.PAYMENT_METHODS);
  if (!validPaymentMethods.includes(orderData.paymentMethod)) {
    errors.push('Phương thức thanh toán không hợp lệ');
  }
  
  // Check items
  if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
    errors.push('Đơn hàng không có sản phẩm');
  } else {
    orderData.items.forEach((item, index) => {
      if (!item.sku) {
        errors.push(`Sản phẩm ${index + 1}: Thiếu mã SKU`);
      }
      if (!item.productName) {
        errors.push(`Sản phẩm ${index + 1}: Thiếu tên sản phẩm`);
      }
      if (!item.price || item.price <= 0) {
        errors.push(`Sản phẩm ${index + 1}: Giá không hợp lệ`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Sản phẩm ${index + 1}: Số lượng không hợp lệ`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * Calculate order totals including shipping and discounts
 * @param {Object} orderData - Order data
 * @return {Object} Calculated totals
 */
function calculateOrderTotals(orderData) {
  const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  
  // Calculate shipping
  let shipping = 0;
  if (subtotal < ORDER_CONFIG.SHIPPING.FREE_THRESHOLD) {
    const province = orderData.shippingAddress.province;
    shipping = ORDER_CONFIG.SHIPPING.MAJOR_CITIES[province] || ORDER_CONFIG.SHIPPING.DEFAULT_RATE;
  }
  
  // Add COD fee if applicable
  if (orderData.paymentMethod === ORDER_CONFIG.PAYMENT_METHODS.COD) {
    const codFee = Math.min(
      Math.max(subtotal * ORDER_CONFIG.COD.FEE_RATE, ORDER_CONFIG.COD.MIN_FEE),
      ORDER_CONFIG.COD.MAX_FEE
    );
    shipping += codFee;
  }
  
  // Calculate discount
  const discount = orderData.discountAmount || 0;
  
  // Calculate total
  const total = subtotal + shipping - discount;
  
  return {
    subtotal: subtotal,
    shipping: shipping,
    discount: discount,
    total: total
  };
}

/**
 * Generate unique order ID
 * @return {string} Generated order ID
 */
function generateOrderId() {
  const timestamp = new Date().getTime().toString().slice(-6); // Last 6 digits
  const random = Math.random().toString(36).substr(2, 2).toUpperCase(); // 2 random chars
  return ORDER_CONFIG.ORDER_ID_PREFIX + timestamp + random;
}

/**
 * Get estimated delivery date based on province
 * @param {string} province - Shipping province
 * @return {string} Estimated delivery description
 */
function getEstimatedDeliveryDate(province) {
  const days = ORDER_CONFIG.SHIPPING.DELIVERY_DAYS[province] || ORDER_CONFIG.SHIPPING.DELIVERY_DAYS.default;
  return `${days} ngày làm việc`;
}

// ============================================================================
// DATABASE OPERATIONS
// ============================================================================

/**
 * Save order to Orders sheet
 * @param {Object} order - Order object
 * @return {Object} Save result
 */
function saveOrderToSheet(order) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    
    // Prepare row data matching the sheet structure
    const rowData = [
      order.orderId,                    // A: OrderID
      order.customerPhone,              // B: SoDienThoai  
      order.customerName,               // C: TenKhachHang
      order.orderDate,                  // D: NgayDatHang
      order.subtotal,                   // E: TongTienSanPham
      order.shippingCost,               // F: PhiVanChuyen
      order.appliedPromotion,           // G: MaGiamGiaDaApDung
      order.discountAmount,             // H: SoTienGiamGiaTuKM
      order.total,                      // I: ThanhTien
      order.paymentMethod,              // J: PhuongThucThanhToan
      order.status,                     // K: TrangThaiDonHang
      order.notes,                      // L: GhiChu
      
      // Additional fields for extended order management
      order.customerEmail,              // M: Email
      order.companyName,                // N: TenCongTy
      order.taxId,                      // O: MaSoThue
      order.shippingProvince,           // P: TinhThanhPho
      order.shippingDistrict,           // Q: QuanHuyen
      order.shippingAddress,            // R: DiaChiCuThe
      order.itemCount,                  // S: SoLuongSanPham
      order.source,                     // T: NguonDonHang
      order.createdAt,                  // U: NgayTao
      order.updatedAt                   // V: NgayCapNhat
    ];
    
    ordersSheet.appendRow(rowData);
    
    Logger.log('Order saved to sheet:', order.orderId);
    return createSuccessResponse('Order saved successfully');
    
  } catch (error) {
    Logger.log('Save order to sheet error:', error);
    return createErrorResponse('Failed to save order to sheet: ' + error.message);
  }
}

/**
 * Save order details to OrderDetails sheet
 * @param {string} orderId - Order ID
 * @param {Array} items - Order items
 * @return {Object} Save result
 */
function saveOrderDetails(orderId, items) {
  try {
    const detailsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('OrderDetails');
    
    items.forEach(item => {
      const rowData = [
        orderId,                        // A: OrderID
        item.sku,                       // B: ProductID (SKU)
        item.productName,               // C: TenSanPham
        item.quantity,                  // D: SoLuong
        item.price,                     // E: DonGiaLucMua
        item.totalPrice,                // F: ThanhTien
        item.variant || '',             // G: GhiChu (variant info)
        
        // Additional fields
        item.thumbnail || '',           // H: HinhAnh
        new Date().toISOString()        // I: NgayThem
      ];
      
      detailsSheet.appendRow(rowData);
    });
    
    Logger.log('Order details saved:', orderId, items.length + ' items');
    return createSuccessResponse('Order details saved successfully');
    
  } catch (error) {
    Logger.log('Save order details error:', error);
    return createErrorResponse('Failed to save order details: ' + error.message);
  }
}

/**
 * Update customer record in Customers sheet
 * @param {Object} customerData - Customer information
 * @param {Object} order - Order information
 */
function updateCustomerRecord(customerData, order) {
  try {
    const customersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Customers');
    const data = customersSheet.getDataRange().getValues();
    const headers = data[0];
    
    // Find existing customer by phone
    let customerRowIndex = -1;
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === customerData.phone) { // SoDienThoai column
        customerRowIndex = i;
        break;
      }
    }
    
    if (customerRowIndex > 0) {
      // Update existing customer
      const row = data[customerRowIndex];
      
      // Update basic info
      customersSheet.getRange(customerRowIndex + 1, 2).setValue(customerData.name); // TenKhachHang
      if (customerData.email) {
        customersSheet.getRange(customerRowIndex + 1, 3).setValue(customerData.email); // Email
      }
      
      // Update order statistics
      const currentOrderCount = parseInt(row[7]) || 0; // TongSoDonHang
      const currentTotalSpent = parseFloat(row[8]) || 0; // TongChiTieu
      
      customersSheet.getRange(customerRowIndex + 1, 8).setValue(currentOrderCount + 1); // TongSoDonHang
      customersSheet.getRange(customerRowIndex + 1, 9).setValue(currentTotalSpent + order.total); // TongChiTieu
      customersSheet.getRange(customerRowIndex + 1, 10).setValue((currentTotalSpent + order.total) / (currentOrderCount + 1)); // GiaTriDonTrungBinh
      customersSheet.getRange(customerRowIndex + 1, 12).setValue(new Date()); // NgayMuaCuoiCung
      customersSheet.getRange(customerRowIndex + 1, 13).setValue(order.shippingAddress); // DiaChiGiaoHang
      
    } else {
      // Create new customer record
      const newCustomerData = [
        customerData.phone,              // A: SoDienThoai
        customerData.name,               // B: TenKhachHang  
        customerData.email || '',        // C: Email
        '',                              // D: ViDo
        '',                              // E: KinhDo
        0,                               // F: DiemTichLuy
        'Thành viên mới',                // G: HangThanhVien
        1,                               // H: TongSoDonHang
        order.total,                     // I: TongChiTieu
        order.total,                     // J: GiaTriDonTrungBinh
        new Date(),                      // K: NgayMuaDauTien
        new Date(),                      // L: NgayMuaCuoiCung
        order.shippingAddress,           // M: DiaChiGiaoHang
        '',                              // N: GhiChu
        '',                              // O: PasswordHash
        '',                              // P: Salt
        new Date().toISOString(),        // Q: JoinedAt
        '',                              // R: LastLogin
        false,                           // S: IsBlocked
        order.companyName || '',         // T: TenDonVi
        order.taxId || '',               // U: MaSoThue
        order.companyAddress || '',      // V: DiaChiDonVi
        '',                              // W: CCCD
        ''                               // X: NgaySinh
      ];
      
      customersSheet.appendRow(newCustomerData);
    }
    
    Logger.log('Customer record updated:', customerData.phone);
    
  } catch (error) {
    Logger.log('Update customer record error:', error);
  }
}

// ============================================================================
// ORDER STATUS MANAGEMENT
// ============================================================================

/**
 * Update order status
 * @param {string} orderId - Order ID
 * @param {string} newStatus - New status
 * @param {string} note - Status change note
 * @return {Object} Update result
 */
function updateOrderStatus(orderId, newStatus, note = '') {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    
    // Find order row
    let orderRowIndex = -1;
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        orderRowIndex = i;
        break;
      }
    }
    
    if (orderRowIndex === -1) {
      return createErrorResponse('Không tìm thấy đơn hàng');
    }
    
    // Update status
    ordersSheet.getRange(orderRowIndex + 1, 11).setValue(newStatus); // TrangThaiDonHang column
    ordersSheet.getRange(orderRowIndex + 1, 22).setValue(new Date().toISOString()); // NgayCapNhat column
    
    // Add note if provided
    if (note) {
      const currentNotes = data[orderRowIndex][11] || ''; // GhiChu column
      const updatedNotes = currentNotes + '\n' + new Date().toLocaleString('vi-VN') + ': ' + note;
      ordersSheet.getRange(orderRowIndex + 1, 12).setValue(updatedNotes);
    }
    
    // Log status change
    logOrderStatusChange(orderId, newStatus, note);
    
    // Send status update email if customer has email
    const customerEmail = data[orderRowIndex][12]; // Email column
    if (customerEmail) {
      sendStatusUpdateEmail(orderId, newStatus, customerEmail);
    }
    
    Logger.log('Order status updated:', orderId, newStatus);
    return createSuccessResponse('Cập nhật trạng thái thành công');
    
  } catch (error) {
    Logger.log('Update order status error:', error);
    return createErrorResponse('Có lỗi khi cập nhật trạng thái đơn hàng');
  }
}

/**
 * Get order by ID
 * @param {string} orderId - Order ID
 * @return {Object} Order data or null
 */
function getOrderById(orderId) {
  try {
    const ordersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Orders');
    const data = ordersSheet.getDataRange().getValues();
    const headers = data[0];
    
    // Find order row
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        const row = data[i];
        
        return {
          orderId: row[0],
          customerPhone: row[1],
          customerName: row[2],
          orderDate: row[3],
          subtotal: row[4],
          shippingCost: row[5],
          appliedPromotion: row[6],
          discountAmount: row[7],
          total: row[8],
          paymentMethod: row[9],
          status: row[10],
          notes: row[11],
          customerEmail: row[12],
          companyName: row[13],
          taxId: row[14],
          shippingProvince: row[15],
          shippingDistrict: row[16],
          shippingAddress: row[17],
          itemCount: row[18],
          source: row[19],
          createdAt: row[20],
          updatedAt: row[21]
        };
      }
    }
    
    return null;
    
  } catch (error) {
    Logger.log('Get order by ID error:', error);
    return null;
  }
}

/**
 * Get order details by order ID
 * @param {string} orderId - Order ID
 * @return {Array} Order details
 */
function getOrderDetails(orderId) {
  try {
    const detailsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('OrderDetails');
    const data = detailsSheet.getDataRange().getValues();
    const details = [];
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === orderId) {
        details.push({
          orderId: data[i][0],
          sku: data[i][1],
          productName: data[i][2],
          quantity: data[i][3],
          unitPrice: data[i][4],
          totalPrice: data[i][5],
          notes: data[i][6],
          thumbnail: data[i][7],
          createdAt: data[i][8]
        });
      }
    }
    
    return details;
    
  } catch (error) {
    Logger.log('Get order details error:', error);
    return [];
  }
}

/**
 * Log order status changes for audit trail
 * @param {string} orderId - Order ID
 * @param {string} status - New status
 * @param {string} note - Status change note
 */
function logOrderStatusChange(orderId, status, note) {
  try {
    // This could be implemented with a separate audit log sheet
    // For now, just log to the Apps Script log
    Logger.log(`Order ${orderId}: Status changed to ${status}. Note: ${note}`);
  } catch (error) {
    Logger.log('Log status change error:', error);
  }
}

// ============================================================================
// EXTENDED API ENDPOINTS FOR ORDER MANAGEMENT
// ============================================================================

/**
 * Extended API handler for order management
 * @param {Object} e - Request parameters
 * @return {Object} API response
 */
function handleOrderAPI(e) {
  const action = e.parameter.action;
  
  switch (action) {
    case 'create_order':
      const orderData = JSON.parse(e.parameter.orderData || '{}');
      return createNewOrder(orderData);
      
    case 'get_order_status':
      const orderId = e.parameter.orderId;
      const order = getOrderById(orderId);
      if (order) {
        const details = getOrderDetails(orderId);
        return createSuccessResponse({
          order: order,
          details: details
        });
      } else {
        return createErrorResponse('Không tìm thấy đơn hàng');
      }
      
    case 'update_order_status':
      const updateOrderId = e.parameter.orderId;
      const newStatus = e.parameter.status;
      const note = e.parameter.note || '';
      return updateOrderStatus(updateOrderId, newStatus, note);
      
    case 'vnpay_callback':
      return handleVNPayCallback(e.parameter);
      
    default:
      return createErrorResponse('Invalid order action');
  }
}

/**
 * Test order creation functionality
 */
function testOrderCreation() {
  const testOrderData = {
    customer: {
      name: 'Nguyễn Văn Test',
      phone: '0987654321',
      email: '<EMAIL>'
    },
    company: {
      name: '',
      taxId: '',
      address: ''
    },
    shippingAddress: {
      province: 'Hồ Chí Minh',
      district: 'Quận 1',
      address: '123 Nguyễn Huệ, Phường Bến Nghé'
    },
    paymentMethod: 'cod',
    items: [
      {
        sku: 'VTC099BNTRON3.8LHT',
        productName: 'Bình Ngâm Thủy Tinh',
        variant: '3.8 lít SVQT',
        price: 380000,
        quantity: 2,
        totalPrice: 760000,
        thumbnail: ''
      }
    ],
    subtotal: 760000,
    shippingCost: 30000,
    discountAmount: 0,
    total: 790000,
    notes: 'Test order',
    appliedPromotion: null
  };
  
  const result = createNewOrder(testOrderData);
  Logger.log('Test order creation result:', result);
  
  return result;
}
/**
 * ============================================================================
 * LOTUS GLASS - SYSTEM VERIFICATION TEST SCRIPT
 * File: lotus-system-test.gs
 * 
 * Chạy các functions này trong Google Apps Script để verify system hoạt động đúng
 * ============================================================================
 */

// ============================================================================
// MAIN VERIFICATION FUNCTION
// ============================================================================

/**
 * Main function to run all system tests
 * Run this to verify complete system functionality
 */
function runFullSystemTest() {
  Logger.log('🚀 Starting Lotus Glass System Verification...');
  
  const results = {
    database: false,
    orderCreation: false,
    emailSystem: false,
    apiEndpoints: false,
    paymentSystem: false,
    overall: false
  };
  
  try {
    // Test 1: Database Connection
    results.database = testDatabaseConnection();
    
    // Test 2: Order Creation
    results.orderCreation = testOrderCreation();
    
    // Test 3: Email System
    results.emailSystem = testEmailSystem();
    
    // Test 4: API Endpoints
    results.apiEndpoints = testAPIEndpoints();
    
    // Test 5: Payment System
    results.paymentSystem = testPaymentSystem();
    
    // Overall result
    results.overall = Object.values(results).every(test => test);
    
    // Print results
    printTestResults(results);
    
    return results;
    
  } catch (error) {
    Logger.log('❌ System test failed:', error);
    return results;
  }
}

// ============================================================================
// DATABASE TESTS
// ============================================================================

/**
 * Test Google Sheets database connection and structure
 */
function testDatabaseConnection() {
  Logger.log('📊 Testing database connection...');
  
  try {
    // Test spreadsheet access
    const ss = SpreadsheetApp.openById(SPREADSHEET_ID);
    if (!ss) {
      Logger.log('❌ Cannot access spreadsheet');
      return false;
    }
    
    // Test required sheets exist
    const requiredSheets = ['Orders', 'OrderDetails', 'Customers'];
    for (const sheetName of requiredSheets) {
      const sheet = ss.getSheetByName(sheetName);
      if (!sheet) {
        Logger.log(`❌ Sheet '${sheetName}' not found`);
        return false;
      }
      Logger.log(`✅ Sheet '${sheetName}' found`);
    }
    
    // Test Orders sheet structure
    const ordersSheet = ss.getSheetByName('Orders');
    const headers = ordersSheet.getRange(1, 1, 1, 12).getValues()[0];
    const requiredHeaders = ['OrderID', 'SoDienThoai', 'TenKhachHang', 'NgayDatHang'];
    
    for (const header of requiredHeaders) {
      if (!headers.includes(header)) {
        Logger.log(`❌ Required header '${header}' not found in Orders sheet`);
        return false;
      }
    }
    
    Logger.log('✅ Database connection and structure verified');
    return true;
    
  } catch (error) {
    Logger.log('❌ Database test failed:', error);
    return false;
  }
}

// ============================================================================
// ORDER CREATION TESTS
// ============================================================================

/**
 * Test order creation functionality
 */
function testOrderCreation() {
  Logger.log('📝 Testing order creation...');
  
  try {
    // Create test order data
    const testOrder = {
      customerInfo: {
        phone: '0123456789',
        name: 'Test Customer',
        email: '<EMAIL>',
        address: 'Test Address, Ho Chi Minh City'
      },
      cartItems: [
        {
          productId: 'TEST001',
          name: 'Test Product',
          quantity: 2,
          price: 100000
        }
      ],
      shippingFee: 30000,
      paymentMethod: 'cod',
      notes: 'Test order from verification script'
    };
    
    // Test order ID generation
    const orderId = generateOrderId();
    if (!orderId || orderId.length < 8) {
      Logger.log('❌ Order ID generation failed');
      return false;
    }
    Logger.log('✅ Order ID generated:', orderId);
    
    // Test order creation
    const result = createOrder(testOrder);
    if (!result || !result.orderId) {
      Logger.log('❌ Order creation failed');
      return false;
    }
    Logger.log('✅ Test order created:', result.orderId);
    
    // Verify order in database
    const ss = SpreadsheetApp.openById(SPREADSHEET_ID);
    const ordersSheet = ss.getSheetByName('Orders');
    const orders = ordersSheet.getDataRange().getValues();
    
    const testOrderInDb = orders.find(row => row[0] === result.orderId);
    if (!testOrderInDb) {
      Logger.log('❌ Test order not found in database');
      return false;
    }
    Logger.log('✅ Test order verified in database');
    
    // Clean up test data
    cleanupTestOrder(result.orderId);
    
    return true;
    
  } catch (error) {
    Logger.log('❌ Order creation test failed:', error);
    return false;
  }
}

// ============================================================================
// EMAIL SYSTEM TESTS
// ============================================================================

/**
 * Test email system functionality
 */
function testEmailSystem() {
  Logger.log('📧 Testing email system...');
  
  try {
    // Test Gmail access
    const quota = GmailApp.getRemainingDailyQuota();
    if (quota === 0) {
      Logger.log('❌ Gmail quota exhausted');
      return false;
    }
    Logger.log('✅ Gmail access verified, remaining quota:', quota);
    
    // Test simple email send
    const testEmail = '<EMAIL>';
    const subject = 'Lotus Glass System Test';
    const body = 'This is a test email from the verification script.';
    
    try {
      GmailApp.sendEmail(testEmail, subject, body);
      Logger.log('✅ Test email sent successfully');
    } catch (emailError) {
      Logger.log('⚠️ Email send test failed (permissions may be needed):', emailError);
      // Don't fail the test - permissions might not be granted yet
    }
    
    // Test email template generation
    const testOrder = {
      orderId: 'TEST123',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      totalAmount: 200000,
      paymentMethod: 'cod'
    };
    
    const emailTemplate = getOrderConfirmationEmailTemplate(testOrder);
    if (!emailTemplate || emailTemplate.length < 100) {
      Logger.log('❌ Email template generation failed');
      return false;
    }
    Logger.log('✅ Email template generation working');
    
    return true;
    
  } catch (error) {
    Logger.log('❌ Email system test failed:', error);
    return false;
  }
}

// ============================================================================
// API ENDPOINT TESTS
// ============================================================================

/**
 * Test API endpoint functionality
 */
function testAPIEndpoints() {
  Logger.log('🔌 Testing API endpoints...');
  
  try {
    // Test doPost function exists
    if (typeof doPost !== 'function') {
      Logger.log('❌ doPost function not found');
      return false;
    }
    
    // Test cart validation endpoint
    const cartTestData = {
      parameter: {
        action: 'validate_cart',
        cart_data: JSON.stringify([{
          productId: 'TEST001',
          quantity: 1,
          price: 100000
        }])
      }
    };
    
    try {
      const cartResult = doPost(cartTestData);
      Logger.log('✅ Cart validation endpoint working');
    } catch (error) {
      Logger.log('⚠️ Cart validation test had issues:', error);
    }
    
    // Test order creation endpoint
    const orderTestData = {
      parameter: {
        action: 'create_order',
        customer_info: JSON.stringify({
          phone: '0123456789',
          name: 'Test Customer',
          email: '<EMAIL>'
        }),
        cart_items: JSON.stringify([{
          productId: 'TEST001',
          name: 'Test Product',
          quantity: 1,
          price: 100000
        }]),
        payment_method: 'cod'
      }
    };
    
    try {
      const orderResult = doPost(orderTestData);
      Logger.log('✅ Order creation endpoint working');
      
      // Clean up test order if created
      if (orderResult && orderResult.getContentText) {
        const response = JSON.parse(orderResult.getContentText());
        if (response.orderId) {
          cleanupTestOrder(response.orderId);
        }
      }
    } catch (error) {
      Logger.log('⚠️ Order creation endpoint test had issues:', error);
    }
    
    return true;
    
  } catch (error) {
    Logger.log('❌ API endpoint test failed:', error);
    return false;
  }
}

// ============================================================================
// PAYMENT SYSTEM TESTS
// ============================================================================

/**
 * Test payment system functionality
 */
function testPaymentSystem() {
  Logger.log('💳 Testing payment system...');
  
  try {
    // Test VNPay configuration
    if (!ORDER_CONFIG.VNPAY.TMN_CODE || ORDER_CONFIG.VNPAY.TMN_CODE === 'YOUR_TMN_CODE') {
      Logger.log('⚠️ VNPay not configured (sandbox mode)');
    } else {
      Logger.log('✅ VNPay configuration found');
    }
    
    // Test payment URL generation
    const testPaymentData = {
      orderId: 'TEST123',
      amount: 100000,
      customerInfo: {
        name: 'Test Customer',
        phone: '0123456789'
      }
    };
    
    try {
      const paymentUrl = generateVNPayURL(testPaymentData);
      if (paymentUrl && paymentUrl.startsWith('http')) {
        Logger.log('✅ VNPay URL generation working');
      } else {
        Logger.log('⚠️ VNPay URL generation needs configuration');
      }
    } catch (error) {
      Logger.log('⚠️ VNPay test had issues:', error);
    }
    
    // Test COD calculation
    const codFee = calculateCODFee(100000);
    if (typeof codFee === 'number' && codFee >= 0) {
      Logger.log('✅ COD fee calculation working:', codFee);
    } else {
      Logger.log('❌ COD fee calculation failed');
      return false;
    }
    
    return true;
    
  } catch (error) {
    Logger.log('❌ Payment system test failed:', error);
    return false;
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Print comprehensive test results
 */
function printTestResults(results) {
  Logger.log('\n============================================================================');
  Logger.log('🏆 LOTUS GLASS SYSTEM VERIFICATION RESULTS');
  Logger.log('============================================================================');
  
  Logger.log(`📊 Database Connection: ${results.database ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`📝 Order Creation: ${results.orderCreation ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`📧 Email System: ${results.emailSystem ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`🔌 API Endpoints: ${results.apiEndpoints ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`💳 Payment System: ${results.paymentSystem ? '✅ PASS' : '❌ FAIL'}`);
  
  Logger.log('----------------------------------------------------------------------------');
  
  if (results.overall) {
    Logger.log('🎉 OVERALL STATUS: ✅ SYSTEM READY FOR PRODUCTION');
    Logger.log('🚀 Your Lotus Glass e-commerce platform is fully functional!');
    Logger.log('💼 Ready to start accepting orders and generating revenue.');
  } else {
    Logger.log('⚠️ OVERALL STATUS: ❌ SYSTEM NEEDS ATTENTION');
    Logger.log('🔧 Please address the failed tests before going live.');
    Logger.log('📋 Check the implementation guide for troubleshooting steps.');
  }
  
  Logger.log('============================================================================\n');
}

/**
 * Clean up test order data
 */
function cleanupTestOrder(orderId) {
  try {
    const ss = SpreadsheetApp.openById(SPREADSHEET_ID);
    
    // Remove from Orders sheet
    const ordersSheet = ss.getSheetByName('Orders');
    const ordersData = ordersSheet.getDataRange().getValues();
    
    for (let i = ordersData.length - 1; i >= 0; i--) {
      if (ordersData[i][0] === orderId) {
        ordersSheet.deleteRow(i + 1);
        break;
      }
    }
    
    // Remove from OrderDetails sheet
    const detailsSheet = ss.getSheetByName('OrderDetails');
    const detailsData = detailsSheet.getDataRange().getValues();
    
    for (let i = detailsData.length - 1; i >= 0; i--) {
      if (detailsData[i][0] === orderId) {
        detailsSheet.deleteRow(i + 1);
      }
    }
    
    Logger.log('🧹 Test order cleaned up:', orderId);
    
  } catch (error) {
    Logger.log('⚠️ Cleanup failed for order:', orderId, error);
  }
}

// ============================================================================
// QUICK INDIVIDUAL TESTS
// ============================================================================

/**
 * Quick test functions for individual components
 */

function quickTestDatabase() {
  return testDatabaseConnection();
}

function quickTestEmail() {
  return testEmailSystem();
}

function quickTestOrder() {
  return testOrderCreation();
}

function quickTestAPI() {
  return testAPIEndpoints();
}

function quickTestPayment() {
  return testPaymentSystem();
}

// ============================================================================
// PERFORMANCE TESTS
// ============================================================================

/**
 * Test system performance
 */
function testSystemPerformance() {
  Logger.log('⚡ Testing system performance...');
  
  const startTime = new Date();
  
  // Test multiple order creations
  const testResults = [];
  for (let i = 0; i < 5; i++) {
    const testStart = new Date();
    
    const testOrder = {
      customerInfo: {
        phone: `012345${i}789`,
        name: `Test Customer ${i}`,
        email: `test${i}@example.com`,
        address: 'Test Address'
      },
      cartItems: [{
        productId: `TEST00${i}`,
        name: `Test Product ${i}`,
        quantity: 1,
        price: 100000
      }],
      paymentMethod: 'cod'
    };
    
    try {
      const result = createOrder(testOrder);
      const testEnd = new Date();
      const duration = testEnd - testStart;
      
      testResults.push({
        orderId: result.orderId,
        duration: duration,
        success: true
      });
      
      // Clean up
      cleanupTestOrder(result.orderId);
      
    } catch (error) {
      testResults.push({
        error: error.message,
        success: false
      });
    }
  }
  
  const endTime = new Date();
  const totalDuration = endTime - startTime;
  
  Logger.log('Performance test results:');
  Logger.log(`Total time: ${totalDuration}ms`);
  Logger.log(`Average per order: ${totalDuration / 5}ms`);
  Logger.log(`Success rate: ${testResults.filter(r => r.success).length}/5`);
  
  return testResults;
}

/**
 * Main entry point - run this function to verify your system
 */
function verifyLotusGlassSystem() {
  return runFullSystemTest();
}
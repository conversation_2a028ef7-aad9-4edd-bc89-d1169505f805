/**
 * ============================================================================
 * LOTUS GLASS - GOOG<PERSON> APPS SCRIPT BACKEND EXTENSION
 * File: lotus-cart-backend.gs
 * 
 * Thêm vào existing code.gs để support shopping cart operations
 * ============================================================================
 */

// ============================================================================
// CART CONFIGURATION
// ============================================================================

const CART_CONFIG = {
  // Validation settings
  MAX_QUANTITY_PER_ITEM: 999,
  MIN_QUANTITY_PER_ITEM: 1,
  MAX_CART_ITEMS: 50,
  
  // Price validation tolerance (for floating point comparison)
  PRICE_TOLERANCE: 0.01,
  
  // Cache settings
  CACHE_DURATION: 300, // 5 minutes
  
  // Error messages
  ERRORS: {
    INVALID_SKU: 'Mã sản phẩm không hợp lệ',
    INVALID_QUANTITY: 'Số lượng không hợp lệ',
    INVALID_PRICE: 'Giá sản phẩm không hợp lệ',
    PRODUCT_NOT_FOUND: 'Không tìm thấy sản phẩm',
    OUT_OF_STOCK: 'Sản phẩm hết hàng',
    CART_TOO_LARGE: 'Giỏ hàng có quá nhiều sản phẩm',
    INVALID_CART_DATA: 'Dữ liệu giỏ hàng không hợp lệ'
  }
};

// ============================================================================
// CART VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate cart items against current product database
 * @param {Array} cartItems - Array of cart items to validate
 * @return {Object} Validation result with updated prices and availability
 */
function validateCartItems(cartItems) {
  try {
    const startTime = new Date();
    
    // Input validation
    if (!Array.isArray(cartItems)) {
      return createErrorResponse(CART_CONFIG.ERRORS.INVALID_CART_DATA);
    }
    
    if (cartItems.length > CART_CONFIG.MAX_CART_ITEMS) {
      return createErrorResponse(CART_CONFIG.ERRORS.CART_TOO_LARGE);
    }
    
    const validatedItems = [];
    const errors = [];
    const warnings = [];
    
    // Get product database
    const productsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Products');
    const productData = getProductDatabase(productsSheet);
    
    for (const item of cartItems) {
      const validation = validateSingleCartItem(item, productData);
      
      if (validation.isValid) {
        validatedItems.push(validation.item);
      } else {
        errors.push({
          sku: item.sku,
          error: validation.error
        });
      }
      
      if (validation.warnings) {
        warnings.push(...validation.warnings);
      }
    }
    
    const processingTime = new Date() - startTime;
    
    return createSuccessResponse({
      validatedItems,
      errors,
      warnings,
      summary: {
        totalItems: validatedItems.length,
        totalValue: validatedItems.reduce((sum, item) => sum + item.totalPrice, 0),
        hasErrors: errors.length > 0,
        hasWarnings: warnings.length > 0
      },
      metadata: {
        processingTime,
        validatedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    Logger.log('Cart validation error:', error);
    return createErrorResponse('Có lỗi xảy ra khi xác thực giỏ hàng');
  }
}

/**
 * Validate a single cart item
 * @param {Object} item - Cart item to validate
 * @param {Object} productData - Product database
 * @return {Object} Validation result
 */
function validateSingleCartItem(item, productData) {
  const warnings = [];
  
  // Basic structure validation
  if (!item.sku || !item.productName || !item.quantity || !item.price) {
    return {
      isValid: false,
      error: CART_CONFIG.ERRORS.INVALID_CART_DATA
    };
  }
  
  // Find product in database
  const product = productData[item.sku];
  if (!product) {
    return {
      isValid: false,
      error: CART_CONFIG.ERRORS.PRODUCT_NOT_FOUND
    };
  }
  
  // Validate quantity
  const quantity = parseInt(item.quantity);
  if (isNaN(quantity) || quantity < CART_CONFIG.MIN_QUANTITY_PER_ITEM || quantity > CART_CONFIG.MAX_QUANTITY_PER_ITEM) {
    return {
      isValid: false,
      error: CART_CONFIG.ERRORS.INVALID_QUANTITY
    };
  }
  
  // Check stock availability
  if (product.stock !== undefined && product.stock < quantity) {
    return {
      isValid: false,
      error: CART_CONFIG.ERRORS.OUT_OF_STOCK
    };
  }
  
  // Validate price (allow for small floating point differences)
  const priceDifference = Math.abs(parseFloat(item.price) - parseFloat(product.price));
  if (priceDifference > CART_CONFIG.PRICE_TOLERANCE) {
    warnings.push({
      type: 'price_changed',
      message: `Giá sản phẩm ${item.productName} đã thay đổi`,
      oldPrice: item.price,
      newPrice: product.price
    });
  }
  
  // Create validated item with current data
  const validatedItem = {
    sku: item.sku,
    productName: product.name || item.productName,
    variant: item.variant || product.variant || '',
    price: parseFloat(product.price),
    quantity: quantity,
    totalPrice: parseFloat(product.price) * quantity,
    thumbnail: product.thumbnail || item.thumbnail || '',
    availability: product.stock > 0 ? 'in_stock' : 'limited_stock',
    stock: product.stock || 0,
    validatedAt: new Date().toISOString()
  };
  
  return {
    isValid: true,
    item: validatedItem,
    warnings: warnings.length > 0 ? warnings : null
  };
}

/**
 * Get product database from sheet with caching
 * @param {Sheet} sheet - Products sheet
 * @return {Object} Product database indexed by SKU
 */
function getProductDatabase(sheet) {
  const cacheKey = 'product_database';
  const cache = CacheService.getScriptCache();
  
  // Try to get from cache
  const cached = cache.get(cacheKey);
  if (cached) {
    try {
      return JSON.parse(cached);
    } catch (e) {
      Logger.log('Cache parse error:', e);
    }
  }
  
  // Build database from sheet
  const data = sheet.getDataRange().getValues();
  const headers = data[0];
  const productData = {};
  
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    const sku = row[0]; // Assuming SKU is in first column
    
    if (sku) {
      productData[sku] = {
        sku: sku,
        name: row[1] || '', // Product name
        variant: row[2] || '', // Variant
        price: parseFloat(row[3]) || 0, // Price
        stock: parseInt(row[4]) || 0, // Stock quantity
        thumbnail: row[5] || '', // Image URL
        category: row[6] || '', // Category
        isActive: row[7] !== false, // Active status
        lastUpdated: new Date().toISOString()
      };
    }
  }
  
  // Cache for next time
  try {
    cache.put(cacheKey, JSON.stringify(productData), CART_CONFIG.CACHE_DURATION);
  } catch (e) {
    Logger.log('Cache save error:', e);
  }
  
  return productData;
}

// ============================================================================
// PROMOTION & DISCOUNT FUNCTIONS
// ============================================================================

/**
 * Apply promotion codes to cart
 * @param {Array} cartItems - Cart items
 * @param {string} promotionCode - Promotion code to apply
 * @return {Object} Updated cart with applied discounts
 */
function applyPromotionToCart(cartItems, promotionCode) {
  try {
    if (!promotionCode || !Array.isArray(cartItems)) {
      return createErrorResponse('Dữ liệu không hợp lệ');
    }
    
    // Get promotion details
    const promotion = getPromotionByCode(promotionCode);
    if (!promotion) {
      return createErrorResponse('Mã giảm giá không hợp lệ hoặc đã hết hạn');
    }
    
    // Check promotion validity
    const now = new Date();
    if (promotion.startDate && new Date(promotion.startDate) > now) {
      return createErrorResponse('Mã giảm giá chưa có hiệu lực');
    }
    
    if (promotion.endDate && new Date(promotion.endDate) < now) {
      return createErrorResponse('Mã giảm giá đã hết hạn');
    }
    
    // Calculate discount
    const cartTotal = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    const discountResult = calculateDiscount(cartItems, cartTotal, promotion);
    
    if (!discountResult.isValid) {
      return createErrorResponse(discountResult.error);
    }
    
    return createSuccessResponse({
      originalTotal: cartTotal,
      discountAmount: discountResult.discountAmount,
      finalTotal: cartTotal - discountResult.discountAmount,
      appliedPromotion: {
        code: promotion.code,
        name: promotion.name,
        type: promotion.type,
        value: promotion.value
      },
      updatedItems: discountResult.updatedItems || cartItems
    });
    
  } catch (error) {
    Logger.log('Apply promotion error:', error);
    return createErrorResponse('Có lỗi xảy ra khi áp dụng mã giảm giá');
  }
}

/**
 * Get promotion by code
 * @param {string} code - Promotion code
 * @return {Object|null} Promotion details or null if not found
 */
function getPromotionByCode(code) {
  try {
    const promotionsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Promotions');
    if (!promotionsSheet) return null;
    
    const data = promotionsSheet.getDataRange().getValues();
    const headers = data[0];
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (row[0] && row[0].toString().toUpperCase() === code.toUpperCase()) {
        return {
          code: row[0],
          name: row[1] || '',
          type: row[2] || 'percentage', // percentage, fixed, free_shipping
          value: parseFloat(row[3]) || 0,
          minOrderValue: parseFloat(row[4]) || 0,
          maxDiscountAmount: parseFloat(row[5]) || 0,
          startDate: row[6] ? new Date(row[6]) : null,
          endDate: row[7] ? new Date(row[7]) : null,
          isActive: row[8] !== false,
          usageLimit: parseInt(row[9]) || 0,
          usageCount: parseInt(row[10]) || 0
        };
      }
    }
    
    return null;
  } catch (error) {
    Logger.log('Get promotion error:', error);
    return null;
  }
}

/**
 * Calculate discount amount based on promotion
 * @param {Array} cartItems - Cart items
 * @param {number} cartTotal - Cart total value
 * @param {Object} promotion - Promotion details
 * @return {Object} Discount calculation result
 */
function calculateDiscount(cartItems, cartTotal, promotion) {
  // Check minimum order value
  if (promotion.minOrderValue && cartTotal < promotion.minOrderValue) {
    return {
      isValid: false,
      error: `Đơn hàng tối thiểu ${formatCurrency(promotion.minOrderValue)} để áp dụng mã này`
    };
  }
  
  let discountAmount = 0;
  
  switch (promotion.type) {
    case 'percentage':
      discountAmount = cartTotal * (promotion.value / 100);
      break;
      
    case 'fixed':
      discountAmount = promotion.value;
      break;
      
    case 'free_shipping':
      // This would be handled in shipping calculation
      discountAmount = 0;
      break;
      
    default:
      return {
        isValid: false,
        error: 'Loại khuyến mãi không hợp lệ'
      };
  }
  
  // Apply maximum discount limit
  if (promotion.maxDiscountAmount && discountAmount > promotion.maxDiscountAmount) {
    discountAmount = promotion.maxDiscountAmount;
  }
  
  // Ensure discount doesn't exceed cart total
  discountAmount = Math.min(discountAmount, cartTotal);
  
  return {
    isValid: true,
    discountAmount: discountAmount,
    type: promotion.type
  };
}

// ============================================================================
// SHIPPING CALCULATION
// ============================================================================

/**
 * Calculate shipping cost based on cart and address
 * @param {Array} cartItems - Cart items
 * @param {Object} shippingAddress - Shipping address details
 * @return {Object} Shipping calculation result
 */
function calculateShippingCost(cartItems, shippingAddress) {
  try {
    if (!Array.isArray(cartItems) || !shippingAddress) {
      return createErrorResponse('Dữ liệu không hợp lệ');
    }
    
    const cartTotal = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    const totalWeight = cartItems.reduce((sum, item) => sum + (item.weight || 0.5) * item.quantity, 0);
    
    // Free shipping thresholds
    const FREE_SHIPPING_THRESHOLD = 500000; // 500k VND
    if (cartTotal >= FREE_SHIPPING_THRESHOLD) {
      return createSuccessResponse({
        shippingCost: 0,
        shippingMethod: 'standard',
        estimatedDays: '3-5',
        reason: 'Miễn phí vận chuyển cho đơn hàng trên 500,000₫'
      });
    }
    
    // Calculate based on province/region
    const shippingCost = calculateShippingByRegion(shippingAddress.province, totalWeight);
    
    return createSuccessResponse({
      shippingCost: shippingCost,
      shippingMethod: 'standard',
      estimatedDays: getEstimatedDeliveryDays(shippingAddress.province),
      breakdown: {
        baseRate: shippingCost,
        weightSurcharge: 0,
        regionSurcharge: 0
      }
    });
    
  } catch (error) {
    Logger.log('Shipping calculation error:', error);
    return createErrorResponse('Có lỗi xảy ra khi tính phí vận chuyển');
  }
}

/**
 * Calculate shipping cost by region
 * @param {string} province - Province name
 * @param {number} weight - Total package weight
 * @return {number} Shipping cost
 */
function calculateShippingByRegion(province, weight) {
  const SHIPPING_RATES = {
    // Major cities - lower rates
    'Hồ Chí Minh': 30000,
    'Hà Nội': 35000,
    'Đà Nẵng': 40000,
    'Cần Thơ': 35000,
    
    // Nearby provinces - standard rates
    'Bình Dương': 35000,
    'Đồng Nai': 35000,
    'Long An': 40000,
    'Tây Ninh': 45000,
    'Bà Rịa - Vũng Tàu': 40000,
    
    // Remote areas - higher rates
    'default': 50000
  };
  
  const baseRate = SHIPPING_RATES[province] || SHIPPING_RATES['default'];
  
  // Add weight surcharge for heavy packages
  const weightSurcharge = weight > 5 ? (weight - 5) * 5000 : 0;
  
  return baseRate + weightSurcharge;
}

/**
 * Get estimated delivery days by province
 * @param {string} province - Province name
 * @return {string} Estimated delivery days
 */
function getEstimatedDeliveryDays(province) {
  const DELIVERY_TIMES = {
    'Hồ Chí Minh': '1-2',
    'Hà Nội': '2-3',
    'Đà Nẵng': '2-3',
    'Cần Thơ': '2-3',
    'default': '3-5'
  };
  
  return DELIVERY_TIMES[province] || DELIVERY_TIMES['default'];
}

// ============================================================================
// INVENTORY MANAGEMENT
// ============================================================================

/**
 * Reserve stock for checkout
 * @param {Array} cartItems - Items to reserve
 * @return {Object} Reservation result
 */
function reserveCartStock(cartItems) {
  try {
    const reservations = [];
    const errors = [];
    
    const productsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Products');
    const data = productsSheet.getDataRange().getValues();
    
    for (const item of cartItems) {
      const reservation = reserveSingleItemStock(item, productsSheet, data);
      
      if (reservation.success) {
        reservations.push(reservation);
      } else {
        errors.push({
          sku: item.sku,
          error: reservation.error
        });
      }
    }
    
    if (errors.length > 0) {
      // Rollback successful reservations
      rollbackReservations(reservations);
      
      return createErrorResponse({
        message: 'Không thể đặt trước một số sản phẩm',
        errors: errors
      });
    }
    
    return createSuccessResponse({
      reservations: reservations,
      reservationId: generateReservationId(),
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutes
    });
    
  } catch (error) {
    Logger.log('Stock reservation error:', error);
    return createErrorResponse('Có lỗi xảy ra khi đặt trước sản phẩm');
  }
}

/**
 * Generate unique reservation ID
 * @return {string} Reservation ID
 */
function generateReservationId() {
  return 'RES_' + new Date().getTime() + '_' + Math.random().toString(36).substr(2, 9);
}

// ============================================================================
// EXTENDED API ENDPOINTS
// ============================================================================

/**
 * Extended doPost function to handle cart operations
 * Thêm vào existing doPost function trong code.gs
 */
function handleCartAPI(e) {
  const action = e.parameter.action;
  
  switch (action) {
    case 'validate_cart':
      const cartData = JSON.parse(e.parameter.cartData || '[]');
      return validateCartItems(cartData);
      
    case 'apply_promotion':
      const items = JSON.parse(e.parameter.items || '[]');
      const promoCode = e.parameter.promotionCode;
      return applyPromotionToCart(items, promoCode);
      
    case 'calculate_shipping':
      const cartItems = JSON.parse(e.parameter.cartItems || '[]');
      const address = JSON.parse(e.parameter.shippingAddress || '{}');
      return calculateShippingCost(cartItems, address);
      
    case 'reserve_stock':
      const reserveItems = JSON.parse(e.parameter.items || '[]');
      return reserveCartStock(reserveItems);
      
    case 'get_promotions':
      return getAvailablePromotions();
      
    default:
      return createErrorResponse('Invalid cart action');
  }
}

/**
 * Get available promotions
 * @return {Object} Available promotions
 */
function getAvailablePromotions() {
  try {
    const promotionsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('Promotions');
    if (!promotionsSheet) {
      return createSuccessResponse({ promotions: [] });
    }
    
    const data = promotionsSheet.getDataRange().getValues();
    const headers = data[0];
    const promotions = [];
    const now = new Date();
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      
      // Check if promotion is active and current
      const isActive = row[8] !== false;
      const startDate = row[6] ? new Date(row[6]) : null;
      const endDate = row[7] ? new Date(row[7]) : null;
      
      if (isActive && 
          (!startDate || startDate <= now) && 
          (!endDate || endDate >= now)) {
        
        promotions.push({
          code: row[0],
          name: row[1],
          description: row[11] || '',
          type: row[2],
          value: row[3],
          minOrderValue: row[4] || 0,
          endDate: endDate ? endDate.toISOString() : null
        });
      }
    }
    
    return createSuccessResponse({ promotions });
    
  } catch (error) {
    Logger.log('Get promotions error:', error);
    return createErrorResponse('Có lỗi khi lấy danh sách khuyến mãi');
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Format currency for Vietnamese Dong
 * @param {number} amount - Amount to format
 * @return {string} Formatted currency string
 */
function formatCurrency(amount) {
  if (!amount || isNaN(amount)) return '0₫';
  return new Intl.NumberFormat('vi-VN').format(amount) + '₫';
}

/**
 * Create standardized success response
 * @param {*} data - Response data
 * @return {Object} Success response object
 */
function createSuccessResponse(data) {
  return {
    success: true,
    data: data,
    timestamp: new Date().toISOString()
  };
}

/**
 * Create standardized error response
 * @param {string} message - Error message
 * @return {Object} Error response object
 */
function createErrorResponse(message) {
  return {
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  };
}

// ============================================================================
// TESTING FUNCTIONS
// ============================================================================

/**
 * Test cart validation functionality
 */
function testCartValidation() {
  const testCart = [
    {
      sku: 'VTC099BNTRON3.8LHT',
      productName: 'Bình Ngâm Thủy Tinh',
      variant: '3.8 lít SVQT',
      price: 380000,
      quantity: 2
    }
  ];
  
  const result = validateCartItems(testCart);
  Logger.log('Cart validation test result:', result);
  
  return result;
}

/**
 * Test shipping calculation
 */
function testShippingCalculation() {
  const testCart = [
    {
      sku: 'VTC099BNTRON3.8LHT',
      productName: 'Bình Ngâm Thủy Tinh',
      totalPrice: 760000,
      weight: 2.5,
      quantity: 2
    }
  ];
  
  const testAddress = {
    province: 'Hồ Chí Minh',
    district: 'Quận 1'
  };
  
  const result = calculateShippingCost(testCart, testAddress);
  Logger.log('Shipping calculation test result:', result);
  
  return result;
}

// ============================================================================
// INTEGRATION INSTRUCTIONS
// ============================================================================

/*
INTEGRATION STEPS:

1. Mở existing code.gs file
2. Thêm toàn bộ code này vào cuối file
3. Update doPost function để handle cart actions:

function doPost(e) {
  try {
    const action = e.parameter.action;
    
    // Existing actions...
    
    // Add cart handling
    if (['validate_cart', 'apply_promotion', 'calculate_shipping', 'reserve_stock', 'get_promotions'].includes(action)) {
      return ContentService
        .createTextOutput(JSON.stringify(handleCartAPI(e)))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Default response
    return ContentService
      .createTextOutput(JSON.stringify(createErrorResponse('Invalid action')))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    Logger.log('API Error:', error);
    return ContentService
      .createTextOutput(JSON.stringify(createErrorResponse('Server error')))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

4. Update SPREADSHEET_ID constant với actual spreadsheet ID
5. Test API endpoints using testCartValidation() và testShippingCalculation()
*/